#!/bin/bash
#
# 引擎容器启动脚本
#

set -e

# 设置编码和环境变量
export LANG=en_US.UTF-8
export LC_ALL=en_US.UTF-8
export PYTHONIOENCODING=utf-8
export PYTHONPATH=/opt/msim:${PYTHONPATH}
export CLIENT_TYPE=engine
export ROS_HOSTNAME=localhost
export ROS_MASTER_URI=http://localhost:11311

# 加载ROS环境
source /opt/ros/noetic/setup.bash

# 使用纯shell方式检查manual模式（与run_all.sh保持一致）
check_local_mode() {
    local tasks_info_file="$1"
    
    if [ ! -f "$tasks_info_file" ]; then
        echo "false"
        return
    fi
    
    # 使用grep和sed提取manual字段值
    # 匹配 "manual": true/false 或 "manual": "true"/"false"
    local_line=$(grep -o '"manual"[[:space:]]*:[[:space:]]*[^,}]*' "$tasks_info_file" 2>/dev/null | head -1)
    
    if [ -z "$local_line" ]; then
        echo "false"
        return
    fi
    
    # 提取值部分（冒号后的内容）
    local_value=$(echo "$local_line" | sed 's/^"manual"[[:space:]]*:[[:space:]]*//' | sed 's/[", ]//g')
    
    # 转换为小写进行比较
    local_value_lower=$(echo "$local_value" | tr '[:upper:]' '[:lower:]')
    
    if [ "$local_value_lower" = "true" ]; then
        echo "true"
    else
        echo "false"
    fi
}

# 检查是否为manual模式
TASKS_INFO_FILE="/home/<USER>/workspace/tasks_info.json"
IS_LOCAL=$(check_local_mode "$TASKS_INFO_FILE")

# 根据模式决定是否启动ROS master
if [ "$IS_LOCAL" = "false" ]; then
    echo "容器模式：启动ROS master..."
    # 启动ROS master
    roscore > /home/<USER>/workspace/logs/roscore.log 2>&1 &
    ROS_MASTER_PID=$!
    
    # 等待ROS master启动
    sleep 3
    if ! rostopic list > /dev/null 2>&1; then
        kill $ROS_MASTER_PID
        exit 1
    fi
    echo "ROS master启动成功"
else
    echo "本地模式：跳过ROS master启动"
    ROS_MASTER_PID=""
fi

# 执行Python命令
cd /opt/msim/apps
python3 client.py engine &
ENGINE_PID=$!

# 等待Python进程结束
wait $ENGINE_PID

# 清理ROS master（仅在容器模式下）
if [ "$IS_LOCAL" = "false" ] && [ -n "$ROS_MASTER_PID" ]; then
    echo "清理ROS master..."
    kill $ROS_MASTER_PID
fi