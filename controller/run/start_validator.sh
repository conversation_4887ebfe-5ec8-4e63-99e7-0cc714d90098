#!/bin/bash
#
# 评价容器启动脚本
#

set -e

# 设置编码和环境变量
export LANG=en_US.UTF-8
export LC_ALL=en_US.UTF-8
export PYTHONIOENCODING=utf-8
export PYTHONPATH=/opt/msim:${PYTHONPATH}
export CLIENT_TYPE=validator
export ROS_MASTER_URI=http://localhost:11311
export ROS_HOSTNAME=localhost

# 加载ROS环境
source /opt/ros/noetic/setup.bash

# 等待ROS master启动
i=0
while [ $i -lt 10 ]; do
    if rostopic list > /dev/null 2>&1; then
        break
    fi
    sleep 1
    i=$((i+1))
done
if [ $i -eq 10 ]; then  
    exit 1
fi

# 执行Python命令
cd /opt/msim/apps
python3 client.py validator &
VALIDATOR_PID=$!

# 等待进程结束
wait $VALIDATOR_PID