#!/usr/bin/env bash

# error codes
# 0 - exited without problems
# 1 - parameters not supported were used or some unexpected error occurred
# 2 - OS not supported by this script
# 3 - installed version of ossutil is up to date
# 4 - supported unzip tools are not availabale

set -e

usage() { echo "Usage: sudo -v ; curl https://udata-production-hz.oss-cn-hangzhou-internal.aliyuncs.com/PUBLIC/ULOGUTIL/install.sh | sudo bash" 1>&2; exit 1; }

#detect the platform
file_suffix=""
OS="$(uname)"
case $OS in
  Linux)
    OS='linux'
    file_suffix='linux'
    ;;
  Darwin)
    OS='mac'
    binTgtDir=/usr/local/bin
    file_suffix='macos'
    ;;
  *)
    echo 'OS not supported'
    exit 2
    ;;
esac

OS_type="$(uname -m)"
case "$OS_type" in
  x86_64|amd64)
    OS_type='amd64'
    ;;
  i?86|x86)
    OS_type='386'
    ;;
  aarch64|arm64)
    OS_type='arm64'
    ;;
  arm*)
    OS_type='arm'
    ;;
  *)
    echo 'OS type not supported'
    exit 2
    ;;
esac
file_suffix="$file_suffix-$OS_type"

set +e
# download and install ossutil
sudo -v ; curl https://gosspublic.alicdn.com/ossutil/install.sh | sudo bash
set -e

# download and install mcap
mcapName="mcap-$file_suffix"
sudo wget "https://udata-production-hz.oss-cn-hangzhou-internal.aliyuncs.com/PUBLIC/MCAP_TOOL/0.0.38/$mcapName" -O /usr/local/bin/mcap
sudo chmod +x /usr/local/bin/mcap


# download and install urecordutil
sudo wget "https://udata-production-hz.oss-cn-hangzhou-internal.aliyuncs.com/PUBLIC/ULOGUTIL/CURRENT/ulogutil.py" -O /usr/local/bin/ulogutil
sudo chmod 755 /usr/local/bin/ulogutil

#update version variable post install
version=$(ulogutil --version)

printf "\n${version} has successfully installed.\n"
exit 0