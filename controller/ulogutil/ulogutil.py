#!/usr/bin/env python3
# -*- coding: UTF-8 -*-

#  Copyright (C) Mogo.ai Information and Technology Co.Ltd 
#
#  All rights reserved.
#
#  This document contains proprietary information belonging to Mogo.ai Co.Ltd.
#  Passing on and copying of this document, and communication of its contents
#  is not permitted without prior written authorization.
#
#
#  <AUTHOR>
#  @date 11/8/23, 11:01 AM
from __future__ import annotations

import argparse
import glob
import json
import logging
import os
import re
import shutil
import subprocess
import sys
import tempfile
import threading
import time
from pathlib import Path
from typing import List, Tuple, Set

VERSION = 'v0.0.1'
TEMP_DIR = tempfile.mkdtemp()
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s[%(levelname)s] - %(message)s')
logger = logging.getLogger()


class ReturnCode:
    SUCCESS = 0
    INTERNAL_FAILED = 1
    DATA_NOT_FOUND = 2
    INPUT_PARAM_ERROR = 3


def execute_shell(cmd: str) -> <PERSON><PERSON>[int, str]:
    proc = subprocess.Popen(cmd, shell=True, stderr=subprocess.PIPE,
                            stdout=subprocess.PIPE, executable='/bin/bash')
    out, err = proc.communicate()
    return proc.returncode, out.decode().strip() + err.decode().strip()


def cleanup():
    try:
        shutil.rmtree(TEMP_DIR)
    except Exception as e:
        logger.warning(f"remove temp dir {TEMP_DIR} failed: {str(e)}")


class Spinner:
    def __init__(self):
        self.running = False
        self.msg = ''
        self.spinner_thread = None
        self.spinner_chars = ['-', '\\', '|', '/']

    def start(self, msg="Processing"):
        if self.running:
            return
        self.msg = msg
        self.running = True
        self.spinner_thread = threading.Thread(target=self._spin)
        self.spinner_thread.start()

    def stop(self):
        self.running = False
        if self.spinner_thread:
            self.spinner_thread.join()
            self.spinner_thread = None

    def _spin(self):
        while self.running:
            for char in self.spinner_chars:
                sys.stdout.write('\r' + f'{self.msg}... {char}')
                sys.stdout.flush()
                time.sleep(0.1)

        sys.stdout.write('\r')
        sys.stdout.flush()


spinner = Spinner()


class OSSUtil:
    OSS_BASE_CMD = 'ossutil'

    @staticmethod
    def config(end_point=None, key_id=None, key_secret=None, sts_token=None, config_path=None):
        if config_path:
            OSSUtil.OSS_BASE_CMD += f" -c {config_path}"

        config_cmd = f"{OSSUtil.OSS_BASE_CMD} config -L CH"
        if end_point:
            end_point = end_point.strip()
            config_cmd += f" -e {end_point}"
        if key_id:
            key_id = key_id.strip()
            config_cmd += f" -i {key_id}"
        if key_secret:
            key_secret = key_secret.strip()
            config_cmd += f" -k {key_secret}"
        if sts_token:
            sts_token = sts_token.strip()
            config_cmd += f" -t {sts_token}"
        code, msg = execute_shell(config_cmd)
        if code != 0:
            raise RuntimeError(f"config oss failed: {msg}")

        else:
            logger.info("Config oss success")

    @staticmethod
    def ls(url) -> Tuple[int, List[str]]:
        cp_cmd = f"{OSSUtil.OSS_BASE_CMD} ls \'{url}\' -s"
        code, msg = execute_shell(cp_cmd)
        if code != 0:
            return code, []
        lines = msg.split('\n')
        if len(lines) <= 3:
            return 0, []
        else:
            return 0, lines[:-3]

    @staticmethod
    def cp_dir(url: str, output: str, include=None, exclude: str = None):
        url = url.strip('/') + '/'
        output = output.rstrip('/') + '/'
        cp_cmd = f"{OSSUtil.OSS_BASE_CMD} cp \'{url}\' \'{output}\' -r --only-current-dir -f"
        if exclude:
            cp_cmd += f" --exclude {exclude}"
        code, msg = execute_shell(cp_cmd)
        if code != 0:
            raise RuntimeError(f"cp dir failed: {msg}")

    @staticmethod
    def cp_single(url: str, output: str):
        output = output.rstrip('/') + '/'
        cp_cmd = f"{OSSUtil.OSS_BASE_CMD} cp \'{url}\' \'{output}\' -f"
        code, msg = execute_shell(cp_cmd)
        if code != 0:
            raise RuntimeError(f"cp file failed: {msg}")


class McapUtil:

    @staticmethod
    def merge(input_path: str, output_path: str):
        files = glob.glob(f"{input_path}/**/*.mcap", recursive=True)
        quoted_files = ' '.join([f"\'{file}\'" for file in files])
        if not files:
            raise ValueError(f"the input path has no mcap file, please check the url or task id")
        merge_cmd = f"mcap merge {quoted_files} -o \'{output_path}\' --allow-duplicate-metadata"
        code, msg = execute_shell(merge_cmd)
        if code != 0:
            raise RuntimeError(f"merge mcap failed: {msg}")

    @staticmethod
    def merge_files(mcap_files: List[str], output_path: str):
        quoted_files = ' '.join([f"\'{file}\'" for file in mcap_files])
        merge_cmd = f"mcap merge {quoted_files} -o \'{output_path}\' --allow-duplicate-metadata"
        code, msg = execute_shell(merge_cmd)
        if code != 0:
            raise RuntimeError(f"merge mcap failed: {msg}")

    @staticmethod
    def filter(input_path: str, output_path: str, start_time=None, end_time=None):
        if not start_time and not end_time:
            if not os.path.exists(os.path.dirname(output_path)):
                os.makedirs(os.path.dirname(output_path))
            shutil.move(input_path, output_path)
            return
        filter_cmd = f"mcap filter \'{input_path}\' -o \'{output_path}\'"
        if start_time:
            filter_cmd += f" -s {start_time}"
        if end_time:
            filter_cmd += f" -e {end_time}"
        code, msg = execute_shell(filter_cmd)
        if code != 0:
            raise RuntimeError(f"filter mcap failed: {msg}")


class ChainHandler(object):

    def __init__(self, next_handler=None):
        self._next_handler = next_handler

    def handle_args(self, args) -> int:
        try:
            res = self.handle(args)
        except Exception as e:
            logger.error(f"handle failed: {e}")
            res = ReturnCode.INTERNAL_FAILED

        if res:
            self.rollback()
        else:
            if self._next_handler:
                res = self._next_handler.handle_args(args)
        return res

    def handle(self, args) -> int:
        raise NotImplementedError

    def rollback(self):
        pass


class CheckHandler(ChainHandler):

    def handle(self, args) -> int:
        return CheckHandler._check_args(args)

    @staticmethod
    def _check_args(args) -> int:
        try:
            CheckHandler._check_target(args)
            CheckHandler._check_path(args)
        except Exception as e:
            logger.error(f"Check args failed: {e}")
            return ReturnCode.INPUT_PARAM_ERROR
        return ReturnCode.SUCCESS

    @staticmethod
    def _check_target(args):
        if args.task and args.urls:
            raise ValueError("the task and urls can not be set at the same time")

        if args.urls:
            urls = args.urls.split(',')
            if any(list(map(lambda x: not x.startswith('oss://'), urls))):
                raise ValueError(f"the url must be oss url, but got {args.urls}")
            args.urls = urls

        if args.task:
            task_ids = set(args.task.split(','))
            if any(list(map(lambda x: not x.isdigit(), task_ids))):
                raise ValueError(f"task id must be digit, but got {args.task}")
            args.task = task_ids

        if args.filter:
            if args.task:
                raise ValueError("the task and filter can not be set at the same time")
            se_time = args.filter.split(':')
            if len(se_time) != 2 or any(list(map(lambda x: x and not x.isdigit(), se_time))) or not se_time[1]:
                raise ValueError(f"the filter time must be digit and must have end time, but got {args.filter}")
            args.start_time = se_time[0] or None
            args.end_time = se_time[1] or None
        else:
            args.start_time = None
            args.end_time = None

        if args.topics:
            args.topics = args.topics.split(',')

    @staticmethod
    def _check_path(args):
        args.output = os.path.realpath(args.output)
        args.filename = None
        if os.path.isfile(args.output):
            raise ValueError(f"the output path {args.output} is a file, please check it")
        elif '.mcap' in args.output:
            filename = os.path.basename(args.output)
            dirname = os.path.dirname(args.output)
            args.output = dirname
            args.filename = filename

        if not os.path.exists(args.output):
            raise ValueError(f"the output path {args.output} is not exists, please check it")

        if not os.path.exists(TEMP_DIR):
            os.makedirs(TEMP_DIR)

        if args.json_path:
            if os.path.exists(args.json_path):
                os.remove(args.json_path)
            if not os.path.exists(os.path.dirname(args.json_path)):
                try:
                    os.makedirs(os.path.dirname(args.json_path))
                except Exception as e:
                    raise RuntimeError(f"create json path->{args.json_path} failed: {e}")


class ConfigHandler(ChainHandler):

    def handle(self, args) -> int:
        if args.config is not None:
            return self._init_config(args)
        return self._check()

    @staticmethod
    def _init_config(args):
        if not args.config:
            end_point = input("Please input the endpoint of the oss: \n").strip()
            key_id = input("Please input accessKeyID of the oss: \n").strip()
            key_secret = input("Please input accessKeySecret of the oss: \n").strip()
            sts_token = input("Please input STS token of the oss: \n").strip()
            config_path = None
        else:
            try:
                config = json.loads(args.config)
                end_point = config.get('endpoint')
                key_id = config.get('accessKeyID')
                key_secret = config.get('accessKeySecret')
                sts_token = config.get('stsToken')
                config_path = os.path.join(TEMP_DIR, '.ossutilconfig')
            except Exception as e:
                logger.error(f"parse config failed: {e}")
                return ReturnCode.INPUT_PARAM_ERROR
        OSSUtil.config(end_point, key_id, key_secret, sts_token, config_path=config_path)
        return ReturnCode.SUCCESS

    @staticmethod
    def _check():
        """
        check ossutil config
        """
        # TODO: check ossutil config
        return ReturnCode.SUCCESS


class DownloadHandler(ChainHandler):
    _sim_task_url = "oss://usim-production-hz/logs/simulating/{}/artifacts/record/"
    _mcap_info = {"mcapTopics": []}

    def handle(self, args) -> int:
        try:
            self._download(self._gen_jobs(args), args.topics, args.exclude, bool(args.json_path))
            self._gen_info_json(args)
            return ReturnCode.SUCCESS
        except FileExistsError as e:
            logger.error(f"download failed: {e}")
            return ReturnCode.DATA_NOT_FOUND
        except ValueError as e:
            logger.error(f"download failed: {e}")
            return ReturnCode.INPUT_PARAM_ERROR
        except Exception as e:
            logger.error(f"download failed: {e}")
            return ReturnCode.INTERNAL_FAILED
        finally:
            spinner.stop()

    def _download(self, download_jobs: List[Tuple[str, str]], topics=None, exclude=None, gen_json=False):

        def _download_target_topics(dir_url, _path, _topics):
            for topic in _topics:
                prefix = '' if topic.startswith('/') else '_'
                file_url = f"{dir_url.strip('/')}/{prefix}{topic.replace('/', '_')}.mcap"
                try:
                    OSSUtil.cp_single(file_url, _path)
                except Exception as e:
                    logger.warning(f"{file_url} download failed: {e}")
                    continue

        spinner.start('Begin download')
        for url, output_path in download_jobs:
            ls_res, ls_files = OSSUtil.ls(url)
            if ls_res:
                raise ValueError(f"list oss path {url} failed, please check oss auth")

            if not ls_files:
                raise FileExistsError(f"the url {url} has no mcap file, please check it")

            if not topics:
                OSSUtil.cp_dir(url, output_path, exclude=exclude)
            else:
                _download_target_topics(url, output_path, topics)
            if gen_json:
                self._add_info_json(output_path, url)
        spinner.stop()

    @staticmethod
    def _gen_jobs(args) -> List[Tuple[str, str]]:

        def _gen_task_jobs(task_ids: Set[str]) -> List[Tuple[str, str]]:
            return list(map(lambda x: (DownloadHandler._sim_task_url.format(str(x)),
                                       os.path.join(TEMP_DIR, str(x))), task_ids))

        def _gen_url_jobs(urls: List[str]) -> List[Tuple[str, str]]:
            return list(map(lambda x: (x, os.path.join(TEMP_DIR, 'union', os.path.basename(x.strip('/')))), urls))

        if args.task:
            return _gen_task_jobs(args.task)
        if args.urls:
            return _gen_url_jobs(args.urls)
        return []

    def _add_info_json(self, mcap_dir: str, oss_path: str) -> None:
        # warning: this method is not complete, only use in usim controller env
        # TODO: use mcap cmd to do this, now only in usim controller env
        try:
            from utils.mcap_tools.reader import get_summary
        except ImportError:
            return

        def strip_oss(url: str) -> str:
            return re.sub(r'^oss://[^/]+/', '', url)

        for topic_mcap_file in os.listdir(mcap_dir):
            file_path = os.path.join(mcap_dir, topic_mcap_file)
            summary = get_summary(file_path)
            statistics = summary.statistics
            channel_id, channel_info = list(summary.channels.items())[0]

            topic_info = {
                "filename": topic_mcap_file,
                "size": os.path.getsize(file_path),
                "topic": channel_info.topic,
                "is_compress": False,
                "message_count": statistics.channel_message_counts.get(channel_id, 0) if statistics else 0,
                # warning: use raw start&end, not after filter
                "start": statistics.message_start_time if statistics else None,
                "end": statistics.message_end_time if statistics else None,
                "path": os.path.join(strip_oss(oss_path), topic_mcap_file)
            }
            self._mcap_info["mcapTopics"].append(topic_info)

    def _gen_info_json(self, args) -> None:
        if not args.json_path:
            return

        with open(args.json_path, 'w') as f:
            f.write(json.dumps(self._mcap_info))


class CoverHandler(ChainHandler):
    def handle(self, args) -> int:
        if not args.convert_h26x:
            return ReturnCode.SUCCESS

        try:
            from utils.h26x import convert_h26x_mcap

        except ImportError as e:
            logger.error(f"import h26x failed: {e}")
            return ReturnCode.INTERNAL_FAILED

        if args.urls:
            mcap_files = list(Path(os.path.join(TEMP_DIR, 'union')).rglob("*h26x.mcap"))
            if not mcap_files:
                return ReturnCode.SUCCESS

            convert_path = Path(os.path.join(TEMP_DIR, 'union')).joinpath('convert')
            convert_path.mkdir(parents=True, exist_ok=True)
            h26x_merge_path = convert_path.joinpath('h26x_merge.mcap')
            McapUtil.merge_files([str(file) for file in mcap_files], str(h26x_merge_path))
            try:
                convert_h26x_mcap(str(h26x_merge_path), str(convert_path), overwrite=True)
                h26x_merge_path.unlink(missing_ok=True)
            except ValueError as e:
                logger.warning(f"convert h26x failed: {e}")
                pass
            except Exception as e:
                logger.error(f"convert h26x failed: {e}")
                return ReturnCode.INTERNAL_FAILED

            for file in mcap_files:
                file.unlink(missing_ok=True)

        return ReturnCode.SUCCESS


class MergeHandler(ChainHandler):

    def handle(self, args) -> int:
        try:
            if args.urls:
                filename = args.filename if args.filename else 'union.mcap'
                McapUtil.merge(os.path.join(TEMP_DIR, 'union'), filename)
                logger.info(f"Generate mcap at {os.path.join(args.output, filename)}")
            elif args.task:
                task_dir = list(filter(lambda x: os.path.isdir(os.path.join(TEMP_DIR, x)), os.listdir(TEMP_DIR)))
                for dir_name in task_dir:
                    out_put = os.path.join(args.output, f"{dir_name}.mcap")
                    McapUtil.merge(os.path.join(TEMP_DIR, str(dir_name)), os.path.join(TEMP_DIR, f"{dir_name}.mcap"))
                    McapUtil.filter(os.path.join(TEMP_DIR, f"{dir_name}.mcap"), out_put, args.start_time, args.end_time)
                    logger.info(f"Generate task {dir_name} mcap at {out_put}")
            return ReturnCode.SUCCESS
        except ValueError as e:
            logger.error(f"merge failed: {e}")
            return ReturnCode.DATA_NOT_FOUND
        except Exception as e:
            logger.error(f"merge failed: {e}")
            return ReturnCode.INTERNAL_FAILED


class FilterHandler(ChainHandler):

    def handle(self, args) -> int:
        try:
            if not args.filter or args.task:
                return ReturnCode.SUCCESS

            mcap_files = list(Path(os.path.join(TEMP_DIR, 'union')).rglob("*.mcap"))
            from concurrent.futures import ProcessPoolExecutor
            with ProcessPoolExecutor(max_workers=2) as executor:
                futures = []
                for mcap in mcap_files:
                    dir_path = mcap.parent
                    dir_name = os.path.basename(str(dir_path))
                    filter_name = f"{dir_name}_filter"
                    filter_path = dir_path.parent.joinpath(filter_name)
                    if not filter_path.exists():
                        filter_path.mkdir(parents=True, exist_ok=True)
                    target_filter_path = filter_path.joinpath(os.path.basename(str(mcap)))
                    futures.append(executor.submit(McapUtil.filter, str(mcap), str(target_filter_path), args.start_time, args.end_time))
                for future in futures:
                    future.result()
                for mcap in mcap_files:
                    mcap.unlink(missing_ok=True)

            return ReturnCode.SUCCESS
        except Exception as e:
            logger.error(f"filter failed: {e}")
            return ReturnCode.INTERNAL_FAILED


def main():
    parser = argparse.ArgumentParser(formatter_class=argparse.RawTextHelpFormatter)
    parser.add_argument('-v', '--version', action='version', version=VERSION, help='show version')
    parser.add_argument('-o', '--output', default="./", help='output path, default: current dir, mcap file will be generated at {output}/union.mcap or task_id.mcap')
    parser.add_argument('-u', '--urls', help='the urls of the record files, example: -u url1,url2')
    parser.add_argument('-c', '--config', nargs='?', const='', default=None,
                        help='init config, 交互式(-c)|非交互式(-c {json}), example: -c, -c \'{"accessKeyID": "xxx", "accessKeySecret": "xxx", "endpoint": "xxx", "stsToken": "xxx}\'')
    parser.add_argument('--task', help='the task of the record files, usage: -t {task_id}, example: -t 1')
    parser.add_argument('--topics', help='filter the topic name, example: topic1,topic2')
    parser.add_argument('--filter', help='filter the merge mcap by timestamp, example: 1000:1001 or :1001')
    parser.add_argument('--exclude', help='exclude the filename, example, --exclude *.txt')
    parser.add_argument('--tmp-path', help='the tmp file path to save the mcap before merge, default: random tmp dir')
    parser.add_argument('--json-path', help='the path of mcap info json file generated by data platform')
    parser.add_argument('--convert-h26x', action='store_true', help='convert h26x topic to image_raw topic')
    arguments = parser.parse_args()

    if arguments.tmp_path:
        global TEMP_DIR
        cleanup()
        TEMP_DIR = arguments.tmp_path

    try:
        res = CheckHandler(ConfigHandler(DownloadHandler(FilterHandler(CoverHandler(MergeHandler()))))).handle_args(arguments)
    except Exception as e:
        logger.error(f"Failed: {e}")
        res = ReturnCode.INTERNAL_FAILED
    finally:
        cleanup()

    return int(res)


if __name__ == "__main__":
    sys.exit(main())
