# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

import usimtask_pb2 as usimtask__pb2


class UdeerSimStub(object):
  # missing associated documentation comment in .proto file
  pass

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.SimStream = channel.stream_stream(
        '/udeer_sim.UdeerSim/SimStream',
        request_serializer=usimtask__pb2.Request.SerializeToString,
        response_deserializer=usimtask__pb2.Response.FromString,
        )


class UdeerSimServicer(object):
  # missing associated documentation comment in .proto file
  pass

  def SimStream(self, request_iterator, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_UdeerSimServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'SimStream': grpc.stream_stream_rpc_method_handler(
          servicer.SimStream,
          request_deserializer=usimtask__pb2.Request.FromString,
          response_serializer=usimtask__pb2.Response.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'udeer_sim.UdeerSim', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
