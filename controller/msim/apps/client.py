import os
import sys
import grpc
import argparse
import json
import codecs
import time
import threading
import uuid
import subprocess
from typing import Iterator, Optional, Dict, Any
import importlib
import traceback
import usimtask_pb2
import usimtask_pb2_grpc
from utils.loggers import get_client_logger
import config.myglobal as myglobal
import rospy

# Set environment variables
os.environ['PYTHONIOENCODING'] = 'utf-8'

# Force UTF-8 encoding
sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer)
sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer)

# 获取客户端日志记录器
logger = get_client_logger()

# 命令类型常量
CMD_TASK_INIT = "task_init"
CMD_TASK_RUN = "task_run"
CMD_TASK_END = "task_end"
CMD_TASK_CLEAN = "task_clean"
CMD_SHELL = "shell"  # 用于执行shell命令

# 命令处理函数类型
CommandHandler = Dict[str, callable]

# 全局客户端实例，方便外部调用
global_client = None

def get_client() -> Optional['SimpleUsimClient']:
    """获取全局客户端实例"""
    return global_client

def send_to_server(message: Dict[str, Any]) -> bool:
    """便捷函数：向服务器发送消息"""
    client = get_client()
    if client and client.is_connected():
        return client.send_message(message)
    else:
        logger.error("客户端未连接或未初始化，无法发送消息")
        return False

class SimpleUsimClient:
    def __init__(self, channel: grpc.Channel, client_type: str) -> None:
        """Initialize gRPC client"""
        self._channel = channel
        self._stub = usimtask_pb2_grpc.UdeerSimStub(self._channel)
        self._client_type = client_type
        self._running = False
        self._connected = False
        self._request_queue = []  # 请求队列
        self._send_lock = threading.Lock()  # 发送锁，确保线程安全
        self._command_handlers = {}  # 命令处理函数映射
        logger.info(f"客户端初始化 - 类型: {client_type}")

    def register_command_handler(self, command: str, handler: callable):
        """注册命令处理函数
        
        Args:
            command: 命令名称
            handler: 处理函数，接收一个参数(data)
        """
        self._command_handlers[command] = handler
        logger.info(f"已注册命令处理函数: {command}")

    def is_connected(self) -> bool:
        """检查客户端是否已连接到服务器"""
        return self._connected

    def send_message(self, message: Dict[str, Any]) -> bool:
        """向服务器发送消息
        
        Args:
            message: 要发送的消息字典
            
        Returns:
            bool: 消息是否成功加入发送队列
        """
        if not self._connected:
            logger.warning("客户端未连接，无法发送消息")
            return False
            
        try:
            # 确保消息有基本字段
            if "client_type" not in message:
                message["client_type"] = self._client_type
            if "timestamp" not in message:
                message["timestamp"] = time.time()
            if "id" not in message:
                message["id"] = str(uuid.uuid4())
                
            # 线程安全地添加到请求队列
            with self._send_lock:
                self._request_queue.append(message)
                
            logger.info(f"[{self._client_type}] 消息已加入发送队列: {message}")
            return True
        except Exception as e:
            logger.error(f"[{self._client_type}] 准备发送消息时出错: {str(e)}")
            return False

    def _generate_requests(self):
        """生成客户端请求的生成器"""
        # 发送初始连接请求
        init_data = {
            "type": "connect",
            "client_type": self._client_type,
            "message": f"{self._client_type} client requesting connection",
            "timestamp": time.time(),
            "id": str(uuid.uuid4())
        }
        init_request = usimtask_pb2.Request()
        init_request.input = json.dumps(init_data)
        logger.info(f"[{self._client_type}] Sending initial connection request")
        yield init_request
        
        # 持续运行，发送队列中的消息
        while self._running:
            # 检查队列中是否有消息要发送
            with self._send_lock:
                if self._request_queue:
                    message = self._request_queue.pop(0)
                    request = usimtask_pb2.Request()
                    request.input = json.dumps(message)
                    logger.info(f"[{self._client_type}] 发送消息: {message}")
                    yield request
            
            # 短暂休眠，避免CPU占用过高
            time.sleep(0.05)

    def _handle_response(self, response_iterator: Iterator[usimtask_pb2.Response]) -> None:
        """处理服务器响应"""
        try:
            # 处理第一个响应（连接确认）
            first_response = next(response_iterator)
            try:
                response_data = json.loads(first_response.output)
                if response_data.get("status") == "connected":
                    self._connected = True
                    logger.info(f"[{self._client_type}] 已成功连接到服务器: {response_data.get('message')}")
                else:
                    logger.warning(f"[{self._client_type}] 连接响应异常: {response_data}")
            except json.JSONDecodeError:
                logger.warning(f"[{self._client_type}] 初始响应格式无效: {first_response.output}")
            
            # 处理后续响应
            for response in response_iterator:
                try:
                    # 解析响应数据
                    command_data = json.loads(response.output)
                    
                    # 检查是否包含命令
                    if "cmd" in command_data:
                        cmd = command_data["cmd"]
                        data = command_data.get("data", {})
                        cmd_id = command_data.get("cmd_id")
                        logger.info(f"[{self._client_type}] 收到命令: {cmd} cmd_id={cmd_id}")
                        
                        # 处理命令
                        if cmd in self._command_handlers:
                            try:
                                data["cmd_id"] = cmd_id
                                self._command_handlers[cmd](data)
                            except Exception as e:
                                logger.error(f"[{self._client_type}] 执行命令 {cmd} 时出错: {str(e)}")
                        else:
                            logger.warning(f"[{self._client_type}] 未注册的命令: {cmd}")
                    else:
                        logger.warning(f"[{self._client_type}] 收到无命令字段的消息: {command_data}")
                        
                except json.JSONDecodeError:
                    logger.warning(f"[{self._client_type}] 响应不是有效的JSON格式: {response.output}")
        except StopIteration:
            logger.info(f"[{self._client_type}] 响应流结束")
        except Exception as e:
            logger.error(f"[{self._client_type}] 处理响应时出错: {str(e)}")
        finally:
            self._connected = False
            self._running = False

    def start_stream(self) -> None:
        """启动与服务器的流通信"""
        try:
            self._running = True
            logger.info(f"[{self._client_type}] 开始与服务器的流通信")
            
            # 创建请求生成器并开始流通信
            request_iterator = self._generate_requests()
            response_iterator = self._stub.SimStream(request_iterator)
            
            logger.info(f"[{self._client_type}] 开始接收服务器响应流")
            self._handle_response(response_iterator)
            
        except grpc.RpcError as e:
            status_code = e.code()
            logger.error(f"[{self._client_type}] gRPC错误: {status_code.name} ({status_code.value})")
            logger.error(f"[{self._client_type}] 详情: {e.details()}")
            self._running = False
            self._connected = False
        except Exception as e:
            logger.error(f"[{self._client_type}] 流通信错误: {str(e)}")
            self._running = False
            self._connected = False
            raise e
        finally:
            logger.info(f"[{self._client_type}] 流通信结束")
            self._running = False
            self._connected = False

def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='gRPC客户端启动器')
    parser.add_argument('client_type', type=str, help='客户端类型 (如: engine, validator)')
    return parser.parse_args()

# 执行Shell命令函数
def execute_shell_command(command, working_dir=None):
    """执行Shell命令并返回结果
    
    Args:
        command: 要执行的shell命令
        working_dir: 执行命令的工作目录（可选）
        
    Returns:
        dict: 包含stdout, stderr和exit_code的字典
    """
    try:
        logger.info(f"执行Shell命令: {command}")
        if working_dir:
            logger.info(f"工作目录: {working_dir}")
            
        # 创建进程，执行命令
        process = subprocess.Popen(
            command,
            shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True,
            cwd=working_dir
        )
        
        # 获取输出
        stdout, stderr = process.communicate()
        exit_code = process.returncode
        
        # 记录执行结果
        if exit_code == 0:
            logger.info(f"命令执行成功，退出码: {exit_code}")
        else:
            logger.warning(f"命令执行失败，退出码: {exit_code}")
            if stderr:
                logger.warning(f"错误输出: {stderr}")
                
        return {
            "stdout": stdout,
            "stderr": stderr,
            "exit_code": exit_code
        }
    except Exception as e:
        logger.error(f"执行Shell命令时出错: {str(e)}")
        return {
            "stdout": "",
            "stderr": str(e),
            "exit_code": -1
        }

step_instance = None
# 基础命令处理函数
def _handle_task_command(data: dict, command: str, cmd_id: str = None) -> None:
    """处理任务命令的通用函数
    
    Args:
        data: 命令数据
        command: 要执行的命令名称（init/run/end/clean）
    """
    global step_instance
    logger.info(f"执行任务{command}: {data}")
    try:
        if command == "init":
            myglobal.PROCESSING_CASE_ID = data["case_id"]
            script_path = data["script_path"]
            logger.info(f"script_path: {script_path}")
            module_name = os.path.basename(script_path).replace('.py', '')
            logger.info(f"module_name: {module_name}")
            sys.path.append(os.path.dirname(script_path))
            logger.info(f"sys.path: {sys.path}")
            module = importlib.import_module(module_name)
            logger.info(f"module: {module}")
            step_instance = getattr(module, 'Step')(data["case_id"])
            logger.info(f"step_instance: {step_instance}")
        command_func = getattr(step_instance, command)
        result = command_func()
        
        if not result:
            raise Exception(f"执行{command}命令返回失败")
            
        logger.info(f"完成任务{command}: {data}")
        if cmd_id:
            ack = {
                "cmd_id": cmd_id,
                "status": "ok"
            }
            send_to_server(ack)
    except Exception as e:
        logger.error(f"执行任务{command}失败: {str(e)}")
        logger.error(f"{traceback.format_exc()}")
        if cmd_id:
            ack = {
                "cmd_id": cmd_id,
                "status": "fail",
                "error": str(e)
            }
            send_to_server(ack)
        raise

def handle_task_init(data):
    _handle_task_command(data, "init", data.get("cmd_id"))

def handle_task_run(data):
    _handle_task_command(data, "run", data.get("cmd_id"))

def handle_task_end(data):
    _handle_task_command(data, "end", data.get("cmd_id"))

def handle_task_clean(data):
    _handle_task_command(data, "clean", data.get("cmd_id"))
    global step_instance
    step_instance = None

def handle_shell(data):
    """处理shell命令"""
    if "command" not in data:
        logger.error("Shell命令缺少command字段")
        return
    command = data["command"]
    working_dir = data.get("working_dir", None)
    cmd_id = data.get("cmd_id")  # 新增，获取cmd_id
    # 执行shell命令
    result = execute_shell_command(command, working_dir)
    # 向服务器发送执行结果
    response = {
        "type": "shell_result",
        "command": command,
        "result": result,
        "status": "ok" if result["exit_code"] == 0 else "fail",  # 新增
        "cmd_id": cmd_id,  # 新增
    }
    send_to_server(response)

def run_client(client_type: str):
    """Run client main function"""
    global global_client
    
    try:
        # 在主线程中初始化ROS节点
        try:
            rospy.init_node(f'sim_{client_type}', anonymous=False)
        except Exception as e:
            logger.error(f"ROS节点初始化失败: {str(e)}")
            return

        # Get server address from environment variables or use defaults
        server_ip = os.getenv("SERVER_IP", "localhost")
        server_port = os.getenv("SERVER_PORT", "50051")
        
        # Create insecure channel (for development)
        with grpc.insecure_channel(f"{server_ip}:{server_port}") as channel:
            # Wait for channel to be ready
            grpc.channel_ready_future(channel).result(timeout=10)
            logger.info(f"[{client_type}] 成功连接到服务器")
            
            # Create and run client
            client = SimpleUsimClient(channel, client_type)
            global_client = client
            
            # 注册命令处理函数
            client.register_command_handler(CMD_TASK_INIT, handle_task_init)
            client.register_command_handler(CMD_TASK_RUN, handle_task_run)
            client.register_command_handler(CMD_TASK_END, handle_task_end)
            client.register_command_handler(CMD_TASK_CLEAN, handle_task_clean)
            client.register_command_handler(CMD_SHELL, handle_shell)
            
            # 启动一个单独的线程处理通信
            client_thread = threading.Thread(target=client.start_stream)
            client_thread.daemon = True
            client_thread.start()
            
            # 主线程持续运行，等待连接建立
            try:
                # 等待客户端连接成功
                for _ in range(30):  # 最多等待3秒
                    if client.is_connected():
                        break
                    time.sleep(0.1)
                
                if client.is_connected():
                    logger.info(f"[{client_type}] 已成功连接并准备好接收命令")
                    
                    # 主循环
                    while client.is_connected():
                        time.sleep(1)  # 简单地保持运行
                else:
                    logger.error(f"[{client_type}] 无法成功连接到服务器")
            except KeyboardInterrupt:
                logger.info(f"[{client_type}] 收到关闭信号")
            finally:
                # 确保线程正常结束
                client._running = False
                client_thread.join(2)
                
    except grpc.FutureTimeoutError:
        logger.error(f"[{client_type}] 连接超时: 服务器在10秒内没有响应")
    except Exception as e:
        logger.error(f"[{client_type}] 发生错误: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    args = parse_args()
    logger.info(f"启动客户端... 类型: {args.client_type}")
    print(f"启动客户端... 类型: {args.client_type}")
    run_client(args.client_type) 