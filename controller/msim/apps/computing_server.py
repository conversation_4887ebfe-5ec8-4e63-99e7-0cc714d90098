#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
#  Copyright (C) Mogo.ai Information and Technology Co.Ltd 
#
#  All rights reserved.
#
#  This document contains proprietary information belonging to Mogo.ai Co.Ltd.
#  Passing on and copying of this document, and communication of its contents
#  is not permitted without prior written authorization.
#

import os
import shutil
import sys
import threading
import time
from pathlib import Path

project_path = os.path.realpath(__file__ + "/../../")
if project_path not in sys.path:
    sys.path.insert(0, project_path)


from controller.task_manager import TaskManager
from utils.loggers import get_server_logger
logger = get_server_logger()
from utils.file_utils import get_setup_value
from utils.constant import ReportStates
from utils.path import get_task_path, get_artifacts_path


def do_task():
    # pre check
    mount_path = Path(get_setup_value("system", "mount_path"))
    if not mount_path.exists():
        logger.error(f"mount path {mount_path} not exists.")
        return 1

    task_file = mount_path.joinpath(get_setup_value("computing", "task_file"))
    task_env_file = mount_path.joinpath(get_setup_value("computing", "env_file"))
    state_file = mount_path.joinpath(get_setup_value("computing", "state_file"))
    result_file = mount_path.joinpath(get_setup_value("computing", "result_file"))
    for file in [task_file, task_env_file, state_file, result_file]:
        if file.exists():
            logger.info(f"file: {task_file} exists, remove it.")
            file.unlink()

    # pull task & handle data
    task_manager = TaskManager()
    task_manager.pull()
    task_data = task_manager.get_task_data()

    task_file.parent.mkdir(parents=True, exist_ok=True)
    task_file.touch()
    task_path = get_task_path(task_data.task_id)
    task_content = {
        "TASK_PATH": task_path,
        "DOCKER_CONFIG_FILE": task_data.auth.docker_config if task_data.auth else "",
        "GIT_PROJECT_DIR": task_data.code.path if task_data.code else "",
    }

    if task_data.data_packing:
        task_content.update({data.name: data.path for data in task_data.data_packing.dataset})

    with open(str(task_env_file), 'w') as f:
        content = '\n'.join([key + '=' + str(value) for key, value in task_content.items()])
        content += '\n'
        f.write(content)

    with open(str(task_file), 'w') as f:
        content = task_data.container.computing.command
        content += '\n'
        f.write(content)

    state_file.touch()

    return_code = 0
    task = task_manager.get_current_task()

    def check_result():
        timeout = 3600
        start_time = time.time()
        logfile = os.path.join(task_path, 'stdout.log')
        while time.time() < start_time + timeout:
            if os.path.exists(logfile):
                break
        else:
            task.message = f"Timeout: {timeout}s"
            task.return_code = 202
            return

        file_exist_time = 0
        threshold = 10
        while True:
            if result_file.exists():
                logger.info("computing result file exists, exit.")
                with open(str(result_file), 'r') as rf:
                    result = rf.read()
                    if 'E' in result:
                        nonlocal return_code
                        return_code = result.strip().strip('E')
                        break
                file_exist_time += 1
            if file_exist_time > threshold:
                logger.error(f"Result file exists time > {threshold}s, exit.")
                return_code = 122
                break
            time.sleep(3)

    check_threading = threading.Thread(target=check_result)
    check_threading.start()
    check_threading.join()
    try:
        return_code = int(return_code)
    except Exception as e:
        logger.error(f"Return code error: {e}")
        return_code = 202

    if return_code == 122:
        task.message = "The mounted path may be full."
        task.return_code = 202
    elif return_code != 0:
        task.message = f"Return code: {return_code}"
        task.return_code = 202
        logger.error(f"Return code: {return_code}")

    stdout_file = os.path.join(task_path, 'stdout.log')
    if os.path.exists(stdout_file):
        shutil.move(stdout_file, get_artifacts_path(task_data.task_id))

    task_manager.upload()
    task_manager.report(report_status=ReportStates.FINISH)
    task_manager.cleanup()


def main():
    while True:
        do_task()


if __name__ == "__main__":
    sys.exit(main())
