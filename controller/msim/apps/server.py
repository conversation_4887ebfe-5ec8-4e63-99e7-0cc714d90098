import os
import sys
import grpc
import codecs
import threading
import time
import json
import queue
import urllib.parse
from concurrent.futures import ThreadPoolExecutor
from typing import Iterable, Dict, Optional, List
import uuid

import usimtask_pb2
import usimtask_pb2_grpc

from server_works import ServerWorks
from grpc_common import (
    set_server, get_server, 
    CMD_TASK_INIT, CMD_TASK_RUN, CMD_TASK_END, CMD_TASK_CLEAN, CMD_SHELL
)
from utils.loggers import get_server_logger

# Set environment variables
os.environ['PYTHONIOENCODING'] = 'utf-8'

# Force UTF-8 encoding
sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer)
sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer)

# 获取服务器日志记录器
logger = get_server_logger()

def format_peer_address(peer: str) -> str:
    """格式化对等端地址，使其更易读
    
    Args:
        peer: 原始对等端地址字符串
        
    Returns:
        str: 格式化后的地址
    """
    try:
        # 移除URL编码
        decoded = urllib.parse.unquote(peer)
        # 提取IP和端口
        if 'ipv6:' in decoded:
            return decoded.replace('ipv6:', '')
        return decoded
    except Exception:
        return peer

class SimpleUsimServer(usimtask_pb2_grpc.UdeerSimServicer):
    def __init__(self):
        """Initialize the gRPC server"""
        self._clients = {}  # Store connected clients: {peer_id: message_queue}
        self._client_types = {}  # Store client types: {peer_id: client_type}
        self._client_names = {}  # Store client names: {client_name: peer_id}
        self._server_works = None
        # 添加事件来通知客户端连接
        self._client_connected_events = {}
        self._pending_cmd_events = {}  # cmd_id -> threading.Event
        self._pending_cmd_results = {} # cmd_id -> result
        logger.info("服务器初始化完成")

    def set_server_works(self, server_works):
        """Set server works instance"""
        self._server_works = server_works

    def is_client_connected(self, client_name: str) -> bool:
        """检查指定客户端是否已连接
        
        Args:
            client_name: 客户端名称/类型标识
            
        Returns:
            bool: 客户端是否已连接
        """
        return client_name in self._client_names

    def wait_for_client(self, client_name: str, timeout: int = 60) -> bool:
        """等待指定客户端连接
        
        Args:
            client_name: 客户端名称/类型标识
            timeout: 等待超时时间（秒）
            
        Returns:
            bool: 客户端是否成功连接
        """
        # 检查客户端是否已连接
        if self.is_client_connected(client_name):
            logger.info(f"客户端 {client_name} 已连接")
            return True
            
        # 为客户端创建连接事件
        if client_name not in self._client_connected_events:
            self._client_connected_events[client_name] = threading.Event()
            
        # 等待客户端连接事件
        logger.info(f"等待客户端 {client_name} 连接，超时时间 {timeout} 秒")
        result = self._client_connected_events[client_name].wait(timeout)
        
        if result:
            logger.info(f"客户端 {client_name} 已成功连接")
        else:
            logger.warning(f"等待客户端 {client_name} 连接超时")
            
        return result

    def wait_for_clients(self, client_names: List[str], timeout: int = 60) -> bool:
        """等待多个客户端连接
        
        Args:
            client_names: 客户端名称/类型标识列表
            timeout: 每个客户端的等待超时时间（秒）
            
        Returns:
            bool: 所有客户端是否成功连接
        """
        start_time = time.time()
        for client_name in client_names:
            # 计算剩余超时时间
            elapsed = time.time() - start_time
            remaining = max(1, timeout - int(elapsed))
            
            # 等待客户端连接
            if not self.wait_for_client(client_name, remaining):
                return False
                
        return True

    def send_message(self, client_name: str, message: str, data: dict = None, timeout=600) -> bool:
        """向指定客户端发送命令
        
        Args:
            client_name: 客户端名称/类型标识
            message: 命令类型，如 "task_init", "task_run" 等
            data: 附加数据（可选）
            
        Returns:
            bool: 消息是否成功加入发送队列
        """
        # 找到与客户端名称对应的peer_id
        peer_id = None
        for pid, cname in self._client_types.items():
            if cname == client_name:
                peer_id = pid
                break
                
        if peer_id is None or peer_id not in self._clients:
            logger.warning(f"客户端 {client_name} 未连接或不存在")
            return False
            
        try:
            cmd_id = str(uuid.uuid4())
            cmd_msg = {
                "cmd": message,
                "cmd_id": cmd_id,
                "timestamp": time.time()
            }
            
            # 添加附加数据（如果有）
            if data:
                cmd_msg["data"] = data
                
            # 放入客户端消息队列
            event = threading.Event()
            self._pending_cmd_events[cmd_id] = event
            self._clients[peer_id].put(cmd_msg, block=True)
            logger.info(f"命令 {message} 已加入 {client_name} 的队列，cmd_id={cmd_id}")
            acked = event.wait(timeout)
            result = self._pending_cmd_results.pop(cmd_id, None)
            self._pending_cmd_events.pop(cmd_id, None)
            if not acked:
                logger.error(f"命令 {message} 等待ack超时，cmd_id={cmd_id}")
                return False
            if result and result.get("status") == "ok":
                return True
            else:
                logger.error(f"命令 {message} 执行失败，返回: {result}")
                return False
        except queue.Full:
            logger.warning(f"客户端 {client_name} 的消息队列已满")
            return False
        except Exception as e:
            logger.error(f"向客户端 {client_name} 发送消息时出错: {str(e)}")
            return False

    def send_shell_command(self, client_name: str, shell_command: str, working_dir: str = None) -> bool:
        """向指定客户端发送shell命令
        
        Args:
            client_name: 客户端名称/类型标识
            shell_command: 要执行的shell命令
            working_dir: 执行命令的工作目录（可选）
            
        Returns:
            bool: 命令是否成功加入发送队列
        """
        data = {
            "command": shell_command
        }
        
        if working_dir:
            data["working_dir"] = working_dir
            
        return self.send_message(client_name, CMD_SHELL, data)

    def SimStream(self, request_iterator: Iterable[usimtask_pb2.Request], context: grpc.ServicerContext) -> Iterable[usimtask_pb2.Response]:
        """Handle streaming requests from clients"""
        client_peer = context.peer()
        formatted_peer = format_peer_address(client_peer)
        client_type = None
        message_queue = queue.Queue(maxsize=100)  # 创建消息队列
        logger.info(f"新客户端连接请求: {formatted_peer}")

        try:
            # 处理第一个请求以获取客户端类型
            first_request = next(request_iterator)
            logger.info(f"收到客户端初始请求: {first_request.input}")
            
            try:
                # 解析客户端类型
                data = json.loads(first_request.input)
                client_type = data.get("client_type", "unknown")
                self._client_types[client_peer] = client_type
                self._clients[client_peer] = message_queue
                self._client_names[client_type] = client_peer
                logger.info(f"客户端类型: {client_type} 已连接")
                
                # 触发客户端连接事件
                if client_type in self._client_connected_events:
                    self._client_connected_events[client_type].set()

                # 响应初始请求
                init_response = usimtask_pb2.Response()
                init_response.output = json.dumps({
                    "status": "connected", 
                    "message": f"服务器已接受 {client_type} 客户端连接",
                    "timestamp": time.time()
                })
                yield init_response
            except json.JSONDecodeError:
                logger.error(f"无法解析客户端请求: {first_request.input}")
                context.abort(grpc.StatusCode.INVALID_ARGUMENT, "请求格式无效")
                return

            # 创建一个后台线程来处理客户端的请求
            request_thread = threading.Thread(
                target=self._handle_client_requests,
                args=(client_peer, client_type, request_iterator)
            )
            request_thread.daemon = True
            request_thread.start()

            # 主循环：从消息队列获取消息并发送到客户端
            while context.is_active():
                try:
                    # 从队列获取消息，无限等待
                    message = message_queue.get()
                    response = usimtask_pb2.Response()
                    response.output = json.dumps(message)
                    logger.info(f"发送命令到客户端 {client_type}: {message['cmd']} cmd_id={message.get('cmd_id')}")
                    yield response
                except Exception as e:
                    logger.error(f"向客户端 {client_type} 发送消息时出错: {str(e)}")
                    break

            logger.info(f"客户端 {client_type} 的流已结束")
        except Exception as e:
            logger.error(f"处理客户端 {client_peer} 请求时出错: {str(e)}")
        finally:
            # 清理资源
            if client_peer in self._clients:
                del self._clients[client_peer]
            if client_peer in self._client_types:
                client_type = self._client_types[client_peer]
                logger.info(f"客户端 {client_type} 断开连接")
                del self._client_types[client_peer]
                if client_type in self._client_names:
                    del self._client_names[client_type]
                # 重置客户端连接事件
                if client_type in self._client_connected_events:
                    self._client_connected_events[client_type].clear()

    def _handle_client_requests(self, client_peer, client_type, request_iterator):
        """处理来自客户端的请求的线程函数"""
        try:
            for request in request_iterator:
                try:
                    data = json.loads(request.input)
                    logger.info(f"收到 {client_type} 请求: {data}")
                    if "cmd_id" in data and "status" in data:
                        cmd_id = data["cmd_id"]
                        self._pending_cmd_results[cmd_id] = data
                        event = self._pending_cmd_events.get(cmd_id)
                        if event:
                            event.set()
                except json.JSONDecodeError:
                    logger.error(f"无法解析 {client_type} 的请求数据: {request.input}")
        except Exception as e:
            logger.error(f"处理 {client_type} 请求时出错: {str(e)}")
        finally:
            logger.info(f"停止处理 {client_type} 的请求")

def run_grpc_server(address: str, server_works) -> None:
    """Run gRPC server in a separate thread"""
    global global_server
    
    try:
        # Create a gRPC server
        server = grpc.server(ThreadPoolExecutor(max_workers=10))
        
        # Create servicer and set server_works
        servicer = SimpleUsimServer()
        servicer.set_server_works(server_works)
        
        # Set global server instance
        set_server(servicer)
        
        # Add the servicer to the server
        usimtask_pb2_grpc.add_UdeerSimServicer_to_server(servicer, server)
        
        # Add insecure port
        server.add_insecure_port(address)
        
        # Start the server
        server.start()
        logger.info(f"gRPC 服务器已启动，监听地址: {address}")
        
        # Keep the server running
        server.wait_for_termination()
    except Exception as e:
        logger.error(f"运行 gRPC 服务器出错: {str(e)}")
        raise e

if __name__ == "__main__":
    # Get server port from environment variable or use default
    server_port = os.getenv("SERVER_PORT", "50051")
    logger.info(f"在端口 {server_port} 上启动服务器")
    
    # Initialize ServerWorks
    server_works = ServerWorks()
    
    # Start gRPC server in a separate thread
    server_thread = threading.Thread(target=run_grpc_server, args=(f"[::]:{server_port}", server_works))
    server_thread.daemon = True
    server_thread.start()
    
    try:
        # 等待服务器启动
        time.sleep(1)
        
        # 获取全局服务器实例
        server = get_server()
        if not server:
            logger.error("无法获取服务器实例")
            sys.exit(1)
            
        # 等待必要的客户端连接
        logger.info("等待必要的客户端连接...")
        required_clients = ["engine", "validator"]
        
        if not server.wait_for_clients(required_clients, 60):
            logger.error("等待客户端连接超时，无法继续初始化")
            sys.exit(1)
            
        # Run server initialization in main thread
        logger.info("开始服务器初始化...")
        server_works.server_init()
        logger.info("服务器初始化完成")

        for case_id, case_info_path in server_works.tasks_info["caseList"].items():
            logger.info("下一个任务。。。。。。。。。")
            if not server_works.task_init(case_id, case_info_path):
                logger.error(f"任务 {case_id} init调用失败")
                break
            
            if not server_works.task_run(case_id):
                logger.error(f"任务 {case_id} run调用失败")
                break
            
            if not server_works.task_end(case_id):
                logger.error(f"任务 {case_id} end调用失败")
                break
            
            if not server_works.task_clean(case_id):
                logger.error(f"任务 {case_id} clean调用失败")
                break
            
            logger.info(f"任务 {case_id} 执行完成")

            for i in range(10):
                time.sleep(1)
                logger.info(f"等待{i}秒")
            
        
        # Keep the main thread running
        while True:
            server_thread.join(1)
            if not server_thread.is_alive():
                logger.error("gRPC 服务器线程意外终止")
                break
    except KeyboardInterrupt:
        logger.info("收到关闭信号")
    except Exception as e:
        logger.error(f"主线程错误: {str(e)}")
        raise e