# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: usimtask.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='usimtask.proto',
  package='udeer_sim',
  syntax='proto2',
  serialized_options=None,
  serialized_pb=_b('\n\x0eusimtask.proto\x12\tudeer_sim\"\x18\n\x07Request\x12\r\n\x05input\x18\x01 \x02(\t\"\x1a\n\x08Response\x12\x0e\n\x06output\x18\x01 \x02(\t2F\n\x08UdeerSim\x12:\n\tSimStream\x12\x12.udeer_sim.Request\x1a\x13.udeer_sim.Response\"\x00(\x01\x30\x01')
)




_REQUEST = _descriptor.Descriptor(
  name='Request',
  full_name='udeer_sim.Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='input', full_name='udeer_sim.Request.input', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=29,
  serialized_end=53,
)


_RESPONSE = _descriptor.Descriptor(
  name='Response',
  full_name='udeer_sim.Response',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='output', full_name='udeer_sim.Response.output', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=55,
  serialized_end=81,
)

DESCRIPTOR.message_types_by_name['Request'] = _REQUEST
DESCRIPTOR.message_types_by_name['Response'] = _RESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Request = _reflection.GeneratedProtocolMessageType('Request', (_message.Message,), dict(
  DESCRIPTOR = _REQUEST,
  __module__ = 'usimtask_pb2'
  # @@protoc_insertion_point(class_scope:udeer_sim.Request)
  ))
_sym_db.RegisterMessage(Request)

Response = _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), dict(
  DESCRIPTOR = _RESPONSE,
  __module__ = 'usimtask_pb2'
  # @@protoc_insertion_point(class_scope:udeer_sim.Response)
  ))
_sym_db.RegisterMessage(Response)



_UDEERSIM = _descriptor.ServiceDescriptor(
  name='UdeerSim',
  full_name='udeer_sim.UdeerSim',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=83,
  serialized_end=153,
  methods=[
  _descriptor.MethodDescriptor(
    name='SimStream',
    full_name='udeer_sim.UdeerSim.SimStream',
    index=0,
    containing_service=None,
    input_type=_REQUEST,
    output_type=_RESPONSE,
    serialized_options=None,
  ),
])
_sym_db.RegisterServiceDescriptor(_UDEERSIM)

DESCRIPTOR.services_by_name['UdeerSim'] = _UDEERSIM

# @@protoc_insertion_point(module_scope)
