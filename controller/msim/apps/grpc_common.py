from utils.loggers import get_server_logger
logger = get_server_logger()

# 命令类型常量
CMD_TASK_INIT = "task_init"
CMD_TASK_RUN = "task_run"
CMD_TASK_END = "task_end"
CMD_TASK_CLEAN = "task_clean"
CMD_SHELL = "shell"  # 用于执行shell命令

# 全局服务器实例，方便外部调用
global_server = None

def get_server():
    """获取全局服务器实例"""
    return global_server

def set_server(server):
    """设置全局服务器实例"""
    global global_server
    global_server = server

def send_to_client(client_name: str, cmd: str, data: dict = None) -> bool:
    """便捷函数：向指定客户端发送命令"""
    server = get_server()
    if server:
        try:
            return server.send_message(client_name, cmd, data)
        except Exception as e:
            logger.error(f"向客户端 {client_name} 发送命令 {cmd} 时出错: {str(e)}")
            return False
    else:
        logger.error(f"服务器未初始化，无法向客户端 {client_name} 发送命令: {cmd}")
        return False

def send_shell_command(client_name: str, shell_command: str, working_dir: str = None) -> bool:
    """便捷函数：向指定客户端发送shell命令"""
    server = get_server()
    if server:
        try:
            result = server.send_shell_command(client_name, shell_command, working_dir)
            if not result:
                logger.warning(f"向客户端 {client_name} 发送shell命令失败: {shell_command}")
            return result
        except Exception as e:
            logger.error(f"向客户端 {client_name} 发送shell命令时出错: {str(e)}")
            return False
    else:
        logger.error(f"服务器未初始化，无法向客户端 {client_name} 发送shell命令: {shell_command}")
        return False 