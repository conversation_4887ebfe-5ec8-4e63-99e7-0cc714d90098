import time
import json
import os
import shutil
from typing import Tuple
from utils.path import (
    get_workspace_path,
    get_mount_path,
    get_tasks_info,
    get_task_workspace_path,
    get_task_casefile_path,
    get_task_map_path,
    get_task_vehicle_path,
    get_task_log_path,
    get_task_result_path,
    get_task_record_path,
    get_mount_path,
    is_manual_mode,
)
from utils.platform.platform_service import PlatformService
from utils.file_utils import untar_to_dest, get_setup_value, find_files
from utils.docker_utils import run_algorithm_container, docker_cp_cmd, stop_container
from grpc_common import (
    send_to_client,
    send_shell_command,
    CMD_TASK_INIT,
    CMD_TASK_RUN,
    CMD_TASK_END,
    CMD_TASK_CLEAN,
)
from utils.loggers import get_server_logger

# 获取服务器日志记录器
logger = get_server_logger()


def singleton(cls):
    """单例装饰器"""
    _instances = {}

    def get_instance(*args, **kwargs):
        if cls not in _instances:
            _instances[cls] = cls(*args, **kwargs)
        return _instances[cls]

    return get_instance


@singleton
class ServerWorks:
    def __init__(self):
        """初始化服务器工作类"""
        self.tasks_info = None
        self.sim_type = None
        self.validator_type = None
        self.engine_path = None
        self.validator_path = None
        self.task_result_path = None

        self.algo_image_id = None
        self.platform_service = PlatformService()

    def download_image(self, url) -> str:  # 返回镜像id
        """下载镜像"""
        # time.sleep(10)
        image_id = self.platform_service.pull_algo_image(url)
        logger.info(f"下载镜像: {url}")
        return image_id

    def server_init(self):
        """服务器初始化"""
        # 读取任务信息
        logger.info("开始读取任务信息...")
        tasks_info_path = get_tasks_info()
        logger.info(f"任务信息路径: {tasks_info_path}")

        try:
            with open(tasks_info_path, "r") as f:
                self.tasks_info = json.load(f)
                self.sim_type = self.tasks_info["simType"]
                self.validator_type = self.tasks_info["validator_types"]
                # 展示所有的task_infos
                logger.info(f"任务信息: {self.tasks_info}")
        except Exception as e:
            logger.error(f"读取任务信息失败: {str(e)}")
            raise

        logger.info("配置执行模块路径...")
        engine_dir = os.path.join(get_workspace_path(), "code", "engine")
        os.makedirs(engine_dir, exist_ok=True)
        self.engine_path = os.path.join(engine_dir, self.sim_type)
        shell_cmd = f"ln -sf /opt/code/engine/{self.sim_type} {self.engine_path}"
        logger.info(f"shell_cmd = {shell_cmd}")
        result = send_shell_command("engine", f"{shell_cmd}", get_workspace_path())
        logger.info(f"配置执行模块命令执行结果: {result}")

        logger.info("配置评价包路径...")
        validator_dir = os.path.join(get_workspace_path(), "code", "validator")
        os.makedirs(validator_dir, exist_ok=True)
        self.validator_path = os.path.join(validator_dir, self.validator_type)
        shell_cmd = (
            f"ln -sf /opt/code/validator/{self.validator_type} {self.validator_path}"
        )
        logger.info(f"shell_cmd = {shell_cmd}")
        result = send_shell_command("validator", f"{shell_cmd}", get_workspace_path())
        logger.info(f"配置评价包命令执行结果: {result}")

        # 检查manual模式，条件性下载镜像
        if not is_manual_mode():
            logger.info("容器模式：开始下载镜像...")
            self.algo_image_id = self.download_image(self.tasks_info["codeImage"])
            with open(os.path.join(get_workspace_path(), "algo_image_id"), "w") as f:
                f.write(self.algo_image_id)
            logger.info("镜像下载完成")
        else:
            logger.info("本地模式：跳过算法镜像下载")
            self.algo_image_id = None

        # 根据case_id创建task工作目录
        logger.info("开始创建task工作目录...")
        for case_id, _ in self.tasks_info["caseList"].items():
            self.create_task_dir(case_id)
        logger.info("所有任务工作目录创建完成")

        sim_task_id = self.tasks_info["simTaskId"]
        # 以sim_task_id_当前时间戳为文件名，新建task结果目录
        self.task_result_path = os.path.join(get_workspace_path(), sim_task_id + "_" + str(int(time.time())))
        os.makedirs(self.task_result_path, exist_ok=True)
        logger.info(f"任务结果目录: {self.task_result_path}")


    def start_algo_container(self, image_id: str) -> str:
        # 启动算法容器、做必要的容器内处理（模块替换、cp消息类型等）
        logger.info("开始启动算法容器...")
        container_id = run_algorithm_container(
            image_name=image_id,
            container_name="algo_container",
            network="container:controller",
            mount_path=[
                "/tmp/.X11-unix:/tmp/.X11-unix",
            ],
            gpu=True,
            privileged=True,
            environment={
                "ROS_LOCALHOST_ONLY": "1",
                "ROS_HOSTNAME": "localhost",
                "ROS_MASTER_URI": "http://localhost:11311",
            },
        )
        logger.info(f"算法容器启动成功，容器ID: {container_id}")
        
        # 检查manual模式决定是否复制文件
        if not is_manual_mode():
            mount_path = get_mount_path()
            # 先清理mount_path下的文件
            if os.path.exists(mount_path):
                logger.info(f"清理挂载路径: {mount_path}")
                shutil.rmtree(mount_path)
                # 新建mount文件夹，并赋予777权限
                os.makedirs(mount_path, exist_ok=True)
                os.chmod(mount_path, 0o777)
            msgs_path = get_setup_value("docker_file_path", "algo_msgs_path")
            logger.info(
                f"开始将消息类型文件复制到宿主机，容器ID: {container_id}，挂载路径: {mount_path}，消息类型文件路径: {msgs_path}"
            )
            cp_result = docker_cp_cmd(
                src_path=msgs_path,
                dest_path=mount_path,
                container_id_or_name=container_id,
                from_docker_to_host=True,
            )
            logger.info(f"消息类型文件复制结果: {cp_result}")
        else:
            logger.info("本地模式：跳过容器文件复制操作")
        
        return container_id

    def case_check(self, case) -> Tuple[bool, str]:
        """
        校验case数据的合法性

        Args:
            case_id: 案例ID

        Returns:
            tuple[bool, str]: (是否合法, 错误信息)
        """
        for data in ("id", "map", "case_file", "vehicle_config", "db.sqlite"):
            if case[data] is None:
                return False, data
        return True, ""

    def _send_task_command(self, case_id: str, cmd: str) -> bool:
        """向engine和validator发送任务命令

        Args:
            case: 任务案例
            cmd: 命令类型（task_init/task_run/task_end/task_clean）

        Returns:
            bool: 两个客户端是否都执行成功
        """
        data_engine = {
            "case_id": case_id,
            "script_path": os.path.join(self.engine_path, "step.py"),
        }
        data_validator = {
            "case_id": case_id,
            "script_path": os.path.join(self.validator_path, "step.py"),
        }

        import threading

        engine_result = False
        validator_result = False

        def send_engine():
            nonlocal engine_result
            engine_result = send_to_client("engine", cmd, data_engine)

        def send_validator():
            nonlocal validator_result
            validator_result = send_to_client("validator", cmd, data_validator)

        t1 = threading.Thread(target=send_engine)
        t2 = threading.Thread(target=send_validator)
        t1.start()
        t2.start()
        t1.join()
        t2.join()
        return engine_result and validator_result

    def task_init(self, case_id, case_info_path):
        """
        任务初始化

        Args:
            case: 任务案例
        """
        # 校验case的合法性
        # TODO: 改为后续检查case_info.json是否合法
        # is_valid, error_msg = self.case_check(case)
        # if not is_valid:
        #     raise Exception(f"case {case['id']} check failed: {error_msg} is none")
        case_workspace_path = get_task_workspace_path(case_id)
        casefile_path = get_task_casefile_path(case_id)
        # 判断是否为COS地址，是则下载
        if get_setup_value("cos", "cos_url") in case_info_path:
            logger.info("传入COS地址，开始下载case信息必要文件...")

            # 下载case info压缩包
            download_filename = self.platform_service.download_from_cloud(
                case_info_path, case_workspace_path
            )
            if not download_filename:
                raise Exception("下载case文件失败")
            # 下载后的文件始终保存为 casefile_path/<filename>（无子目录）
            file_path = os.path.join(case_workspace_path, download_filename)
            logger.info(
                "case_workspace_path:{}, file_path:{}".format(case_workspace_path, file_path)
            )
            untar_to_dest(file_path, casefile_path)
            os.remove(file_path)
            case_info_json_path = os.path.join(casefile_path, download_filename.split('.')[0], "case_info.json")
        #==== manual模式， case_info_path是一个压缩包========
        elif case_info_path.endswith('.tar.gz'):
            logger.info("传入case压缩包, 压缩包名：{}，开始解压...".format(case_info_path))
            tar_filename = os.path.basename(case_info_path)
            # case_info_path改为映射后容器内路径
            case_info_path = os.path.join(get_workspace_path(), tar_filename)
            logger.info("修改后，容器内路径case_info_path: {}".format(case_info_path))
            # 把压缩包移动到指定路径
            shutil.copy2(case_info_path, case_workspace_path)
            untar_to_dest(os.path.join(case_workspace_path, tar_filename), casefile_path)
            os.remove(os.path.join(case_workspace_path, tar_filename))
            case_info_json_path = os.path.join(casefile_path, tar_filename.split('.')[0], "case_info.json")
        # ==========manual模式， case_info_path是一个文件夹=========
        else:
            logger.info("传入case文件夹，开始移动...")
            last_folder = os.path.basename(os.path.normpath(case_info_path))
            case_info_path = os.path.join(get_workspace_path(), last_folder)
            if os.path.exists(os.path.join(casefile_path, last_folder)):
                shutil.rmtree(os.path.join(casefile_path, last_folder))
            shutil.copytree(case_info_path, os.path.join(casefile_path, last_folder), dirs_exist_ok=True)
            case_info_json_path = os.path.join(casefile_path, last_folder, "case_info.json")
        logger.info("### case_info_json_path is {}".format(case_info_json_path))

        with open(case_info_json_path, "r", encoding="utf-8") as f:
            case_info = json.load(f)
            case_map = case_info.get("map")
            case_vehicle_config = case_info.get("vehicle_config")
            case_db_sqlite = case_info.get("db.sqlite")

        if case_map is not None:
            map_path = get_task_map_path(case_id)
            logger.info(f"开始下载地图 {case_map} 到 {map_path}")
            download_result = self.platform_service.download_map(
                map_name_cn=case_map,
                map_type=None,
                map_version=None,
                download_path=map_path,
            )
            if not download_result:
                raise Exception(f"map {case_map} download failed")

        # vehicle_config
        if case_vehicle_config is not None:
            vehicle_config_path = get_task_vehicle_path(case_id)
            download_result = self.platform_service.download_veh_config(
                case_vehicle_config, vehicle_config_path
            )
            if not download_result:
                raise Exception(
                    f"vehicle_config {case_vehicle_config} download failed"
                )

        # db.sqlite
        if case_db_sqlite is not None:
            casefile_path = get_task_casefile_path(case_id)
            download_result = self.platform_service.download_sqlite_file(
                case_db_sqlite, casefile_path
            )
            if not download_result:
                raise Exception(
                    f"db.sqlite {case_db_sqlite} download failed"
                )


        # 检查是否为manual模式
        if not is_manual_mode():
            # 容器模式：启动算法容器
            if not self.algo_image_id:
                raise Exception("算法镜像ID为空，无法启动容器")
            self.algo_container_id = self.start_algo_container(self.algo_image_id)
            with open(
                os.path.join(get_task_workspace_path(case_id), "algo_container_id"), "w"
            ) as f:
                f.write(self.algo_container_id)
            logger.info(f"容器模式：算法容器启动完成，ID: {self.algo_container_id}")
        else:
            # 本地模式：跳过算法容器启动
            self.algo_container_id = None
            logger.info("本地模式：跳过算法容器启动")

        time.sleep(1)

        return self._send_task_command(case_id, CMD_TASK_INIT)

    def task_run(self, case_id):
        """
        任务运行

        Args:
            case: 任务案例
        """
        return self._send_task_command(case_id, CMD_TASK_RUN)

    def task_end(self, case_id):
        """
        任务结束

        Args:
            case: 任务案例
        """
        return self._send_task_command(case_id, CMD_TASK_END)

    def task_clean(self, case_id):
        """
        任务清理

        Args:
            case: 任务案例
        """
        clean_result = self._send_task_command(case_id, CMD_TASK_CLEAN)
        # 删除casefile目录下的bag文件
        case_workspace_path = get_task_workspace_path(case_id)
        casefile_path = get_task_casefile_path(case_id)
        bag_files = find_files(casefile_path, "*.bag")
        for bag_file in bag_files:
            os.remove(bag_file)
        sqlite_files = find_files(casefile_path, "*.sqlite*")
        for sqlite_file in sqlite_files:
            os.remove(sqlite_file)
        # 删除casefile_path目录下的map文件夹
        if os.path.exists(os.path.join(casefile_path, "map")):
            shutil.rmtree(os.path.join(casefile_path, "map"))
        # 将case_workspace_path目录及所有文件移动至self.task_result_path目录下
        if os.path.exists(case_workspace_path):
            shutil.move(case_workspace_path, self.task_result_path)
            logger.info(f"任务结束，将{case_workspace_path}目录及所有文件移动至{self.task_result_path}")

        # 只在容器模式下停止容器
        if not is_manual_mode() and hasattr(self, 'algo_container_id') and self.algo_container_id:
            stop_result = stop_container(self.algo_container_id)
            return clean_result and stop_result
        else:
            logger.info("本地模式或无容器ID：跳过容器停止操作")
            return clean_result

    def create_task_dir(self, case_id):
        """
        创建任务相关的所有目录结构

        Args:
            case_id: 案例ID

        Returns:
            str: 任务工作空间路径
        """
        # 获取基础工作空间路径
        workspace_path = get_workspace_path()
        if not os.path.exists(workspace_path):
            raise Exception(f"workspace_path {workspace_path} not exists")

        # 创建任务工作空间
        case_workspace = get_task_workspace_path(case_id)
        if not os.path.exists(case_workspace):
            os.makedirs(case_workspace)
            os.chmod(case_workspace, 0o777)
            logger.info(f"创建任务工作空间: {case_workspace}")

        # 创建任务相关的所有子目录
        directories = [
            get_task_casefile_path(case_id),  # 案例文件目录
            get_task_map_path(case_id),  # 地图目录
            get_task_vehicle_path(case_id),  # 车辆目录
            get_task_log_path(case_id),  # 日志目录
            get_task_result_path(case_id),  # 结果目录
            get_task_record_path(case_id),  # 记录目录
        ]

        for directory in directories:
            if not os.path.exists(directory):
                os.makedirs(directory)
                os.chmod(directory, 0o777)
                logger.info(f"创建目录: {directory}")

        return case_workspace
