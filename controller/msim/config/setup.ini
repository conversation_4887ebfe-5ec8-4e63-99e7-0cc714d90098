[system]
language = zh
runtime_mode = online
platform = x86
internal_network = true
workspace_path = /home/<USER>/workspace/

[mq_internal]
host = **********
port = 5672
vhost = /
credentials_user = admin
credentials_password = admin
#mq_queue = huangzhuofei_7727
mq_queue = common_sim_engine


[mq]
host = *************
port = 56720
vhost = /
credentials_user = huoshan
credentials_password = <PERSON><PERSON><PERSON>@2024
mq_queue = huangzhuofei_7727

[grpc]
server_ip = localhost
server_port = 50051
clients = validator,algorithm,engine

[platform]
scheduler_test_url = http://usim-test.udeer.ai/_internal/usim
scheduler_prod_url = http://usim.udeer.ai/_internal/usim
task_pull_api = /open/schedule/task/pull
task_report_api = /open/schedule/task/report

[cos]
cos_url = https://autocar-mogosim-1255510688.cos.ap-beijing.myqcloud.com/
secret_id= AKIDkOoyrTqwkHYYHjvqUrwlJaaKnztWsxlw
secret_key = xd18zauwiFVTAbkcDZdQwBArHQItDG2Y
region = ap-beijing
bucketName = autocar-mogosim-1255510688

[tos]
ak = AKLTNGI4M2JlYTZhMDM5NGFiMmE1OTBlOTMwM2VmMWFlNTI
sk = TnpZMVpXTmhOMlUwTnpBMk5EQXpNMkpqWXpNM1lUaGhZemM0T0RFeE5EVQ==
endpoint = tos-cn-beijing.ivolces.com
region = cn-beijing
bucket_name = logsim-tos-bj

[mogo_platform]
login_url = https://mygateway.zhidaoauto.com/api/auth/login
username = zhangxinhong
password = ZDzxh123789
vehicle_config_url = https://mygateway.zhidaoauto.com/api/artifact/carConfig/download/
gridmap_query_url = https://mygateway.zhidaoauto.com/api/artifact/query/page
gridmap_download_url = https://mygateway.zhidaoauto.com/api/artifact/getArtifactDownloadInfo


[docker]
algo_repository = mogohub.tencentcloudcr.com/op/base-ros2
algo_username = tcr$read-only
algo_password = EmyZmm7OXIcwSqHtVHfmdEzk1g9si4KI

[docker_file_path]
algo_msgs_path = /autocar-code/install/lib/python3/dist-packages/

[warnreport]
warn_url = https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=61953743-bf41-453a-8afe-8a5676ac3d0d

[cloud]
cloud_config = controller/msim/config/cloud_config.yaml
