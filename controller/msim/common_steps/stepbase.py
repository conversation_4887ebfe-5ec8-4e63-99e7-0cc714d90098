from abc import ABC, abstractmethod
from utils.loggers import LoggerProxy
from utils.path import get_task_workspace_path, get_workspace_path
import os
logger = LoggerProxy() 

class StepBase(ABC):
    """步骤基类，定义了所有步骤必须实现的接口"""
    def __init__(self, case_id=None):
        self.case_id = case_id
        
    def get_case_id(self):
        return self.case_id

    def get_algo_container_id(self):
        if not self.algo_container_id:
            algo_container_id_path = os.path.join(get_task_workspace_path(self.case_id), "algo_container_id")
            if os.path.exists(algo_container_id_path):
                with open(algo_container_id_path, "r") as f:
                    self.algo_container_id = f.read()
            else:
                self.algo_container_id = None
        return self.algo_container_id
       
    @abstractmethod
    def init(self) -> bool:
        """初始化步骤
        
        Returns:
            bool: 初始化是否成功
        """
        pass
    
    @abstractmethod
    def run(self) -> bool:
        """运行步骤
        
        Returns:
            bool: 运行是否成功
        """
        pass
    
    @abstractmethod
    def end(self) -> bool:
        """结束步骤
        
        Returns:
            bool: 结束是否成功
        """
        pass
    
    @abstractmethod
    def clean(self) -> bool:
        """清理步骤
        
        Returns:
            bool: 清理是否成功
        """
        pass

class EngineStepBase(StepBase):
    def __init__(self, case_id):
        super().__init__(case_id)
        algo_container_id_path = os.path.join(get_task_workspace_path(self.case_id), "algo_container_id")
        if os.path.exists(algo_container_id_path):
            with open(algo_container_id_path, "r") as f:
                self.algo_container_id = f.read()
        else:
            self.algo_container_id = None
        

class ValidatorStepBase(StepBase):
    def __init__(self, case_id):
        super().__init__(case_id)





