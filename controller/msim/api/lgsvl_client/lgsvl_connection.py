#!/usr/bin/python3
# -*- coding: utf-8 -*-

import time
import lgsvl
import signal
import os
import threading
from typing import Optional
from concurrent.futures import ThreadPoolExecutor, TimeoutError

from utils.loggers import LoggerProxy
from .utils.lgsvl_controller import LgsvlController

logger = LoggerProxy("lgsvl")


class UserInterruption(Exception):
    pass


class SimConnection:
    def __init__(
        self,
        seconds=60,
        scene=lgsvl.wise.DefaultAssets.map_borregasave,
        host="localhost",
        error_message=None,
        load_scene=True,
        lgsvl_root: Optional[str] = None,
        auto_start_lgsvl=True,
        case_id: str = "case_001",
    ):
        """
        LGSVL仿真连接管理器。

        参数:
            seconds (int): 连接超时时间，默认60秒
            scene: 仿真场景
            host (str): 仿真器主机地址
            error_message (str): 错误信息
            load_scene (bool): 是否加载场景
            lgsvl_root (str): LGSVL根目录路径，如果提供则启用自动启停功能
            auto_start_lgsvl (bool): 是否自动启动LGSVL（仅在提供lgsvl_root时有效）
        """
        logger.info("simConnection init start..")
        self.seconds = seconds
        self.scene = scene
        self.load_scene = load_scene
        self.host = host
        self.collision = False
        self.sim = None
        self.load_scene_success = False

        # 初始化LGSVL控制器
        self.lgsvl_controller: Optional[LgsvlController] = None
        self.auto_start_lgsvl = auto_start_lgsvl
        if lgsvl_root:
            self.lgsvl_controller = LgsvlController(lgsvl_root, case_id)
            logger.info(f"已初始化LGSVL控制器，根目录: {lgsvl_root}")

        logger.info("simConnection init end..")

    def __del__(self):
        if self.load_scene_success == False:
            return
        logger.info(f"SimConnection del {self}...")
        self.handle_stop()

    def handle_userInterruption(self, signum, frame):
        logger.info(f"handle_userInterruption {self}...")
        self.safe_reset_stop(interrupt=True)
        raise UserInterruption("user canceled")

    def handle_stop(self):
        logger.info(f"handle_stop {self}...")
        self.safe_reset_stop()

    def on_collsion_handler(self, agent1, agent2, contact):
        logger.info("ego collision occurred, simulation terminated!")
        self.collision = True
        self.safe_reset_stop()

    def start_lgsvl_if_needed(self):
        """
        如果配置了LGSVL控制器且启用自动启动，则启动LGSVL仿真器。
        """
        if self.lgsvl_controller and self.auto_start_lgsvl:
            logger.info("准备启动LGSVL仿真器...")
            try:
                self.lgsvl_controller.start()
                logger.info("LGSVL仿真器启动成功")
                # 等待仿真器完全启动
                time.sleep(5)
            except Exception as e:
                logger.error(f"启动LGSVL仿真器失败: {e}")
                raise

    def stop_lgsvl_if_needed(self):
        """
        如果配置了LGSVL控制器，则停止LGSVL仿真器。
        """
        if self.lgsvl_controller:
            logger.info("准备停止LGSVL仿真器...")
            try:
                self.lgsvl_controller.stop()
                logger.info("LGSVL仿真器停止成功")
            except Exception as e:
                logger.error(f"停止LGSVL仿真器失败: {e}")

    def __enter__(self):
        logger.info("simConnection enter start... ")

        # 设置用户中断信号处理器（仅支持主线程）
        if threading.current_thread() is threading.main_thread():
            try:
                signal.signal(
                    signal.SIGINT, self.handle_userInterruption
                )  # CTRL+C / KILL -2
                signal.signal(signal.SIGUSR2, self.handle_userInterruption)  # KILL -12
                logger.info("已设置用户中断信号处理器")
            except ValueError as e:
                logger.warning(f"设置信号处理器失败: {e}")

        # 启动LGSVL仿真器（如果需要）
        self.start_lgsvl_if_needed()

        # 单次连接尝试，最多等待60秒
        logger.info(f"准备连接LGSVL模拟器，最大等待时间: {self.seconds}秒")

        sim_instance = None
        exception_container = []

        def connect_task():
            nonlocal sim_instance
            nonlocal exception_container
            try:
                sim_instance = lgsvl.Simulator(
                    os.environ.get("LGSVL__SIMULATOR_HOST", self.host), 8181
                )
            except Exception as e:
                exception_container.append(e)

        connect_thread = threading.Thread(target=connect_task)
        connect_thread.start()
        connect_thread.join(timeout=self.seconds)

        if connect_thread.is_alive():
            logger.error(f"连接LGSVL模拟器超时（超过{self.seconds}秒）")
            raise ConnectionError(f"连接LGSVL模拟器超时（超过{self.seconds}秒）")
        elif exception_container:
            logger.error(f"连接LGSVL模拟器失败: {exception_container[0]}")
            raise ConnectionError(f"连接LGSVL模拟器失败: {exception_container[0]}")
        else:
            self.sim = sim_instance
            logger.info("LGSVL模拟器连接成功！")

        logger.info("simConnection success... ")
        if self.load_scene:
            if self.sim.current_scene == self.scene:
                logger.info("start! reset command!")
                self.sim.reset()
            else:
                logger.info(f"sim load scene:{self.scene}")
                self.sim.load(self.scene)
                self.load_scene_success = True
        logger.info("SimConnection enter end...")
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        logger.info(f"simConnection exit start... {exc_type} {exc_val} {exc_tb} {self}")
        try:
            self.safe_reset_stop()
        finally:
            # 停止LGSVL仿真器（如果需要）
            self.stop_lgsvl_if_needed()
            logger.info("simConnection exit end... ")
            return exc_type == None

    def safe_reset_stop(self, interrupt=False):
        """
        安全重置和停止仿真器连接。

        参数:
            interrupt (bool): 是否为中断停止
        """
        if self.sim is None:
            logger.info(f"safe_reset_stop sim is None {self}...")
            return

        try:
            logger.info(f"safe_reset_stop start {self}...")
            if self.sim.remote.isAlive():
                logger.info("sim remote is alive...")
                try:
                    # 为EXT_API_MODE命令添加超时处理
                    logger.info("准备执行EXT_API_MODE命令...")

                    import threading

                    api_mode_exception = [None]
                    api_mode_success = [False]

                    def execute_api_mode():
                        try:
                            self.exec_ext_cmd("EXT_API_MODE", lgsvl.ObjectState())
                            api_mode_success[0] = True
                        except Exception as e:
                            api_mode_exception[0] = e

                    # 使用线程执行命令，设置较短的超时时间
                    api_mode_thread = threading.Thread(
                        target=execute_api_mode, daemon=True
                    )
                    api_mode_thread.start()
                    api_mode_thread.join(timeout=5)  # 5秒超时

                    if api_mode_thread.is_alive():
                        logger.warning("EXT_API_MODE命令执行超时，跳过该步骤")
                    elif api_mode_exception[0] is not None:
                        logger.warning(
                            f"EXT_API_MODE命令执行失败: {api_mode_exception[0]}"
                        )
                    elif api_mode_success[0]:
                        logger.info("EXT_API_MODE命令执行成功")
                        time.sleep(2)  # 减少等待时间

                    # 停止仿真
                    if not self.sim.stopped:
                        logger.info("stop command")
                        stop_exception = [None]
                        stop_success = [False]

                        def execute_stop():
                            try:
                                self.sim.stop()
                                stop_success[0] = True
                            except Exception as e:
                                stop_exception[0] = e

                        stop_thread = threading.Thread(target=execute_stop, daemon=True)
                        stop_thread.start()
                        stop_thread.join(timeout=5)  # 5秒超时

                        if stop_thread.is_alive():
                            logger.warning("仿真停止命令超时")
                        elif stop_exception[0] is not None:
                            logger.warning(f"仿真停止失败: {stop_exception[0]}")
                        elif stop_success[0]:
                            logger.info("仿真已停止")

                except Exception as e:
                    logger.warning(f"执行仿真器操作时出现异常: {e}")
            logger.info("close command!")
        except Exception as e:
            logger.exception(e)
            logger.error("重置模拟器异常", e)
        finally:
            try:
                if self.sim:
                    self.sim.close()
            except Exception as close_e:
                logger.warning(f"关闭仿真器时出错: {close_e}")
            finally:
                self.sim = None
                logger.info("safe_reset_stop end... ")

    def exec_ext_cmd(self, ext_cmd: str, object_state=None):
        """
        执行扩展命令。

        参数:
            ext_cmd (str): 扩展命令名称
            object_state: 对象状态
        """
        logger.info(f"exec_ext_cmd:{ext_cmd} {object_state}")
        if self.sim == None or not self.sim.remote.isAlive():
            logger.warning(f"模拟器未连接或已断开，无法执行命令: {ext_cmd}")
            return

        logger.info(f"exec ext cmd:{ext_cmd}!")

        # 为命令执行添加超时机制
        command_exception = [None]
        command_success = [False]

        def execute_command():
            try:
                self.sim.controllable_add(ext_cmd, object_state)
                command_success[0] = True
            except Exception as e:
                command_exception[0] = e

        # 使用线程执行命令，设置超时
        command_thread = threading.Thread(target=execute_command, daemon=True)
        command_thread.start()
        command_thread.join(timeout=10)  # 10秒超时

        if command_thread.is_alive():
            logger.error(f"执行扩展命令超时: {ext_cmd}")
            # 对于超时的命令，我们不抛出异常，而是记录错误并继续
        elif command_exception[0] is not None:
            logger.error(f"执行扩展命令失败: {ext_cmd}, 错误: {command_exception[0]}")
            # 同样不抛出异常，记录错误并继续
        elif command_success[0]:
            logger.info(f"exec ext cmd:{ext_cmd} end!")
        else:
            logger.warning(f"执行扩展命令状态未知: {ext_cmd}")

    def exec_trajectoy_cmd(self, traj_data):
        """
        执行轨迹命令。

        参数:
            traj_data: 轨迹数据
        """
        logger.info("exec_trajectoy_cmd")
        if self.sim == None or not self.sim.remote.isAlive():
            return
        self.sim.remote.command("simulator/routing_trajectory", traj_data)
        logger.info("exec_trajectoy_cmd ok")
