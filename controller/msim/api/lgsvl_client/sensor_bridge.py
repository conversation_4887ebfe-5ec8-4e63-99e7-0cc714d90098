#!/usr/bin/python3
# -*- coding: utf-8 -*-

from enum import IntEnum
from math import pi
from loguru import logger
from util.lgsvl_utils import quaternion2rpy, unity2real_pos
from util.redis_util import get_string, key_exists, set_string
from task_env import (
    get_map_origin,
    get_map_version,
    get_use_dpqp_node,
    get_vehicle_type,
    get_special_type,
    get_bus_type,
)
import rospy
import time
import os
import sys

# lgsvl sensor message
from lgsvl_msgs.msg import CanBusData
from lgsvl_msgs.msg import VehicleControlData
from lgsvl_msgs.msg import VehicleStateData
from lgsvl_msgs.msg import SignalArray
from lgsvl_msgs.msg import Signal, ObstacleContour

sys.path.insert(
    0, os.path.expanduser("~/catkin_ws/src/simulation/sim_lgsvl_client/node/lib")
)
from control_msgs.msg import VehicleStatus

# general sensor message
from nav_msgs.msg import Odometry
from sensor_msgs.msg import Imu
from std_msgs.msg import Int32, String
from lgsvl_msgs.msg import RefPoint

# autopilot msgs
from autopilot_msgs.msg import BinaryData

from proto.localization_pb2 import Localization
from proto.hadmap_pb2 import MapMsg, Object as MapObject
from proto.traffic_light_pb2 import TrafficLights

from visualization_msgs.msg import MarkerArray

seq = 1


# traffic lights color
class TLColor(IntEnum):
    GREEN = 0
    YELLOW = 1
    RED = 2


class TCLObject(object):
    def __init__(self, x: float, y: float, z: float):
        self.x = x
        self.y = y
        self.z = z

    def __init__(self, signal: Signal):
        self.x = signal.bbox.position.position.x
        self.y = signal.bbox.position.position.y
        self.z = signal.bbox.position.position.z
        self.label = signal.label

    def __eq__(self, other):
        return self.x == other.x

    def __ne__(self, other):
        return self.x != other.x

    def __gt__(self, other):
        return self.x > other.x

    def __ge__(self, other):
        return self.x >= other.x

    def __lt__(self, other):
        return self.x < other.x

    def __le__(self, other):
        return self.x <= other.x


class SensorBridge(object):

    def __init__(self):
        # AD stack init
        rospy.init_node("autopilot_stack")
        # entering autopilot mode by rosparam
        for i in range(20):
            rospy.set_param("/autopilot/PilotMode", 1)
            rospy.set_param("/autopilot/SetFwiperOn", 1)
            time.sleep(0.2)
            if rospy.get_param("/autopilot/PilotMode") == 0:
                continue
            else:
                break
        else:
            rospy.logerr("Failed to enter AUTO_DRIVE mode, program terminated!")
            exit(0)

        self.autopilot_pub = rospy.Publisher(
            "/autopilot/AutoPilotCmd", Int32, queue_size=1
        )
        self.ego_state_pub = rospy.Publisher(
            "/chassis/vehicle_state", BinaryData, queue_size=1
        )
        self.ego_state_refactor_pub = rospy.Publisher(
            "/chassis/chassis_states", BinaryData, queue_size=1
        )
        self.FSM_state_pub = rospy.Publisher("/fsm/fsm_state", BinaryData, queue_size=1)
        # 2.3x之后的bus才需要
        if get_vehicle_type() != "taxi" and get_map_version() != "2030":
            self.ego_state_panel_pub = rospy.Publisher(
                "/vehicle/status/panel", VehicleStatus, queue_size=1
            )
        self.ego_loc_pub = rospy.Publisher(
            "/localization/global", BinaryData, queue_size=1
        )
        self.obstacle_pub = rospy.Publisher(
            "/perception/fusion/obstacles", BinaryData, queue_size=1
        )
        self.ego_ctrl_pub = rospy.Publisher(
            "/lgsvl_control", VehicleControlData, queue_size=1, tcp_nodelay=True
        )
        self.ego_stat_pub = rospy.Publisher(
            "/vehicle_state", VehicleStateData, queue_size=1
        )
        self.traffic_light_pub = rospy.Publisher(
            "/perception/camera/trfclts_state", String, queue_size=1
        )
        self.preception_traffic_light_pub = rospy.Publisher(
            "/perception/fusion/trfclts_state", String, queue_size=1
        )
        # 2.6之前由模拟器传停止线信息
        self.stop_line_pub = rospy.Publisher(
            "/hadmap/stop_line", BinaryData, queue_size=1
        )
        if get_vehicle_type() == "taxi" and get_map_version() >= "2060":
            self.hadmap_engine_stop_line_pub = rospy.Publisher(
                "/hadmap_engine/stop_line", BinaryData, queue_size=1
            )
        # bus 510起添加chassis25_pub
        self.chassis25_pub = rospy.Publisher(
            "/chassis/chassis2025", BinaryData, queue_size=1
        )

        # 障碍物角点信息，发送给lgsvl
        self.lgsvl_obstacle_pub = rospy.Publisher(
            "/lgsvl/obstacle_contour", ObstacleContour, queue_size=1
        )

        self.fusion_viz = rospy.Publisher(
            "/perception/fusion/fusion_viz", MarkerArray, queue_size=1
        )

        # ros parameter
        self.maximum_steering = 500.0
        self.steering_ratio = 18.0
        vt = get_vehicle_type()
        rospy.loginfo("type = {}".format(vt))
        if vt == "taxi":
            self.maximum_steering = 500.0
            self.steering_ratio = 18.0
        if vt == "bus":
            self.maximum_steering = 700.0
            self.steering_ratio = 22.0
        if get_bus_type() == "B2":
            rospy.loginfo("type = B2")
            self.maximum_steering = 900.0
            self.steering_ratio = 21.0
        if get_special_type() == "sweeper":
            rospy.loginfo("type = sweeper")
            self.maximum_steering = 790.0
            self.steering_ratio = 18.0

        # shared state variable
        self.last_control_time = rospy.Time.now()

        self.ego_acceleration_x = 0.0
        self.ego_acceleration_y = 0.0
        self.ego_acceleration_z = 0.0
        self.ego_angular_x = 0.0
        self.ego_angular_y = 0.0
        self.ego_angular_z = 0.0

        self.ego_odom_yaw = 0.0
        self.ego_odom_roll = 0.0
        self.ego_odom_pitch = 0.0

        self.longitude = 0.0
        self.latitude = 0.0

        self.last_red_time = rospy.Time.now().to_sec() - 9999.0
        self.signal_time_to_hold = -1
        self.map_stop_line = MapObject()
        self.longitude_speed = 0.0
        self.utm_zone_code = None

        self.ego_utm_x = 0.0
        self.ego_utm_y = 0.0

        self.last_obstacle_time = 0
        self.static_dict = {}

        self.is_auto_driving = False

    def on_control(self, msg):
        # ego control command
        from proto.control_command_pb2 import ControlCommand
        from proto.chassis_pb2 import LIGHT_FLASH, LIGHT_LEFT, LIGHT_RIGHT

        vt = get_vehicle_type()

        ctrl_command = ControlCommand()
        ctrl_command.ParseFromString(msg.data)
        # set autopilot mode if controller is on
        if ctrl_command.pilot_mode == 0:
            auto_msg = Int32()
            auto_msg.data = 1
            self.autopilot_pub.publish(auto_msg)

        set_string("cmd_pilot_mode", ctrl_command.pilot_mode, 1)

        self.last_control_time = rospy.Time.now()
        vehicle_ctrl_cmd = VehicleControlData()
        # bus和taxi有油门开度和brake

        if vt == "taxi" or vt == "bus" or "sweeper" == get_special_type():
            vehicle_ctrl_cmd.acceleration_pct = ctrl_command.throttle / 100.0
            # bus暂时除100吧，特殊原因，bus huzhanyi建议
            vehicle_ctrl_cmd.braking_pct = (
                ctrl_command.brake / 100 if vt == "bus" else ctrl_command.brake
            )
            # if vt  == 'taxi' and get_string('break.factor') != None:
            #     if self.static_dict.get('taxi_break_factor', None) is None:
            #         factor = float(get_string('break.factor'))
            #         logger.info('{} break factor:{}', get_vehicle_vendor(), factor)
            #         self.static_dict['taxi_break_factor'] = factor
            #     vehicle_ctrl_cmd.braking_pct = vehicle_ctrl_cmd.braking_pct * self.static_dict['taxi_break_factor']
        elif vt == "special_vehicle":
            vehicle_ctrl_cmd.acceleration_pct = ctrl_command.accel

        if vt == "taxi":
            pct = self.ctr_acc_trans(ctrl_command.accel)
            if pct != None:
                if pct > 0:
                    vehicle_ctrl_cmd.acceleration_pct = pct
                    vehicle_ctrl_cmd.braking_pct = 0
                else:
                    vehicle_ctrl_cmd.acceleration_pct = 0
                    vehicle_ctrl_cmd.braking_pct = abs(pct)

        vehicle_ctrl_cmd.ctr_cmd_acc = ctrl_command.accel
        vehicle_ctrl_cmd.target_wheel_angle = (
            -ctrl_command.steering / self.steering_ratio * pi / 180
        )
        vehicle_ctrl_cmd.header.stamp = self.last_control_time
        vehicle_ctrl_cmd.header.frame_id = "base_link"
        self.ego_ctrl_pub.publish(vehicle_ctrl_cmd)
        vehicle_stat_cmd = VehicleStateData()
        vehicle_stat_cmd.header.stamp = self.last_control_time
        vehicle_stat_cmd.header.frame_id = "base_link"
        vehicle_stat_cmd.header.seq = ctrl_command.header.seq
        if ctrl_command.light == LIGHT_LEFT:
            vehicle_stat_cmd.blinker_state = VehicleStateData.BLINKERS_LEFT
        elif ctrl_command.light == LIGHT_RIGHT:
            vehicle_stat_cmd.blinker_state = VehicleStateData.BLINKERS_RIGHT
        elif ctrl_command.light == LIGHT_FLASH:
            vehicle_stat_cmd.blinker_state = VehicleStateData.BLINKERS_HAZARD
        else:
            vehicle_stat_cmd.blinker_state = VehicleStateData.BLINKERS_OFF
        self.ego_stat_pub.publish(vehicle_stat_cmd)

    def on_control2025(self, msg):
        # ego control command
        from proto.control2025_pb2 import ControlCommand as Control2025
        from proto.chassis2025_pb2 import LIGHT_FLASH, LIGHT_LEFT, LIGHT_RIGHT

        vt = get_vehicle_type()

        ctrl_command2025 = Control2025()
        ctrl_command2025.ParseFromString(msg.data)
        # set autopilot mode if controller is on
        if not ctrl_command2025.chassis_wire_cmd.chassis_wire_enable:
            auto_msg = Int32()
            auto_msg.data = 1
            self.autopilot_pub.publish(auto_msg)

        set_string(
            "cmd_pilot_mode",
            int(ctrl_command2025.chassis_wire_cmd.chassis_wire_enable),
            1,
        )

        self.last_control_time = rospy.Time.now()
        vehicle_ctrl_cmd = VehicleControlData()
        # bus和taxi有油门开度和brake

        if vt == "taxi" or vt == "bus" or "sweeper" == get_special_type():
            vehicle_ctrl_cmd.acceleration_pct = (
                ctrl_command2025.drive_system_cmd.throttle / 100.0
            )
            # bus暂时除100吧，特殊原因，bus huzhanyi建议
            vehicle_ctrl_cmd.braking_pct = (
                ctrl_command2025.brake_system_cmd.brake_pedal / 100
                if vt == "bus"
                else ctrl_command2025.brake_system_cmd.brake_pedal
            )
            # if vt  == 'taxi' and get_string('break.factor') != None:
            #     if self.static_dict.get('taxi_break_factor', None) is None:
            #         factor = float(get_string('break.factor'))
            #         logger.info('{} break factor:{}', get_vehicle_vendor(), factor)
            #         self.static_dict['taxi_break_factor'] = factor
            #     vehicle_ctrl_cmd.braking_pct = vehicle_ctrl_cmd.braking_pct * self.static_dict['taxi_break_factor']
        elif vt == "special_vehicle":
            vehicle_ctrl_cmd.acceleration_pct = (
                ctrl_command2025.drive_system_cmd.accel / 100
            )
            vehicle_ctrl_cmd.braking_pct = abs(
                ctrl_command2025.brake_system_cmd.brake_acceleration
            )
        if vt == "taxi":
            if ctrl_command2025.brake_system_cmd.brake_acceleration < 0:
                pct = self.ctr_acc_trans(
                    ctrl_command2025.brake_system_cmd.brake_acceleration
                )
                if pct != None:
                    vehicle_ctrl_cmd.acceleration_pct = 0
                    vehicle_ctrl_cmd.braking_pct = abs(pct)
            elif ctrl_command2025.drive_system_cmd.accel > 0:
                pct = self.ctr_acc_trans(ctrl_command2025.drive_system_cmd.accel)
                if pct != None:
                    vehicle_ctrl_cmd.acceleration_pct = pct
                    vehicle_ctrl_cmd.braking_pct = 0

        if ctrl_command2025.brake_system_cmd.brake_acceleration < 0:
            vehicle_ctrl_cmd.ctr_cmd_acc = (
                ctrl_command2025.brake_system_cmd.brake_acceleration
            )
        elif ctrl_command2025.drive_system_cmd.accel > 0:
            vehicle_ctrl_cmd.ctr_cmd_acc = ctrl_command2025.drive_system_cmd.accel
        else:
            vehicle_ctrl_cmd.ctr_cmd_acc = 0

        vehicle_ctrl_cmd.target_wheel_angle = (
            -ctrl_command2025.steer_system_cmd.steer_wheel_angle
            / self.steering_ratio
            * pi
            / 180
        )
        vehicle_ctrl_cmd.header.stamp = self.last_control_time
        vehicle_ctrl_cmd.header.frame_id = "base_link"
        self.ego_ctrl_pub.publish(vehicle_ctrl_cmd)
        vehicle_stat_cmd = VehicleStateData()
        vehicle_stat_cmd.header.stamp = self.last_control_time
        vehicle_stat_cmd.header.frame_id = "base_link"
        vehicle_stat_cmd.header.seq = ctrl_command2025.header.seq
        if ctrl_command2025.bcm_system_cmd.turn_light == LIGHT_LEFT:
            vehicle_stat_cmd.blinker_state = VehicleStateData.BLINKERS_LEFT
        elif ctrl_command2025.bcm_system_cmd.turn_light == LIGHT_RIGHT:
            vehicle_stat_cmd.blinker_state = VehicleStateData.BLINKERS_RIGHT
        elif ctrl_command2025.bcm_system_cmd.turn_light == LIGHT_FLASH:
            vehicle_stat_cmd.blinker_state = VehicleStateData.BLINKERS_HAZARD
        else:
            vehicle_stat_cmd.blinker_state = VehicleStateData.BLINKERS_OFF
        self.ego_stat_pub.publish(vehicle_stat_cmd)
        rospy.loginfo_throttle(
            1,
            "2025vehicle_stat_cmd:{} \n 2025vehicle_ctrl_cmd:{}".format(
                vehicle_stat_cmd, vehicle_ctrl_cmd
            ),
        )

    def ctr_acc_trans(self, ctr_acc):
        expact_ctr_acc = round(ctr_acc, 2)
        cur_v = round(self.longitude_speed, 2)
        # initV:{expactA:pct}
        acc_trans_dict = self.static_dict.get("acc_trans_dict", {})
        break_trans_dict = self.static_dict.get("break_trans_dict", {})
        if len(acc_trans_dict) == 0:
            with open(
                "/home/<USER>/catkin_ws/src/simulation/sim_lgsvl_client/node/conf/hq_ctr_map"
            ) as f:
                for line in f.readlines():
                    splited = line.replace("\n", "").split(" ")
                    if len(splited) != 3:
                        continue
                    # logger.debug('acc_trans_dict splited {}', splited)
                    initV = splited[0]
                    expactA = splited[1]
                    pct = splited[2]
                    # if '-' in expactA and '-' not in pct:
                    #     continue
                    # if '-' not in expactA and '-' in pct:
                    #     continue
                    if "-" in pct or "-" in expactA:
                        break_trans_dict.setdefault(initV, {})
                        break_trans_dict.get(initV)[expactA] = pct
                    else:
                        acc_trans_dict.setdefault(initV, {})
                        acc_trans_dict.get(initV)[expactA] = pct
                self.static_dict["acc_trans_dict"] = acc_trans_dict
                self.static_dict["break_trans_dict"] = break_trans_dict
                logger.debug(
                    "acc_trans_dict {}\n break_trans_dict {}",
                    acc_trans_dict,
                    break_trans_dict,
                )

        t = 10000000
        acc_throttle_dict = None
        target_dict = acc_trans_dict if expact_ctr_acc > 0 else break_trans_dict
        for k, v in target_dict.items():
            initV = float(k)
            delta = abs(cur_v - initV)
            if delta < t:
                acc_throttle_dict = v
                t = delta
                # logger.debug('initV:{} delta:{} acc_throttle_dict:{}', initV, delta, acc_throttle_dict)
        # logger.debug('curV:{} acc_throttle_dict:{}', cur_v, acc_throttle_dict)
        final_pct = None
        t = 10000000
        for k, v in acc_throttle_dict.items():
            expectA = float(k)
            pct = float(v)
            # if expact_ctr_acc >= 0 and pct < 0:
            #     continue
            # if expact_ctr_acc < 0 and pct > 0:
            #     continue
            if abs(expact_ctr_acc - expectA) < t:
                final_pct = pct
                t = abs(expact_ctr_acc - expectA)
        # logger.debug('curV:{} expactA:{} pct:{} acc_throttle_dict:{}', cur_v, expact_ctr_acc, final_pct, acc_throttle_dict)
        return final_pct if final_pct != None else None

    def on_control_ref(self, msg):
        # ego control command
        vt = get_vehicle_type()
        from proto.control_command_ref_pb2 import ControlCommand
        from proto.control_command_ref_pb2 import (
            CONTROL_TURN_LIGHT_FLASH,
            CONTROL_TURN_LIGHT_LEFT,
            CONTROL_TURN_LIGHT_RIGHT,
            WIRE_CONTROL_MANUAL,
        )

        ctrl_command = ControlCommand()
        ctrl_command.ParseFromString(msg.data)
        # set autopilot mode if controller is on
        if (
            ctrl_command.autopilot_assistance_command.chassis_wire_control_mode_cmd
            == WIRE_CONTROL_MANUAL
        ):
            auto_msg = Int32()
            auto_msg.data = 1
            self.autopilot_pub.publish(auto_msg)

        self.static_dict["auto_mode"] = (
            False
            if ctrl_command.autopilot_assistance_command.chassis_wire_control_mode_cmd
            == WIRE_CONTROL_MANUAL
            else True
        )

        self.last_control_time = rospy.Time.now()
        vehicle_ctrl_cmd = VehicleControlData()
        # bus和taxi有油门开度和brake

        vehicle_ctrl_cmd.acceleration_pct = (
            ctrl_command.driving_system_control_command.throttle_cmd / 100.0
        )
        vehicle_ctrl_cmd.braking_pct = abs(
            ctrl_command.brake_system_control_command.brake_deceleration_cmd
        )

        # vehicle_ctrl_cmd.ctr_cmd_acc = ctrl_command.driving_system_control_command.acceleration_cmd

        # vehicle_ctrl_cmd.target_wheel_angle  = -ctrl_command.lateral_system_control_command.steering_cmd / self.steering_ratio * pi / 180
        vehicle_ctrl_cmd.header.stamp = self.last_control_time
        vehicle_ctrl_cmd.header.frame_id = "base_link"
        self.ego_ctrl_pub.publish(vehicle_ctrl_cmd)
        vehicle_stat_cmd = VehicleStateData()
        vehicle_stat_cmd.header.stamp = self.last_control_time
        vehicle_stat_cmd.header.frame_id = "base_link"

        if (
            ctrl_command.bcm_system_control_command.trun_light_cmd
            == CONTROL_TURN_LIGHT_LEFT
        ):
            vehicle_stat_cmd.blinker_state = VehicleStateData.BLINKERS_LEFT
        elif (
            ctrl_command.bcm_system_control_command.trun_light_cmd
            == CONTROL_TURN_LIGHT_RIGHT
        ):
            vehicle_stat_cmd.blinker_state = VehicleStateData.BLINKERS_RIGHT
        elif (
            ctrl_command.bcm_system_control_command.trun_light_cmd
            == CONTROL_TURN_LIGHT_FLASH
        ):
            vehicle_stat_cmd.blinker_state = VehicleStateData.BLINKERS_HAZARD
        else:
            vehicle_stat_cmd.blinker_state = VehicleStateData.BLINKERS_OFF
        self.ego_stat_pub.publish(vehicle_stat_cmd)
        rospy.loginfo_throttle(
            1,
            "ctrl_command_ref:{} \n vehicle_ctrl_cmd:{}".format(
                ctrl_command, vehicle_ctrl_cmd
            ),
        )

    def on_ego_state(self, msg):
        # ego states: steering, speed, gear, imu_x, throttle, brake, longitude, latitude
        now = rospy.Time.now()
        self.longitude = msg.gps_longitude
        self.latitude = msg.gps_latitude
        self.longitude_speed = msg.speed_mps

        if self.utm_zone_code is None:
            self.utm_zone_code = int(self.longitude / 6) + 31

        # 借此通道发送fsm消息
        from proto.fsm2024_pb2 import FSMStateMsg

        FSM_state_msg = FSMStateMsg()
        global seq
        seq = seq + 1
        FSM_state_msg.header.stamp.sec = now.secs
        FSM_state_msg.header.stamp.nsec = now.nsecs
        FSM_state_msg.header.seq = seq
        FSM_state_msg.header.frame_id = "fsm.FSMStateMsg"
        FSM_state_msg.header.module_name = "fsm.FSMStateMsg"
        FSM_state_msg.function_state = 6
        if get_vehicle_type() == "bus" and get_map_version() < "4500":
            FSM_state_msg.function_state = 3
        FSM_state_msg.fsm_safety_stop_mode = 0
        FSM_state_msg.active_mode = 1
        FSM_state_msg.new_msg_flag = True
        FSM_state_msg.pilot_standby_flag = True
        FSM_state_msg.parallel_standby_flag = True
        FSM_state_msg.simulator_standby_flag = True
        # protobuf
        FSM_state_data = FSM_state_msg.SerializeToString()
        FSM_state = BinaryData()

        FSM_state.header.stamp = now
        FSM_state.header.seq = 0
        FSM_state.header.frame_id = "fsm.FSMStateMsg"
        FSM_state.name = "fsm.FSMStateMsg"

        FSM_state.size = len(FSM_state_data)
        FSM_state.data = FSM_state_data
        # publish
        self.FSM_state_pub.publish(FSM_state)
        rospy.loginfo_throttle(1, "seq:{},FSM_state_msg:{}".format(seq, FSM_state_msg))

        from proto.vehicle_state_pb2 import VehicleState

        vehicle_state_msg = VehicleState()
        vehicle_state_msg.header.stamp.sec = now.secs
        vehicle_state_msg.header.stamp.nsec = now.nsecs
        vehicle_state_msg.header.frame_id = "chassis_base"
        vehicle_state_msg.header.module_name = "chassis_VehicleState"
        # 2.3x版本或新版本taxi
        vehicle_state_msg.steering = -msg.steer_pct * self.maximum_steering
        # if not (get_vehicle_type() == 'taxi' or get_map_version() == '2030'):
        # vehicle_state_msg.pilot_mode         =  1
        vehicle_state_msg.pilot_mode = 1 if get_string("cmd_pilot_mode") == "1" else 0
        vehicle_state_msg.speed = msg.speed_mps
        vehicle_state_msg.gear = msg.selected_gear
        # 2.3x不需要
        if get_map_version() != "2030":
            if vehicle_state_msg.gear == 1:
                vehicle_state_msg.gear = 4
        if "sweeper" in get_special_type():
            # if (vehicle_state_msg.gear == 0):
            if vehicle_state_msg.speed == 0:
                vehicle_state_msg.speed = 0
                vehicle_state_msg.gear = 1
                vehicle_state_msg.epb = 1
            else:
                vehicle_state_msg.epb = 2
            # vehicle_state_msg.speed = 0
        vehicle_state_msg.accel = self.ego_acceleration_x
        vehicle_state_msg.throttle = msg.throttle_pct
        vehicle_state_msg.brake = msg.brake_pct
        # protobuf
        vehicle_state_data = vehicle_state_msg.SerializeToString()
        vehicle_state = BinaryData()
        vehicle_state.header.stamp = now
        vehicle_state.header.frame_id = "chassis_base"
        vehicle_state.name = "chassis.VehicleState"
        vehicle_state.size = len(vehicle_state_data)
        vehicle_state.data = vehicle_state_data
        # publish
        self.ego_state_pub.publish(vehicle_state)

        # chassis2025
        from proto.chassis2025_pb2 import (
            ChassisStates as Chassis25,
        )  # 避免与清扫车老接口同名

        chassis25_msg = Chassis25()
        chassis25_msg.header.stamp.sec = now.secs
        chassis25_msg.header.stamp.nsec = now.nsecs
        chassis25_msg.header.frame_id = "chassis_base"
        chassis25_msg.header.module_name = "chassis25"

        chassis25_msg.chassis_wire_states.chassis_wire_enable = True
        chassis25_msg.drive_system_states.drive_enable = True
        chassis25_msg.brake_system_states.brake_enable = True
        chassis25_msg.steer_system_states.steer_enable = True
        chassis25_msg.gear_system_states.gear_enable = True
        chassis25_msg.epb_system_states.epb_enable = True

        chassis25_msg.steer_system_states.steer_wheel_angle = (
            -msg.steer_pct * self.maximum_steering
        )
        chassis25_msg.vehicle_motion_states.speed = msg.speed_mps
        chassis25_msg.gear_system_states.gear = msg.selected_gear
        if get_map_version() != "2030":
            if msg.selected_gear == 1:  # gear N
                chassis25_msg.gear_system_states.gear = 4  # gear D
        if "sweeper" in get_special_type():
            if abs(chassis25_msg.vehicle_motion_states.speed) < 1e-6:
                chassis25_msg.vehicle_motion_states.speed = 0
                chassis25_msg.gear_system_states.gear = 1
                chassis25_msg.epb_system_states.epb = 1  # locked
            else:
                chassis25_msg.epb_system_states.epb = 2  # released
        chassis25_msg.vehicle_motion_states.acceleration = self.ego_acceleration_x
        chassis25_msg.drive_system_states.throttle = msg.throttle_pct
        chassis25_msg.brake_system_states.brake_pedal_position = msg.brake_pct

        # protobuf
        chassis25_data = chassis25_msg.SerializeToString()
        chassis25_state = BinaryData()
        chassis25_state.header.stamp = now
        chassis25_state.header.frame_id = "chassis_base"
        chassis25_state.name = "chassis25.ChassisStates"
        chassis25_state.size = len(chassis25_data)
        chassis25_state.data = chassis25_data

        self.chassis25_pub.publish(chassis25_state)
        rospy.loginfo_throttle(3, "chassis25_msg:{}".format(chassis25_msg))

        # 2.3x之后的bus才需要
        if get_vehicle_type() != "taxi" and get_map_version() != "2030":
            vehicle_status_ = VehicleStatus()
            vehicle_status_.pilot_mode = 1
            vehicle_status_.steering = vehicle_state_msg.steering
            vehicle_status_.speed = vehicle_state_msg.speed
            vehicle_status_.accel = vehicle_state_msg.accel
            # vehicle_status_.longitude_driving_mode = vehicle_state_msg.longitude_driving_mode
            # vehicle_status_.eps_steering_mode = vehicle_state_msg.eps_steering_mode
            vehicle_status_.gear = vehicle_state_msg.gear
            self.ego_state_panel_pub.publish(vehicle_status_)

        set_string("ego_longitude_speed", msg.speed_mps)
        rospy.loginfo_throttle(
            3,
            "/chassis/vehicle_state \n vehicle_state_msg:{}".format(vehicle_state_msg),
        )

    def on_ego_state_ref(self, msg):
        # ego states: steering, speed, gear, imu_x, throttle, brake, longitude, latitude
        now = rospy.Time.now()
        self.longitude = msg.gps_longitude
        self.latitude = msg.gps_latitude
        self.longitude_speed = msg.speed_mps

        if self.utm_zone_code is None:
            self.utm_zone_code = int(self.longitude / 6) + 31
        from proto.chassis_states_pb2 import (
            ChassisStates,
            EPB_STATE_LOCKED,
            EPB_STATE_RELEASED,
            GEAR_N,
            MODE_AUTO_DRIVE,
        )

        chassis_states = ChassisStates()
        chassis_states.header.stamp.sec = now.secs
        chassis_states.header.stamp.nsec = now.nsecs
        chassis_states.header.frame_id = "chassis_base"
        chassis_states.header.module_name = "chassis.ChassisStates"

        chassis_states.steer_system_states.steering_wheel_angle = (
            -msg.steer_pct * self.maximum_steering
        )
        chassis_states.vehicle_motion_states.speed = msg.speed_mps
        chassis_states.gear_system_states.gear_position = msg.selected_gear
        # chassis_states.brake_system_states.brake_pedal_state = False

        chassis_states.driving_system_states.driving_enable_state = 1
        chassis_states.brake_system_states.brake_enable_sts = 1
        chassis_states.steer_system_states.steer_enable_state = 1
        chassis_states.gear_system_states.gear_enable_state = 1
        chassis_states.epb_system_states.epb_enable_state = 1
        chassis_states.chassis_autopilot_assistance_information.chassis_pilot_mode_state = (
            MODE_AUTO_DRIVE
        )
        if get_map_version() != "2030":
            if chassis_states.gear_system_states.gear_position == 1:
                chassis_states.gear_system_states.gear_position = 4

        if "sweeper" in get_special_type():
            # if (vehicle_state_msg.gear == 0):

            if (
                abs(chassis_states.vehicle_motion_states.speed) < 1e-6
                and self.static_dict.get("auto_mode", False) == False
            ):
                chassis_states.vehicle_motion_states.speed = 0
                chassis_states.gear_system_states.gear_position = GEAR_N
                chassis_states.epb_system_states.epb_work_state = EPB_STATE_LOCKED
            else:
                chassis_states.epb_system_states.epb_work_state = EPB_STATE_RELEASED
        chassis_states.vehicle_motion_states.acceleration = self.ego_acceleration_x
        chassis_states.driving_system_states.throttle_response_position = (
            msg.throttle_pct
        )

        # chassis_states.brake_system_states.brake_system_states              =  msg.brake_pct
        # protobuf
        vehicle_state_data = chassis_states.SerializeToString()
        vehicle_state_bin = BinaryData()
        vehicle_state_bin.header.stamp = now
        vehicle_state_bin.header.frame_id = "chassis_base"
        vehicle_state_bin.name = "chassis.ChassisStates"
        vehicle_state_bin.size = len(vehicle_state_data)
        vehicle_state_bin.data = vehicle_state_data
        # publish
        self.ego_state_refactor_pub.publish(vehicle_state_bin)
        set_string("ego_longitude_speed", msg.speed_mps)
        rospy.loginfo_throttle(
            1,
            "ego_state_pub_ref msg:{} \n\nvehicle_state_msg:{}".format(
                msg, chassis_states
            ),
        )
        del ChassisStates

    def on_imu(self, msg):
        (
            seq,
            self.ego_acceleration_x,
            self.ego_acceleration_y,
            self.ego_acceleration_z,
        ) = (
            msg.header.seq,
            msg.linear_acceleration.x,
            msg.linear_acceleration.y,
            msg.linear_acceleration.z,
        )
        rospy.loginfo_throttle(
            1,
            "imuseq:{},msg.linear_acceleration.x:{}".format(
                seq, msg.linear_acceleration.x
            ),
        )
        self.ego_angular_x, self.ego_angular_y, self.ego_angular_z = (
            msg.angular_velocity.x,
            msg.angular_velocity.y,
            msg.angular_velocity.z,
        )

    def on_gps_odom(self, msg):
        # ego localization global
        if self.utm_zone_code is None:
            return
        now = rospy.Time.now()
        self.ego_odom_roll, self.ego_odom_pitch, self.ego_odom_yaw = quaternion2rpy(
            msg.pose.pose.orientation.x,
            msg.pose.pose.orientation.y,
            msg.pose.pose.orientation.z,
            msg.pose.pose.orientation.w,
        )
        localization_msg = Localization()
        localization_msg.header.stamp.sec = now.secs
        localization_msg.header.stamp.nsec = now.nsecs
        localization_msg.header.frame_id = "utm"
        localization_msg.header.module_name = "localization.Localization"
        localization_msg.position.x = msg.pose.pose.position.x
        localization_msg.position.y = msg.pose.pose.position.y
        localization_msg.position.z = msg.pose.pose.position.z
        localization_msg.longitude = self.longitude
        localization_msg.latitude = self.latitude
        localization_msg.yaw = self.ego_odom_yaw % (2 * pi)
        localization_msg.roll = self.ego_odom_roll % (2 * pi)
        localization_msg.pitch = self.ego_odom_pitch % (2 * pi)
        localization_msg.yaw_v = self.ego_angular_z
        localization_msg.pitch_v = self.ego_angular_y
        localization_msg.roll_v = self.ego_angular_x
        localization_msg.utm_zone = self.utm_zone_code
        localization_msg.longitudinal_v = self.longitude_speed
        localization_msg.horizontal_v = self.longitude_speed
        localization_msg.vertical_a = self.ego_acceleration_x
        localization_msg.gnss_status = 42
        localization_msg.loc_status = 0
        # if get_vehicle_type() == 'taxi':
        localization_msg.longitudinal_a = self.ego_acceleration_x
        self.ego_utm_x = localization_msg.position.x
        self.ego_utm_y = localization_msg.position.y

        # protobuf
        localization_data = localization_msg.SerializeToString()
        localization = BinaryData()
        localization.header.stamp = now
        localization.header.frame_id = "utm"
        localization.name = "localization.Localization"
        localization.size = len(localization_data)
        localization.data = localization_data
        # publish
        self.ego_loc_pub.publish(localization)
        self.static_dict["last_gps_msg"] = msg
        if key_exists("obs_published") == False:
            obstacle_contour = ObstacleContour()
            obstacle_contour.header.stamp = rospy.Time.now()
            obstacle_contour.header.frame_id = "base_link"
            ego_utm_pt = RefPoint()
            ego_utm_pt.x = self.ego_utm_x
            ego_utm_pt.y = self.ego_utm_y
            ego_utm_pt.z = 3
            obstacle_contour.utms.append(ego_utm_pt)
            # self.lgsvl_obstacle_pub.publish(obstacle_contour)
        rospy.loginfo_throttle(
            3, "/localization/global \n sim localization {}".format(localization_msg)
        )
        set_string("ego_utm_xy", "{}:{}".format(self.ego_utm_x, self.ego_utm_y))
        set_string("ego_odom_yaw", self.ego_odom_yaw)

    def on_signals_detection(self, msg):
        # signals array data
        red_signals_list = []
        green_signals_list = []
        yellow_signals_list = []
        # rospy.loginfo('on_signals_detection {}'.format(msg.signals))
        for signal in msg.signals:
            if "green" in signal.label:
                green_signals_list.append(TCLObject(signal))
            elif "red" in signal.label:
                red_signals_list.append(TCLObject(signal))
            elif "yellow" in signal.label:
                yellow_signals_list.append(TCLObject(signal))
            mox, moy = get_map_origin()
            use_lgsvl_stopline = get_string("lgsvl_stopline") == "true"
            if (
                signal.stopline != None
                and signal.stopline.position != None
                and mox != None
                and use_lgsvl_stopline
            ):
                slp = signal.stopline.position
                stopline_pos = unity2real_pos(slp.x, slp.y, slp.z)
                # map message
                map_msg = MapMsg()
                now = rospy.Time.now()
                map_msg.header.stamp.sec = now.secs
                map_msg.header.stamp.nsec = now.nsecs
                map_msg.header.frame_id = "stop"
                map_msg.header.module_name = "hadmap.MapMsg"
                stopline = map_msg.map.objects.add()
                # stopline.pkid = 1
                stopline.type = 21
                stopline.text = "Stop"
                # stopline.attribute = "straight"
                p = stopline.geom.add()

                stopline.geom[0].x = stopline_pos[0] + mox
                stopline.geom[0].y = stopline_pos[1] + moy
                stopline.geom[0].z = stopline_pos[2]
                stopline.pkid = 1

                # protobuf
                stop_line_data = map_msg.SerializeToString()
                stop_line_proto = BinaryData()
                stop_line_proto.header.stamp = now
                stop_line_proto.header.frame_id = "stop"
                stop_line_proto.name = "hadmap.MapMsg"
                stop_line_proto.size = len(stop_line_data)
                stop_line_proto.data = stop_line_data
                # if get_vehicle_type() == 'taxi' and get_map_version() >= '2.6':
                # 使用dpqp新轨迹表示启动了planning260，会使用新的停止线topic
                if get_use_dpqp_node() == True:
                    self.hadmap_engine_stop_line_pub.publish(stop_line_proto)
                else:
                    self.stop_line_pub.publish(stop_line_proto)

        green_signals_list.sort()
        red_signals_list.sort()
        yellow_signals_list.sort()

        self.traffic_lights_processing(
            red_signals_list, green_signals_list, yellow_signals_list
        )

    def on_stop_line_detection(self, msg):
        # map message
        map_msg = MapMsg()
        map_msg.ParseFromString(msg.data)
        # logger.debug('on_stop_line_detection {}'.format(map_msg))
        if map_msg.map is not None and len(map_msg.map.objects) == 1:
            # logger.debug('receive on_stop_line_detection:{}', map_msg)
            self.map_stop_line = map_msg.map.objects[0]
            self.map_stop_line.pkid = 1
        else:
            self.map_stop_line.pkid = 0
        rospy.loginfo_throttle(1, "on_stop_line_detection {}".format(map_msg))

    def on_hadmap_engine_stop_line_detection(self, msg):
        pass
        # logger.debug('on_hadmap_engine_stop_line_detection receive...', msg)
        # self.on_stop_line_detection(msg)

    def traffic_lights_processing(
        self, red_sig_list: list, green_sig_list: list, yellow_signals_list: list
    ):
        # turn left, turn right or go straight
        if self.map_stop_line.pkid > 0:
            direction = self.map_stop_line.attribute
        else:
            direction = ""

        now = rospy.Time.now()
        traffic_lights_msg = TrafficLights()
        traffic_lights_msg.header.stamp.sec = now.secs
        traffic_lights_msg.header.stamp.nsec = now.nsecs
        traffic_lights_msg.header.frame_id = "base_link"

        # 749以前版本灯态兼容
        state = (
            TLColor.RED
            if len(red_sig_list) > 0
            else (TLColor.GREEN if len(green_sig_list) > 0 else TLColor.YELLOW)
        )
        traffic_lights_msg.straight.id = 1
        traffic_lights_msg.straight.type = 1
        traffic_lights_msg.straight.state = (
            1 if state == TLColor.RED else (2 if state == TLColor.YELLOW else 3)
        )
        traffic_lights_msg.straight.duration = self.signal_time_to_hold

        # 749及以后版本灯态发送
        # 红灯
        if len(red_sig_list) > 0:
            for i in range(len(red_sig_list)):
                # 直行
                if "2" in red_sig_list[i].label:
                    traffic_lights_msg.straight.id = 1
                    traffic_lights_msg.straight.type = 1
                    traffic_lights_msg.straight.state = 1
                    traffic_lights_msg.straight.duration = self.signal_time_to_hold
                # 左转
                if "3" in red_sig_list[i].label:
                    traffic_lights_msg.left.id = 1
                    traffic_lights_msg.left.type = 1
                    traffic_lights_msg.left.state = 1
                    traffic_lights_msg.left.duration = self.signal_time_to_hold
                # 右转
                if "4" in red_sig_list[i].label:
                    traffic_lights_msg.right.id = 1
                    traffic_lights_msg.right.type = 1
                    traffic_lights_msg.right.state = 1
                    traffic_lights_msg.right.duration = self.signal_time_to_hold
                # 掉头
                if "5" in red_sig_list[i].label:
                    traffic_lights_msg.u_turn.id = 1
                    traffic_lights_msg.u_turn.type = 1
                    traffic_lights_msg.u_turn.state = 1
                    traffic_lights_msg.u_turn.duration = self.signal_time_to_hold

        # 绿灯
        if len(green_sig_list) > 0:
            for i in range(len(green_sig_list)):
                # 直行
                if "2" in green_sig_list[i].label:
                    traffic_lights_msg.straight.id = 1
                    traffic_lights_msg.straight.type = 1
                    traffic_lights_msg.straight.state = 3
                    traffic_lights_msg.straight.duration = self.signal_time_to_hold
                # 左转
                if "3" in green_sig_list[i].label:
                    traffic_lights_msg.left.id = 1
                    traffic_lights_msg.left.type = 1
                    traffic_lights_msg.left.state = 3
                    traffic_lights_msg.left.duration = self.signal_time_to_hold
                # 右转
                if "4" in green_sig_list[i].label:
                    traffic_lights_msg.right.id = 1
                    traffic_lights_msg.right.type = 1
                    traffic_lights_msg.right.state = 3
                    traffic_lights_msg.right.duration = self.signal_time_to_hold
                # 掉头
                if "5" in green_sig_list[i].label:
                    traffic_lights_msg.u_turn.id = 1
                    traffic_lights_msg.u_turn.type = 1
                    traffic_lights_msg.u_turn.state = 3
                    traffic_lights_msg.u_turn.duration = self.signal_time_to_hold

        # 黄灯
        if len(yellow_signals_list) > 0:
            for i in range(len(yellow_signals_list)):
                # 直行
                if "2" in yellow_signals_list[i].label:
                    traffic_lights_msg.straight.id = 1
                    traffic_lights_msg.straight.type = 1
                    traffic_lights_msg.straight.state = 2
                    traffic_lights_msg.straight.duration = self.signal_time_to_hold
                # 左转
                if "3" in yellow_signals_list[i].label:
                    traffic_lights_msg.left.id = 1
                    traffic_lights_msg.left.type = 1
                    traffic_lights_msg.left.state = 2
                    traffic_lights_msg.left.duration = self.signal_time_to_hold
                # 右转
                if "4" in yellow_signals_list[i].label:
                    traffic_lights_msg.right.id = 1
                    traffic_lights_msg.right.type = 1
                    traffic_lights_msg.right.state = 2
                    traffic_lights_msg.right.duration = self.signal_time_to_hold
                # 掉头
                if "5" in yellow_signals_list[i].label:
                    traffic_lights_msg.u_turn.id = 1
                    traffic_lights_msg.u_turn.type = 1
                    traffic_lights_msg.u_turn.state = 2
                    traffic_lights_msg.u_turn.duration = self.signal_time_to_hold

        # protobuf
        traffic_lights = String()
        traffic_lights.data = traffic_lights_msg.SerializeToString()

        # publish
        self.traffic_light_pub.publish(traffic_lights)
        self.preception_traffic_light_pub.publish(traffic_lights)

        map_msg = TrafficLights()
        map_msg.ParseFromString(traffic_lights.data)

        rospy.loginfo_throttle(
            1,
            "R={}, G={}, Y={}, Stop={}".format(
                len(red_sig_list),
                len(green_sig_list),
                len(yellow_signals_list),
                direction,
            ),
        )

    def listen(self):
        # autopilot topics
        rospy.Subscriber("/hadmap/stop_line", BinaryData, self.on_stop_line_detection)
        rospy.Subscriber(
            "/chassis/command",
            BinaryData,
            self.on_control,
            queue_size=1,
            tcp_nodelay=True,
        )
        rospy.Subscriber("/control/control_command", BinaryData, self.on_control_ref)
        rospy.Subscriber(
            "/control/control2025",
            BinaryData,
            self.on_control2025,
            queue_size=1,
            tcp_nodelay=True,
        )

        # 新的停止线topic，目前只有taxi260+版本有，不依赖打点文件
        if get_vehicle_type() == "taxi" and get_map_version() >= "2060":
            rospy.Subscriber(
                "/hadmap_engine/stop_line",
                BinaryData,
                self.on_hadmap_engine_stop_line_detection,
            )
        # lgsvl simulator topics
        if get_special_type() == "sweeper" and get_map_version() >= "2090":
            rospy.Subscriber(
                "/EgoState", CanBusData, self.on_ego_state_ref, queue_size=1
            )
        else:
            rospy.Subscriber("/EgoState", CanBusData, self.on_ego_state, queue_size=1)
        rospy.Subscriber("/imu", Imu, self.on_imu)
        # tcp_nodelay设置后定位才能达到100hz
        rospy.Subscriber(
            "/odom", Odometry, self.on_gps_odom, queue_size=1, tcp_nodelay=True
        )
        rospy.Subscriber("/signals", SignalArray, self.on_signals_detection)


if __name__ == "__main__":
    sb = SensorBridge()
    sb.listen()
    rospy.spin()
