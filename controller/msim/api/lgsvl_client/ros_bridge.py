#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
"""
ROS Bridge模块，负责启动和管理rosbridge_websocket，
提供WebSocket服务，允许非ROS程序与ROS系统通信。
"""

import os
import threading
import time
import xml.etree.ElementTree as ET
import subprocess
from typing import Optional, Dict, Any, List

from utils.loggers import LoggerProxy

logger = LoggerProxy("lgsvl")


class RosBridge:
    """ROS Bridge类，负责启动和管理rosbridge_websocket服务。

    提供WebSocket服务，允许非ROS程序（如LGSVL模拟器）与ROS系统通信。

    属性:
        _bridge_ip: ROS Bridge服务器IP地址
        _bridge_port: ROS Bridge服务器端口
        _process: rosbridge_websocket进程
        _is_running: 指示Bridge是否正在运行
    """

    def __init__(self, bridge_ip: str = "localhost", bridge_port: int = 9090):
        """初始化ROS Bridge。

        参数:
            bridge_ip: ROS Bridge服务器IP地址，默认为"localhost"
            bridge_port: ROS Bridge服务器端口，默认为9090
        """
        self._bridge_ip = bridge_ip
        self._bridge_port = bridge_port
        self._process = None
        self._is_running = False
        self._stop_event = threading.Event()

    def start(self) -> bool:
        """启动ROS Bridge服务。

        返回:
            启动是否成功
        """
        if self._is_running:
            logger.warning("ROS Bridge已经在运行中")
            return True

        try:
            # 查找rosbridge_websocket.launch文件
            launch_file = self._find_launch_file()
            if not launch_file:
                logger.error("找不到rosbridge_websocket.launch文件")
                return False

            # 修改端口配置
            self._update_port_config(launch_file)

            # 启动rosbridge_websocket
            cmd = ["roslaunch", launch_file]
            self._process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                env=os.environ.copy(),
            )

            # 等待服务启动
            time.sleep(2)

            if self._process.poll() is None:
                self._is_running = True
                logger.info(
                    f"ROS Bridge已启动，IP: {self._bridge_ip}, 端口: {self._bridge_port}"
                )
                return True
            else:
                stdout, stderr = self._process.communicate()
                logger.error(f"ROS Bridge启动失败: {stderr.decode('utf-8')}")
                return False

        except Exception as e:
            logger.error(f"启动ROS Bridge时发生错误: {str(e)}")
            return False

    def stop(self) -> bool:
        """停止ROS Bridge服务。

        返回:
            停止是否成功
        """
        if not self._is_running:
            logger.warning("ROS Bridge未在运行")
            return True

        try:
            if self._process:
                self._process.terminate()
                self._process.wait(timeout=5)
                self._process = None

            self._is_running = False
            logger.info("ROS Bridge已停止")
            return True

        except Exception as e:
            logger.error(f"停止ROS Bridge时发生错误: {str(e)}")
            if self._process:
                self._process.kill()
                self._process = None
            self._is_running = False
            return False

    def is_running(self) -> bool:
        """检查ROS Bridge是否正在运行。

        返回:
            ROS Bridge是否正在运行
        """
        if self._process and self._process.poll() is None:
            return True
        else:
            self._is_running = False
            return False

    def _find_launch_file(self) -> str:
        """查找rosbridge_websocket.launch文件。

        返回:
            launch文件路径，如果找不到则返回空字符串
        """
        # 首先在ROS包路径中查找
        try:
            result = subprocess.run(
                ["rospack", "find", "rosbridge_server"], capture_output=True, text=True
            )
            if result.returncode == 0:
                package_path = result.stdout.strip()
                launch_file = os.path.join(
                    package_path, "launch", "rosbridge_websocket.launch"
                )
                if os.path.exists(launch_file):
                    return launch_file
        except Exception as e:
            logger.warning(f"在ROS包路径中查找launch文件时出错: {str(e)}")

        # 在常见路径中查找
        common_paths = [
            "/opt/ros/noetic/share/rosbridge_server/launch/rosbridge_websocket.launch",
            "/opt/ros/melodic/share/rosbridge_server/launch/rosbridge_websocket.launch",
            "/opt/ros/foxy/share/rosbridge_server/launch/rosbridge_websocket.launch",
            os.path.expanduser(
                "~/catkin_ws/src/rosbridge_suite/rosbridge_server/launch/rosbridge_websocket.launch"
            ),
        ]

        for path in common_paths:
            if os.path.exists(path):
                return path

        return ""

    def _update_port_config(self, launch_file: str) -> None:
        """更新launch文件中的端口配置。

        参数:
            launch_file: launch文件路径
        """
        try:
            tree = ET.parse(launch_file)
            root = tree.getroot()

            # 查找port参数并更新
            for arg in root.findall("arg"):
                if "name" in arg.attrib and arg.attrib["name"] == "port":
                    arg.attrib["default"] = str(self._bridge_port)

            # 保存修改后的文件
            tree.write(launch_file)
            logger.info(f"已更新ROS Bridge端口配置为: {self._bridge_port}")

        except Exception as e:
            logger.error(f"更新端口配置时发生错误: {str(e)}")

    @property
    def bridge_ip(self) -> str:
        """获取ROS Bridge服务器IP地址。

        返回:
            ROS Bridge服务器IP地址
        """
        return self._bridge_ip

    @bridge_ip.setter
    def bridge_ip(self, ip: str) -> None:
        """设置ROS Bridge服务器IP地址。

        参数:
            ip: 新的IP地址
        """
        if self._is_running:
            logger.warning("ROS Bridge正在运行，无法更改IP地址")
            return

        self._bridge_ip = ip

    @property
    def bridge_port(self) -> int:
        """获取ROS Bridge服务器端口。

        返回:
            ROS Bridge服务器端口
        """
        return self._bridge_port

    @bridge_port.setter
    def bridge_port(self, port: int) -> None:
        """设置ROS Bridge服务器端口。

        参数:
            port: 新的端口
        """
        if self._is_running:
            logger.warning("ROS Bridge正在运行，无法更改端口")
            return

        self._bridge_port = port
