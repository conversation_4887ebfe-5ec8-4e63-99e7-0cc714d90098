# This Python file uses the following encoding: utf-8
"""autogenerated by genpy from lgsvl_msgs/Detection3D.msg. Do not edit."""
import codecs
import sys
python3 = True if sys.hexversion > 0x03000000 else False
import genpy
import struct

import geometry_msgs.msg
import lgsvl_msgs.msg
import std_msgs.msg

class Detection3D(genpy.Message):
  _md5sum = "84255d68ccbb1932c54e765809fbf7cc"
  _type = "lgsvl_msgs/Detection3D"
  _has_header = True  # flag to mark the presence of a Header object
  _full_text = """std_msgs/Header header
uint32 id  # The numeric ID of the detected object
string label  # car, pedestrian
float32 score  # The confidence score of the detected object in the range [0-1]
uint32 intent
uint32 flag
float32 ignore
float32 over
float32 give
BoundingBox3D bbox  # A 3D bounding box
geometry_msgs/Twist velocity  # Linear and angular velocity

float64 utm_x
float64 utm_y

float64 utmx1
float64 utmy1

float64 utmx2
float64 utmy2

float64 utmx3
float64 utmy3

float64 utmx4
float64 utmy4

uint32 index
================================================================================
MSG: std_msgs/Header
# Standard metadata for higher-level stamped data types.
# This is generally used to communicate timestamped data 
# in a particular coordinate frame.
# 
# sequence ID: consecutively increasing ID 
uint32 seq
#Two-integer timestamp that is expressed as:
# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')
# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')
# time-handling sugar is provided by the client library
time stamp
#Frame this data is associated with
string frame_id

================================================================================
MSG: lgsvl_msgs/BoundingBox3D
geometry_msgs/Pose position  # 3D position and orientation of the bounding box center in Lidar space, in meters
geometry_msgs/Vector3 size  # Size of the bounding box, in meters

================================================================================
MSG: geometry_msgs/Pose
# A representation of pose in free space, composed of position and orientation. 
Point position
Quaternion orientation

================================================================================
MSG: geometry_msgs/Point
# This contains the position of a point in free space
float64 x
float64 y
float64 z

================================================================================
MSG: geometry_msgs/Quaternion
# This represents an orientation in free space in quaternion form.

float64 x
float64 y
float64 z
float64 w

================================================================================
MSG: geometry_msgs/Vector3
# This represents a vector in free space. 
# It is only meant to represent a direction. Therefore, it does not
# make sense to apply a translation to it (e.g., when applying a 
# generic rigid transformation to a Vector3, tf2 will only apply the
# rotation). If you want your data to be translatable too, use the
# geometry_msgs/Point message instead.

float64 x
float64 y
float64 z
================================================================================
MSG: geometry_msgs/Twist
# This expresses velocity in free space broken into its linear and angular parts.
Vector3  linear
Vector3  angular
"""
  __slots__ = ['header','id','label','score','intent','flag','ignore','over','give','bbox','velocity','utm_x','utm_y','utmx1','utmy1','utmx2','utmy2','utmx3','utmy3','utmx4','utmy4','index']
  _slot_types = ['std_msgs/Header','uint32','string','float32','uint32','uint32','float32','float32','float32','lgsvl_msgs/BoundingBox3D','geometry_msgs/Twist','float64','float64','float64','float64','float64','float64','float64','float64','float64','float64','uint32']

  def __init__(self, *args, **kwds):
    """
    Constructor. Any message fields that are implicitly/explicitly
    set to None will be assigned a default value. The recommend
    use is keyword arguments as this is more robust to future message
    changes.  You cannot mix in-order arguments and keyword arguments.

    The available fields are:
       header,id,label,score,intent,flag,ignore,over,give,bbox,velocity,utm_x,utm_y,utmx1,utmy1,utmx2,utmy2,utmx3,utmy3,utmx4,utmy4,index

    :param args: complete set of field values, in .msg order
    :param kwds: use keyword arguments corresponding to message field names
    to set specific fields.
    """
    if args or kwds:
      super(Detection3D, self).__init__(*args, **kwds)
      # message fields cannot be None, assign default values for those that are
      if self.header is None:
        self.header = std_msgs.msg.Header()
      if self.id is None:
        self.id = 0
      if self.label is None:
        self.label = ''
      if self.score is None:
        self.score = 0.
      if self.intent is None:
        self.intent = 0
      if self.flag is None:
        self.flag = 0
      if self.ignore is None:
        self.ignore = 0.
      if self.over is None:
        self.over = 0.
      if self.give is None:
        self.give = 0.
      if self.bbox is None:
        self.bbox = lgsvl_msgs.msg.BoundingBox3D()
      if self.velocity is None:
        self.velocity = geometry_msgs.msg.Twist()
      if self.utm_x is None:
        self.utm_x = 0.
      if self.utm_y is None:
        self.utm_y = 0.
      if self.utmx1 is None:
        self.utmx1 = 0.
      if self.utmy1 is None:
        self.utmy1 = 0.
      if self.utmx2 is None:
        self.utmx2 = 0.
      if self.utmy2 is None:
        self.utmy2 = 0.
      if self.utmx3 is None:
        self.utmx3 = 0.
      if self.utmy3 is None:
        self.utmy3 = 0.
      if self.utmx4 is None:
        self.utmx4 = 0.
      if self.utmy4 is None:
        self.utmy4 = 0.
      if self.index is None:
        self.index = 0
    else:
      self.header = std_msgs.msg.Header()
      self.id = 0
      self.label = ''
      self.score = 0.
      self.intent = 0
      self.flag = 0
      self.ignore = 0.
      self.over = 0.
      self.give = 0.
      self.bbox = lgsvl_msgs.msg.BoundingBox3D()
      self.velocity = geometry_msgs.msg.Twist()
      self.utm_x = 0.
      self.utm_y = 0.
      self.utmx1 = 0.
      self.utmy1 = 0.
      self.utmx2 = 0.
      self.utmy2 = 0.
      self.utmx3 = 0.
      self.utmy3 = 0.
      self.utmx4 = 0.
      self.utmy4 = 0.
      self.index = 0

  def _get_types(self):
    """
    internal API method
    """
    return self._slot_types

  def serialize(self, buff):
    """
    serialize message into buffer
    :param buff: buffer, ``StringIO``
    """
    try:
      _x = self
      buff.write(_get_struct_3I().pack(_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs))
      _x = self.header.frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self.id
      buff.write(_get_struct_I().pack(_x))
      _x = self.label
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_f2I3f26dI().pack(_x.score, _x.intent, _x.flag, _x.ignore, _x.over, _x.give, _x.bbox.position.position.x, _x.bbox.position.position.y, _x.bbox.position.position.z, _x.bbox.position.orientation.x, _x.bbox.position.orientation.y, _x.bbox.position.orientation.z, _x.bbox.position.orientation.w, _x.bbox.size.x, _x.bbox.size.y, _x.bbox.size.z, _x.velocity.linear.x, _x.velocity.linear.y, _x.velocity.linear.z, _x.velocity.angular.x, _x.velocity.angular.y, _x.velocity.angular.z, _x.utm_x, _x.utm_y, _x.utmx1, _x.utmy1, _x.utmx2, _x.utmy2, _x.utmx3, _x.utmy3, _x.utmx4, _x.utmy4, _x.index))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize(self, str):
    """
    unpack serialized message in str into this message instance
    :param str: byte array of serialized message, ``str``
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.header is None:
        self.header = std_msgs.msg.Header()
      if self.bbox is None:
        self.bbox = lgsvl_msgs.msg.BoundingBox3D()
      if self.velocity is None:
        self.velocity = geometry_msgs.msg.Twist()
      end = 0
      _x = self
      start = end
      end += 12
      (_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs,) = _get_struct_3I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.header.frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.header.frame_id = str[start:end]
      start = end
      end += 4
      (self.id,) = _get_struct_I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.label = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.label = str[start:end]
      _x = self
      start = end
      end += 236
      (_x.score, _x.intent, _x.flag, _x.ignore, _x.over, _x.give, _x.bbox.position.position.x, _x.bbox.position.position.y, _x.bbox.position.position.z, _x.bbox.position.orientation.x, _x.bbox.position.orientation.y, _x.bbox.position.orientation.z, _x.bbox.position.orientation.w, _x.bbox.size.x, _x.bbox.size.y, _x.bbox.size.z, _x.velocity.linear.x, _x.velocity.linear.y, _x.velocity.linear.z, _x.velocity.angular.x, _x.velocity.angular.y, _x.velocity.angular.z, _x.utm_x, _x.utm_y, _x.utmx1, _x.utmy1, _x.utmx2, _x.utmy2, _x.utmx3, _x.utmy3, _x.utmx4, _x.utmy4, _x.index,) = _get_struct_f2I3f26dI().unpack(str[start:end])
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill


  def serialize_numpy(self, buff, numpy):
    """
    serialize message with numpy array types into buffer
    :param buff: buffer, ``StringIO``
    :param numpy: numpy python module
    """
    try:
      _x = self
      buff.write(_get_struct_3I().pack(_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs))
      _x = self.header.frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self.id
      buff.write(_get_struct_I().pack(_x))
      _x = self.label
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_f2I3f26dI().pack(_x.score, _x.intent, _x.flag, _x.ignore, _x.over, _x.give, _x.bbox.position.position.x, _x.bbox.position.position.y, _x.bbox.position.position.z, _x.bbox.position.orientation.x, _x.bbox.position.orientation.y, _x.bbox.position.orientation.z, _x.bbox.position.orientation.w, _x.bbox.size.x, _x.bbox.size.y, _x.bbox.size.z, _x.velocity.linear.x, _x.velocity.linear.y, _x.velocity.linear.z, _x.velocity.angular.x, _x.velocity.angular.y, _x.velocity.angular.z, _x.utm_x, _x.utm_y, _x.utmx1, _x.utmy1, _x.utmx2, _x.utmy2, _x.utmx3, _x.utmy3, _x.utmx4, _x.utmy4, _x.index))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize_numpy(self, str, numpy):
    """
    unpack serialized message in str into this message instance using numpy for array types
    :param str: byte array of serialized message, ``str``
    :param numpy: numpy python module
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.header is None:
        self.header = std_msgs.msg.Header()
      if self.bbox is None:
        self.bbox = lgsvl_msgs.msg.BoundingBox3D()
      if self.velocity is None:
        self.velocity = geometry_msgs.msg.Twist()
      end = 0
      _x = self
      start = end
      end += 12
      (_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs,) = _get_struct_3I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.header.frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.header.frame_id = str[start:end]
      start = end
      end += 4
      (self.id,) = _get_struct_I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.label = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.label = str[start:end]
      _x = self
      start = end
      end += 236
      (_x.score, _x.intent, _x.flag, _x.ignore, _x.over, _x.give, _x.bbox.position.position.x, _x.bbox.position.position.y, _x.bbox.position.position.z, _x.bbox.position.orientation.x, _x.bbox.position.orientation.y, _x.bbox.position.orientation.z, _x.bbox.position.orientation.w, _x.bbox.size.x, _x.bbox.size.y, _x.bbox.size.z, _x.velocity.linear.x, _x.velocity.linear.y, _x.velocity.linear.z, _x.velocity.angular.x, _x.velocity.angular.y, _x.velocity.angular.z, _x.utm_x, _x.utm_y, _x.utmx1, _x.utmy1, _x.utmx2, _x.utmy2, _x.utmx3, _x.utmy3, _x.utmx4, _x.utmy4, _x.index,) = _get_struct_f2I3f26dI().unpack(str[start:end])
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill

_struct_I = genpy.struct_I
def _get_struct_I():
    global _struct_I
    return _struct_I
_struct_3I = None
def _get_struct_3I():
    global _struct_3I
    if _struct_3I is None:
        _struct_3I = struct.Struct("<3I")
    return _struct_3I
_struct_f2I3f26dI = None
def _get_struct_f2I3f26dI():
    global _struct_f2I3f26dI
    if _struct_f2I3f26dI is None:
        _struct_f2I3f26dI = struct.Struct("<f2I3f26dI")
    return _struct_f2I3f26dI
