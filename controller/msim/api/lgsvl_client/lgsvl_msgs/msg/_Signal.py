# This Python file uses the following encoding: utf-8
"""autogenerated by genpy from lgsvl_msgs/Signal.msg. Do not edit."""
import codecs
import sys
python3 = True if sys.hexversion > 0x03000000 else False
import genpy
import struct

import geometry_msgs.msg
import lgsvl_msgs.msg
import std_msgs.msg

class Signal(genpy.Message):
  _md5sum = "97e3e68df6899dc3aeec024df4e65e51"
  _type = "lgsvl_msgs/Signal"
  _has_header = True  # flag to mark the presence of a Header object
  _full_text = """std_msgs/Header header
uint32 id  # The numeric ID of the detected signal
string label  # green, yellow, red
float32 score  # The confidence score of the detected signal in the range [0-1]
BoundingBox3D bbox  # A 3D bounding box
geometry_msgs/Pose stopline
================================================================================
MSG: std_msgs/Header
# Standard metadata for higher-level stamped data types.
# This is generally used to communicate timestamped data 
# in a particular coordinate frame.
# 
# sequence ID: consecutively increasing ID 
uint32 seq
#Two-integer timestamp that is expressed as:
# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')
# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')
# time-handling sugar is provided by the client library
time stamp
#Frame this data is associated with
string frame_id

================================================================================
MSG: lgsvl_msgs/BoundingBox3D
geometry_msgs/Pose position  # 3D position and orientation of the bounding box center in Lidar space, in meters
geometry_msgs/Vector3 size  # Size of the bounding box, in meters

================================================================================
MSG: geometry_msgs/Pose
# A representation of pose in free space, composed of position and orientation. 
Point position
Quaternion orientation

================================================================================
MSG: geometry_msgs/Point
# This contains the position of a point in free space
float64 x
float64 y
float64 z

================================================================================
MSG: geometry_msgs/Quaternion
# This represents an orientation in free space in quaternion form.

float64 x
float64 y
float64 z
float64 w

================================================================================
MSG: geometry_msgs/Vector3
# This represents a vector in free space. 
# It is only meant to represent a direction. Therefore, it does not
# make sense to apply a translation to it (e.g., when applying a 
# generic rigid transformation to a Vector3, tf2 will only apply the
# rotation). If you want your data to be translatable too, use the
# geometry_msgs/Point message instead.

float64 x
float64 y
float64 z"""
  __slots__ = ['header','id','label','score','bbox','stopline']
  _slot_types = ['std_msgs/Header','uint32','string','float32','lgsvl_msgs/BoundingBox3D','geometry_msgs/Pose']

  def __init__(self, *args, **kwds):
    """
    Constructor. Any message fields that are implicitly/explicitly
    set to None will be assigned a default value. The recommend
    use is keyword arguments as this is more robust to future message
    changes.  You cannot mix in-order arguments and keyword arguments.

    The available fields are:
       header,id,label,score,bbox,stopline

    :param args: complete set of field values, in .msg order
    :param kwds: use keyword arguments corresponding to message field names
    to set specific fields.
    """
    if args or kwds:
      super(Signal, self).__init__(*args, **kwds)
      # message fields cannot be None, assign default values for those that are
      if self.header is None:
        self.header = std_msgs.msg.Header()
      if self.id is None:
        self.id = 0
      if self.label is None:
        self.label = ''
      if self.score is None:
        self.score = 0.
      if self.bbox is None:
        self.bbox = lgsvl_msgs.msg.BoundingBox3D()
      if self.stopline is None:
        self.stopline = geometry_msgs.msg.Pose()
    else:
      self.header = std_msgs.msg.Header()
      self.id = 0
      self.label = ''
      self.score = 0.
      self.bbox = lgsvl_msgs.msg.BoundingBox3D()
      self.stopline = geometry_msgs.msg.Pose()

  def _get_types(self):
    """
    internal API method
    """
    return self._slot_types

  def serialize(self, buff):
    """
    serialize message into buffer
    :param buff: buffer, ``StringIO``
    """
    try:
      _x = self
      buff.write(_get_struct_3I().pack(_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs))
      _x = self.header.frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self.id
      buff.write(_get_struct_I().pack(_x))
      _x = self.label
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_f17d().pack(_x.score, _x.bbox.position.position.x, _x.bbox.position.position.y, _x.bbox.position.position.z, _x.bbox.position.orientation.x, _x.bbox.position.orientation.y, _x.bbox.position.orientation.z, _x.bbox.position.orientation.w, _x.bbox.size.x, _x.bbox.size.y, _x.bbox.size.z, _x.stopline.position.x, _x.stopline.position.y, _x.stopline.position.z, _x.stopline.orientation.x, _x.stopline.orientation.y, _x.stopline.orientation.z, _x.stopline.orientation.w))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize(self, str):
    """
    unpack serialized message in str into this message instance
    :param str: byte array of serialized message, ``str``
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.header is None:
        self.header = std_msgs.msg.Header()
      if self.bbox is None:
        self.bbox = lgsvl_msgs.msg.BoundingBox3D()
      if self.stopline is None:
        self.stopline = geometry_msgs.msg.Pose()
      end = 0
      _x = self
      start = end
      end += 12
      (_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs,) = _get_struct_3I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.header.frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.header.frame_id = str[start:end]
      start = end
      end += 4
      (self.id,) = _get_struct_I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.label = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.label = str[start:end]
      _x = self
      start = end
      end += 140
      (_x.score, _x.bbox.position.position.x, _x.bbox.position.position.y, _x.bbox.position.position.z, _x.bbox.position.orientation.x, _x.bbox.position.orientation.y, _x.bbox.position.orientation.z, _x.bbox.position.orientation.w, _x.bbox.size.x, _x.bbox.size.y, _x.bbox.size.z, _x.stopline.position.x, _x.stopline.position.y, _x.stopline.position.z, _x.stopline.orientation.x, _x.stopline.orientation.y, _x.stopline.orientation.z, _x.stopline.orientation.w,) = _get_struct_f17d().unpack(str[start:end])
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill


  def serialize_numpy(self, buff, numpy):
    """
    serialize message with numpy array types into buffer
    :param buff: buffer, ``StringIO``
    :param numpy: numpy python module
    """
    try:
      _x = self
      buff.write(_get_struct_3I().pack(_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs))
      _x = self.header.frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self.id
      buff.write(_get_struct_I().pack(_x))
      _x = self.label
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_f17d().pack(_x.score, _x.bbox.position.position.x, _x.bbox.position.position.y, _x.bbox.position.position.z, _x.bbox.position.orientation.x, _x.bbox.position.orientation.y, _x.bbox.position.orientation.z, _x.bbox.position.orientation.w, _x.bbox.size.x, _x.bbox.size.y, _x.bbox.size.z, _x.stopline.position.x, _x.stopline.position.y, _x.stopline.position.z, _x.stopline.orientation.x, _x.stopline.orientation.y, _x.stopline.orientation.z, _x.stopline.orientation.w))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize_numpy(self, str, numpy):
    """
    unpack serialized message in str into this message instance using numpy for array types
    :param str: byte array of serialized message, ``str``
    :param numpy: numpy python module
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.header is None:
        self.header = std_msgs.msg.Header()
      if self.bbox is None:
        self.bbox = lgsvl_msgs.msg.BoundingBox3D()
      if self.stopline is None:
        self.stopline = geometry_msgs.msg.Pose()
      end = 0
      _x = self
      start = end
      end += 12
      (_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs,) = _get_struct_3I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.header.frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.header.frame_id = str[start:end]
      start = end
      end += 4
      (self.id,) = _get_struct_I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.label = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.label = str[start:end]
      _x = self
      start = end
      end += 140
      (_x.score, _x.bbox.position.position.x, _x.bbox.position.position.y, _x.bbox.position.position.z, _x.bbox.position.orientation.x, _x.bbox.position.orientation.y, _x.bbox.position.orientation.z, _x.bbox.position.orientation.w, _x.bbox.size.x, _x.bbox.size.y, _x.bbox.size.z, _x.stopline.position.x, _x.stopline.position.y, _x.stopline.position.z, _x.stopline.orientation.x, _x.stopline.orientation.y, _x.stopline.orientation.z, _x.stopline.orientation.w,) = _get_struct_f17d().unpack(str[start:end])
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill

_struct_I = genpy.struct_I
def _get_struct_I():
    global _struct_I
    return _struct_I
_struct_3I = None
def _get_struct_3I():
    global _struct_3I
    if _struct_3I is None:
        _struct_3I = struct.Struct("<3I")
    return _struct_3I
_struct_f17d = None
def _get_struct_f17d():
    global _struct_f17d
    if _struct_f17d is None:
        _struct_f17d = struct.Struct("<f17d")
    return _struct_f17d
