# This Python file uses the following encoding: utf-8
"""autogenerated by genpy from lgsvl_msgs/CanBusData.msg. Do not edit."""
import codecs
import sys
python3 = True if sys.hexversion > 0x03000000 else False
import genpy
import struct

import geometry_msgs.msg
import std_msgs.msg

class CanBusData(genpy.Message):
  _md5sum = "b87b5770e05e57bf8145ee83c4177b03"
  _type = "lgsvl_msgs/CanBusData"
  _has_header = True  # flag to mark the presence of a Header object
  _full_text = """std_msgs/Header header

float32 speed_mps
float32 throttle_pct  # 0 to 1
float32 brake_pct     # 0 to 1
float32 steer_pct     # -1 to 1
bool parking_brake_active
bool high_beams_active
bool low_beams_active
bool hazard_lights_active
bool fog_lights_active
bool left_turn_signal_active
bool right_turn_signal_active
bool wipers_active
bool reverse_gear_active
int8 selected_gear
bool engine_active
float32 engine_rpm
float64 gps_latitude
float64 gps_longitude
float64 gps_altitude
geometry_msgs/Quaternion orientation
geometry_msgs/Vector3 linear_velocities

int8 GEAR_NEUTRAL = 0
int8 GEAR_DRIVE = 1
int8 GEAR_REVERSE = 2
int8 GEAR_PARKING = 3
int8 GEAR_LOW = 4

================================================================================
MSG: std_msgs/Header
# Standard metadata for higher-level stamped data types.
# This is generally used to communicate timestamped data 
# in a particular coordinate frame.
# 
# sequence ID: consecutively increasing ID 
uint32 seq
#Two-integer timestamp that is expressed as:
# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')
# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')
# time-handling sugar is provided by the client library
time stamp
#Frame this data is associated with
string frame_id

================================================================================
MSG: geometry_msgs/Quaternion
# This represents an orientation in free space in quaternion form.

float64 x
float64 y
float64 z
float64 w

================================================================================
MSG: geometry_msgs/Vector3
# This represents a vector in free space. 
# It is only meant to represent a direction. Therefore, it does not
# make sense to apply a translation to it (e.g., when applying a 
# generic rigid transformation to a Vector3, tf2 will only apply the
# rotation). If you want your data to be translatable too, use the
# geometry_msgs/Point message instead.

float64 x
float64 y
float64 z"""
  # Pseudo-constants
  GEAR_NEUTRAL = 0
  GEAR_DRIVE = 1
  GEAR_REVERSE = 2
  GEAR_PARKING = 3
  GEAR_LOW = 4

  __slots__ = ['header','speed_mps','throttle_pct','brake_pct','steer_pct','parking_brake_active','high_beams_active','low_beams_active','hazard_lights_active','fog_lights_active','left_turn_signal_active','right_turn_signal_active','wipers_active','reverse_gear_active','selected_gear','engine_active','engine_rpm','gps_latitude','gps_longitude','gps_altitude','orientation','linear_velocities']
  _slot_types = ['std_msgs/Header','float32','float32','float32','float32','bool','bool','bool','bool','bool','bool','bool','bool','bool','int8','bool','float32','float64','float64','float64','geometry_msgs/Quaternion','geometry_msgs/Vector3']

  def __init__(self, *args, **kwds):
    """
    Constructor. Any message fields that are implicitly/explicitly
    set to None will be assigned a default value. The recommend
    use is keyword arguments as this is more robust to future message
    changes.  You cannot mix in-order arguments and keyword arguments.

    The available fields are:
       header,speed_mps,throttle_pct,brake_pct,steer_pct,parking_brake_active,high_beams_active,low_beams_active,hazard_lights_active,fog_lights_active,left_turn_signal_active,right_turn_signal_active,wipers_active,reverse_gear_active,selected_gear,engine_active,engine_rpm,gps_latitude,gps_longitude,gps_altitude,orientation,linear_velocities

    :param args: complete set of field values, in .msg order
    :param kwds: use keyword arguments corresponding to message field names
    to set specific fields.
    """
    if args or kwds:
      super(CanBusData, self).__init__(*args, **kwds)
      # message fields cannot be None, assign default values for those that are
      if self.header is None:
        self.header = std_msgs.msg.Header()
      if self.speed_mps is None:
        self.speed_mps = 0.
      if self.throttle_pct is None:
        self.throttle_pct = 0.
      if self.brake_pct is None:
        self.brake_pct = 0.
      if self.steer_pct is None:
        self.steer_pct = 0.
      if self.parking_brake_active is None:
        self.parking_brake_active = False
      if self.high_beams_active is None:
        self.high_beams_active = False
      if self.low_beams_active is None:
        self.low_beams_active = False
      if self.hazard_lights_active is None:
        self.hazard_lights_active = False
      if self.fog_lights_active is None:
        self.fog_lights_active = False
      if self.left_turn_signal_active is None:
        self.left_turn_signal_active = False
      if self.right_turn_signal_active is None:
        self.right_turn_signal_active = False
      if self.wipers_active is None:
        self.wipers_active = False
      if self.reverse_gear_active is None:
        self.reverse_gear_active = False
      if self.selected_gear is None:
        self.selected_gear = 0
      if self.engine_active is None:
        self.engine_active = False
      if self.engine_rpm is None:
        self.engine_rpm = 0.
      if self.gps_latitude is None:
        self.gps_latitude = 0.
      if self.gps_longitude is None:
        self.gps_longitude = 0.
      if self.gps_altitude is None:
        self.gps_altitude = 0.
      if self.orientation is None:
        self.orientation = geometry_msgs.msg.Quaternion()
      if self.linear_velocities is None:
        self.linear_velocities = geometry_msgs.msg.Vector3()
    else:
      self.header = std_msgs.msg.Header()
      self.speed_mps = 0.
      self.throttle_pct = 0.
      self.brake_pct = 0.
      self.steer_pct = 0.
      self.parking_brake_active = False
      self.high_beams_active = False
      self.low_beams_active = False
      self.hazard_lights_active = False
      self.fog_lights_active = False
      self.left_turn_signal_active = False
      self.right_turn_signal_active = False
      self.wipers_active = False
      self.reverse_gear_active = False
      self.selected_gear = 0
      self.engine_active = False
      self.engine_rpm = 0.
      self.gps_latitude = 0.
      self.gps_longitude = 0.
      self.gps_altitude = 0.
      self.orientation = geometry_msgs.msg.Quaternion()
      self.linear_velocities = geometry_msgs.msg.Vector3()

  def _get_types(self):
    """
    internal API method
    """
    return self._slot_types

  def serialize(self, buff):
    """
    serialize message into buffer
    :param buff: buffer, ``StringIO``
    """
    try:
      _x = self
      buff.write(_get_struct_3I().pack(_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs))
      _x = self.header.frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_4f9BbBf10d().pack(_x.speed_mps, _x.throttle_pct, _x.brake_pct, _x.steer_pct, _x.parking_brake_active, _x.high_beams_active, _x.low_beams_active, _x.hazard_lights_active, _x.fog_lights_active, _x.left_turn_signal_active, _x.right_turn_signal_active, _x.wipers_active, _x.reverse_gear_active, _x.selected_gear, _x.engine_active, _x.engine_rpm, _x.gps_latitude, _x.gps_longitude, _x.gps_altitude, _x.orientation.x, _x.orientation.y, _x.orientation.z, _x.orientation.w, _x.linear_velocities.x, _x.linear_velocities.y, _x.linear_velocities.z))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize(self, str):
    """
    unpack serialized message in str into this message instance
    :param str: byte array of serialized message, ``str``
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.header is None:
        self.header = std_msgs.msg.Header()
      if self.orientation is None:
        self.orientation = geometry_msgs.msg.Quaternion()
      if self.linear_velocities is None:
        self.linear_velocities = geometry_msgs.msg.Vector3()
      end = 0
      _x = self
      start = end
      end += 12
      (_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs,) = _get_struct_3I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.header.frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.header.frame_id = str[start:end]
      _x = self
      start = end
      end += 111
      (_x.speed_mps, _x.throttle_pct, _x.brake_pct, _x.steer_pct, _x.parking_brake_active, _x.high_beams_active, _x.low_beams_active, _x.hazard_lights_active, _x.fog_lights_active, _x.left_turn_signal_active, _x.right_turn_signal_active, _x.wipers_active, _x.reverse_gear_active, _x.selected_gear, _x.engine_active, _x.engine_rpm, _x.gps_latitude, _x.gps_longitude, _x.gps_altitude, _x.orientation.x, _x.orientation.y, _x.orientation.z, _x.orientation.w, _x.linear_velocities.x, _x.linear_velocities.y, _x.linear_velocities.z,) = _get_struct_4f9BbBf10d().unpack(str[start:end])
      self.parking_brake_active = bool(self.parking_brake_active)
      self.high_beams_active = bool(self.high_beams_active)
      self.low_beams_active = bool(self.low_beams_active)
      self.hazard_lights_active = bool(self.hazard_lights_active)
      self.fog_lights_active = bool(self.fog_lights_active)
      self.left_turn_signal_active = bool(self.left_turn_signal_active)
      self.right_turn_signal_active = bool(self.right_turn_signal_active)
      self.wipers_active = bool(self.wipers_active)
      self.reverse_gear_active = bool(self.reverse_gear_active)
      self.engine_active = bool(self.engine_active)
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill


  def serialize_numpy(self, buff, numpy):
    """
    serialize message with numpy array types into buffer
    :param buff: buffer, ``StringIO``
    :param numpy: numpy python module
    """
    try:
      _x = self
      buff.write(_get_struct_3I().pack(_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs))
      _x = self.header.frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_4f9BbBf10d().pack(_x.speed_mps, _x.throttle_pct, _x.brake_pct, _x.steer_pct, _x.parking_brake_active, _x.high_beams_active, _x.low_beams_active, _x.hazard_lights_active, _x.fog_lights_active, _x.left_turn_signal_active, _x.right_turn_signal_active, _x.wipers_active, _x.reverse_gear_active, _x.selected_gear, _x.engine_active, _x.engine_rpm, _x.gps_latitude, _x.gps_longitude, _x.gps_altitude, _x.orientation.x, _x.orientation.y, _x.orientation.z, _x.orientation.w, _x.linear_velocities.x, _x.linear_velocities.y, _x.linear_velocities.z))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize_numpy(self, str, numpy):
    """
    unpack serialized message in str into this message instance using numpy for array types
    :param str: byte array of serialized message, ``str``
    :param numpy: numpy python module
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.header is None:
        self.header = std_msgs.msg.Header()
      if self.orientation is None:
        self.orientation = geometry_msgs.msg.Quaternion()
      if self.linear_velocities is None:
        self.linear_velocities = geometry_msgs.msg.Vector3()
      end = 0
      _x = self
      start = end
      end += 12
      (_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs,) = _get_struct_3I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.header.frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.header.frame_id = str[start:end]
      _x = self
      start = end
      end += 111
      (_x.speed_mps, _x.throttle_pct, _x.brake_pct, _x.steer_pct, _x.parking_brake_active, _x.high_beams_active, _x.low_beams_active, _x.hazard_lights_active, _x.fog_lights_active, _x.left_turn_signal_active, _x.right_turn_signal_active, _x.wipers_active, _x.reverse_gear_active, _x.selected_gear, _x.engine_active, _x.engine_rpm, _x.gps_latitude, _x.gps_longitude, _x.gps_altitude, _x.orientation.x, _x.orientation.y, _x.orientation.z, _x.orientation.w, _x.linear_velocities.x, _x.linear_velocities.y, _x.linear_velocities.z,) = _get_struct_4f9BbBf10d().unpack(str[start:end])
      self.parking_brake_active = bool(self.parking_brake_active)
      self.high_beams_active = bool(self.high_beams_active)
      self.low_beams_active = bool(self.low_beams_active)
      self.hazard_lights_active = bool(self.hazard_lights_active)
      self.fog_lights_active = bool(self.fog_lights_active)
      self.left_turn_signal_active = bool(self.left_turn_signal_active)
      self.right_turn_signal_active = bool(self.right_turn_signal_active)
      self.wipers_active = bool(self.wipers_active)
      self.reverse_gear_active = bool(self.reverse_gear_active)
      self.engine_active = bool(self.engine_active)
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill

_struct_I = genpy.struct_I
def _get_struct_I():
    global _struct_I
    return _struct_I
_struct_3I = None
def _get_struct_3I():
    global _struct_3I
    if _struct_3I is None:
        _struct_3I = struct.Struct("<3I")
    return _struct_3I
_struct_4f9BbBf10d = None
def _get_struct_4f9BbBf10d():
    global _struct_4f9BbBf10d
    if _struct_4f9BbBf10d is None:
        _struct_4f9BbBf10d = struct.Struct("<4f9BbBf10d")
    return _struct_4f9BbBf10d
