# This Python file uses the following encoding: utf-8
"""autogenerated by genpy from lgsvl_msgs/ComfortData.msg. Do not edit."""
import codecs
import sys
python3 = True if sys.hexversion > 0x03000000 else False
import genpy
import struct


class ComfortData(genpy.Message):
  _md5sum = "324b3a38cb5e5a387286d1c3523ebd4a"
  _type = "lgsvl_msgs/ComfortData"
  _has_header = False  # flag to mark the presence of a Header object
  _full_text = """float32 velocity
float32 acceleration
float32 jerk
float32 angularVelocity
float32 angularAcceleration
float32 roll
float32 slip
bool isaccel
float32 accel_limit
"""
  __slots__ = ['velocity','acceleration','jerk','angularVelocity','angularAcceleration','roll','slip','isaccel','accel_limit']
  _slot_types = ['float32','float32','float32','float32','float32','float32','float32','bool','float32']

  def __init__(self, *args, **kwds):
    """
    Constructor. Any message fields that are implicitly/explicitly
    set to None will be assigned a default value. The recommend
    use is keyword arguments as this is more robust to future message
    changes.  You cannot mix in-order arguments and keyword arguments.

    The available fields are:
       velocity,acceleration,jerk,angularVelocity,angularAcceleration,roll,slip,isaccel,accel_limit

    :param args: complete set of field values, in .msg order
    :param kwds: use keyword arguments corresponding to message field names
    to set specific fields.
    """
    if args or kwds:
      super(ComfortData, self).__init__(*args, **kwds)
      # message fields cannot be None, assign default values for those that are
      if self.velocity is None:
        self.velocity = 0.
      if self.acceleration is None:
        self.acceleration = 0.
      if self.jerk is None:
        self.jerk = 0.
      if self.angularVelocity is None:
        self.angularVelocity = 0.
      if self.angularAcceleration is None:
        self.angularAcceleration = 0.
      if self.roll is None:
        self.roll = 0.
      if self.slip is None:
        self.slip = 0.
      if self.isaccel is None:
        self.isaccel = False
      if self.accel_limit is None:
        self.accel_limit = 0.
    else:
      self.velocity = 0.
      self.acceleration = 0.
      self.jerk = 0.
      self.angularVelocity = 0.
      self.angularAcceleration = 0.
      self.roll = 0.
      self.slip = 0.
      self.isaccel = False
      self.accel_limit = 0.

  def _get_types(self):
    """
    internal API method
    """
    return self._slot_types

  def serialize(self, buff):
    """
    serialize message into buffer
    :param buff: buffer, ``StringIO``
    """
    try:
      _x = self
      buff.write(_get_struct_7fBf().pack(_x.velocity, _x.acceleration, _x.jerk, _x.angularVelocity, _x.angularAcceleration, _x.roll, _x.slip, _x.isaccel, _x.accel_limit))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize(self, str):
    """
    unpack serialized message in str into this message instance
    :param str: byte array of serialized message, ``str``
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      end = 0
      _x = self
      start = end
      end += 33
      (_x.velocity, _x.acceleration, _x.jerk, _x.angularVelocity, _x.angularAcceleration, _x.roll, _x.slip, _x.isaccel, _x.accel_limit,) = _get_struct_7fBf().unpack(str[start:end])
      self.isaccel = bool(self.isaccel)
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill


  def serialize_numpy(self, buff, numpy):
    """
    serialize message with numpy array types into buffer
    :param buff: buffer, ``StringIO``
    :param numpy: numpy python module
    """
    try:
      _x = self
      buff.write(_get_struct_7fBf().pack(_x.velocity, _x.acceleration, _x.jerk, _x.angularVelocity, _x.angularAcceleration, _x.roll, _x.slip, _x.isaccel, _x.accel_limit))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize_numpy(self, str, numpy):
    """
    unpack serialized message in str into this message instance using numpy for array types
    :param str: byte array of serialized message, ``str``
    :param numpy: numpy python module
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      end = 0
      _x = self
      start = end
      end += 33
      (_x.velocity, _x.acceleration, _x.jerk, _x.angularVelocity, _x.angularAcceleration, _x.roll, _x.slip, _x.isaccel, _x.accel_limit,) = _get_struct_7fBf().unpack(str[start:end])
      self.isaccel = bool(self.isaccel)
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill

_struct_I = genpy.struct_I
def _get_struct_I():
    global _struct_I
    return _struct_I
_struct_7fBf = None
def _get_struct_7fBf():
    global _struct_7fBf
    if _struct_7fBf is None:
        _struct_7fBf = struct.Struct("<7fBf")
    return _struct_7fBf
