# This Python file uses the following encoding: utf-8
"""autogenerated by genpy from lgsvl_msgs/PDPObstacleArray.msg. Do not edit."""
import codecs
import sys
python3 = True if sys.hexversion > 0x03000000 else False
import genpy
import struct

import lgsvl_msgs.msg

class PDPObstacleArray(genpy.Message):
  _md5sum = "2a5e62cab95898b2b81f6b9e28151aae"
  _type = "lgsvl_msgs/PDPObstacleArray"
  _has_header = False  # flag to mark the presence of a Header object
  _full_text = """PDPObstacle[] obstacles
================================================================================
MSG: lgsvl_msgs/PDPObstacle
uint32  id
uint32  type
float64 x
float64 y
float64 z
float64 yaw
float64 length
float64 width
float64 height"""
  __slots__ = ['obstacles']
  _slot_types = ['lgsvl_msgs/PDPObstacle[]']

  def __init__(self, *args, **kwds):
    """
    Constructor. Any message fields that are implicitly/explicitly
    set to None will be assigned a default value. The recommend
    use is keyword arguments as this is more robust to future message
    changes.  You cannot mix in-order arguments and keyword arguments.

    The available fields are:
       obstacles

    :param args: complete set of field values, in .msg order
    :param kwds: use keyword arguments corresponding to message field names
    to set specific fields.
    """
    if args or kwds:
      super(PDPObstacleArray, self).__init__(*args, **kwds)
      # message fields cannot be None, assign default values for those that are
      if self.obstacles is None:
        self.obstacles = []
    else:
      self.obstacles = []

  def _get_types(self):
    """
    internal API method
    """
    return self._slot_types

  def serialize(self, buff):
    """
    serialize message into buffer
    :param buff: buffer, ``StringIO``
    """
    try:
      length = len(self.obstacles)
      buff.write(_struct_I.pack(length))
      for val1 in self.obstacles:
        _x = val1
        buff.write(_get_struct_2I7d().pack(_x.id, _x.type, _x.x, _x.y, _x.z, _x.yaw, _x.length, _x.width, _x.height))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize(self, str):
    """
    unpack serialized message in str into this message instance
    :param str: byte array of serialized message, ``str``
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.obstacles is None:
        self.obstacles = None
      end = 0
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.obstacles = []
      for i in range(0, length):
        val1 = lgsvl_msgs.msg.PDPObstacle()
        _x = val1
        start = end
        end += 64
        (_x.id, _x.type, _x.x, _x.y, _x.z, _x.yaw, _x.length, _x.width, _x.height,) = _get_struct_2I7d().unpack(str[start:end])
        self.obstacles.append(val1)
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill


  def serialize_numpy(self, buff, numpy):
    """
    serialize message with numpy array types into buffer
    :param buff: buffer, ``StringIO``
    :param numpy: numpy python module
    """
    try:
      length = len(self.obstacles)
      buff.write(_struct_I.pack(length))
      for val1 in self.obstacles:
        _x = val1
        buff.write(_get_struct_2I7d().pack(_x.id, _x.type, _x.x, _x.y, _x.z, _x.yaw, _x.length, _x.width, _x.height))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize_numpy(self, str, numpy):
    """
    unpack serialized message in str into this message instance using numpy for array types
    :param str: byte array of serialized message, ``str``
    :param numpy: numpy python module
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.obstacles is None:
        self.obstacles = None
      end = 0
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.obstacles = []
      for i in range(0, length):
        val1 = lgsvl_msgs.msg.PDPObstacle()
        _x = val1
        start = end
        end += 64
        (_x.id, _x.type, _x.x, _x.y, _x.z, _x.yaw, _x.length, _x.width, _x.height,) = _get_struct_2I7d().unpack(str[start:end])
        self.obstacles.append(val1)
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill

_struct_I = genpy.struct_I
def _get_struct_I():
    global _struct_I
    return _struct_I
_struct_2I7d = None
def _get_struct_2I7d():
    global _struct_2I7d
    if _struct_2I7d is None:
        _struct_2I7d = struct.Struct("<2I7d")
    return _struct_2I7d
