# This Python file uses the following encoding: utf-8
"""autogenerated by genpy from lgsvl_msgs/ObstaclePredicted.msg. Do not edit."""
import codecs
import sys
python3 = True if sys.hexversion > 0x03000000 else False
import genpy
import struct

import lgsvl_msgs.msg

class ObstaclePredicted(genpy.Message):
  _md5sum = "dbd46be9e5d75bff6ee92a31dfb52ae0"
  _type = "lgsvl_msgs/ObstaclePredicted"
  _has_header = False  # flag to mark the presence of a Header object
  _full_text = """uint32 id
SvlPredictPoint[] predictes 
================================================================================
MSG: lgsvl_msgs/SvlPredictPoint
float64 x
float64 y
float64 z
float64 yaw
float64 xv
float64 yv"""
  __slots__ = ['id','predictes']
  _slot_types = ['uint32','lgsvl_msgs/SvlPredictPoint[]']

  def __init__(self, *args, **kwds):
    """
    Constructor. Any message fields that are implicitly/explicitly
    set to None will be assigned a default value. The recommend
    use is keyword arguments as this is more robust to future message
    changes.  You cannot mix in-order arguments and keyword arguments.

    The available fields are:
       id,predictes

    :param args: complete set of field values, in .msg order
    :param kwds: use keyword arguments corresponding to message field names
    to set specific fields.
    """
    if args or kwds:
      super(ObstaclePredicted, self).__init__(*args, **kwds)
      # message fields cannot be None, assign default values for those that are
      if self.id is None:
        self.id = 0
      if self.predictes is None:
        self.predictes = []
    else:
      self.id = 0
      self.predictes = []

  def _get_types(self):
    """
    internal API method
    """
    return self._slot_types

  def serialize(self, buff):
    """
    serialize message into buffer
    :param buff: buffer, ``StringIO``
    """
    try:
      _x = self.id
      buff.write(_get_struct_I().pack(_x))
      length = len(self.predictes)
      buff.write(_struct_I.pack(length))
      for val1 in self.predictes:
        _x = val1
        buff.write(_get_struct_6d().pack(_x.x, _x.y, _x.z, _x.yaw, _x.xv, _x.yv))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize(self, str):
    """
    unpack serialized message in str into this message instance
    :param str: byte array of serialized message, ``str``
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.predictes is None:
        self.predictes = None
      end = 0
      start = end
      end += 4
      (self.id,) = _get_struct_I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.predictes = []
      for i in range(0, length):
        val1 = lgsvl_msgs.msg.SvlPredictPoint()
        _x = val1
        start = end
        end += 48
        (_x.x, _x.y, _x.z, _x.yaw, _x.xv, _x.yv,) = _get_struct_6d().unpack(str[start:end])
        self.predictes.append(val1)
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill


  def serialize_numpy(self, buff, numpy):
    """
    serialize message with numpy array types into buffer
    :param buff: buffer, ``StringIO``
    :param numpy: numpy python module
    """
    try:
      _x = self.id
      buff.write(_get_struct_I().pack(_x))
      length = len(self.predictes)
      buff.write(_struct_I.pack(length))
      for val1 in self.predictes:
        _x = val1
        buff.write(_get_struct_6d().pack(_x.x, _x.y, _x.z, _x.yaw, _x.xv, _x.yv))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize_numpy(self, str, numpy):
    """
    unpack serialized message in str into this message instance using numpy for array types
    :param str: byte array of serialized message, ``str``
    :param numpy: numpy python module
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.predictes is None:
        self.predictes = None
      end = 0
      start = end
      end += 4
      (self.id,) = _get_struct_I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.predictes = []
      for i in range(0, length):
        val1 = lgsvl_msgs.msg.SvlPredictPoint()
        _x = val1
        start = end
        end += 48
        (_x.x, _x.y, _x.z, _x.yaw, _x.xv, _x.yv,) = _get_struct_6d().unpack(str[start:end])
        self.predictes.append(val1)
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill

_struct_I = genpy.struct_I
def _get_struct_I():
    global _struct_I
    return _struct_I
_struct_6d = None
def _get_struct_6d():
    global _struct_6d
    if _struct_6d is None:
        _struct_6d = struct.Struct("<6d")
    return _struct_6d
