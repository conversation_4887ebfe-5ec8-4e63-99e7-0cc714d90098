# This Python file uses the following encoding: utf-8
"""autogenerated by genpy from lgsvl_msgs/VehicleStateData.msg. Do not edit."""
import codecs
import sys
python3 = True if sys.hexversion > 0x03000000 else False
import genpy
import struct

import std_msgs.msg

class VehicleStateData(genpy.Message):
  _md5sum = "b4405ea9889445c8f7934126d18f047c"
  _type = "lgsvl_msgs/VehicleStateData"
  _has_header = True  # flag to mark the presence of a Header object
  _full_text = """std_msgs/Header header

uint8 blinker_state
uint8 headlight_state
uint8 wiper_state
uint8 current_gear
uint8 vehicle_mode
bool hand_brake_active
bool horn_active
bool autonomous_mode_active

uint8 BLINKERS_OFF = 0
uint8 BLINKERS_LEFT = 1
uint8 BLINKERS_RIGHT = 2
uint8 BLINKERS_HAZARD = 3

uint8 HEADLIGHTS_OFF = 0
uint8 HEADLIGHTS_LOW = 1
uint8 HEADLIGHTS_HIGH = 2

uint8 WIPERS_OFF = 0
uint8 WIPERS_LOW = 1
uint8 WIPERS_MED = 2
uint8 WIPERS_HIGH = 3

uint8 GEAR_NEUTRAL = 0
uint8 GEAR_DRIVE = 1
uint8 GEAR_REVERSE = 2
uint8 GEAR_PARKING = 3
uint8 GEAR_LOW = 4

uint8 VEHICLE_MODE_COMPLETE_MANUAL = 0
uint8 VEHICLE_MODE_COMPLETE_AUTO_DRIVE = 1
uint8 VEHICLE_MODE_AUTO_STEER_ONLY = 2
uint8 VEHICLE_MODE_AUTO_SPEED_ONLY = 3
uint8 VEHICLE_MODE_EMERGENCY_MODE = 4

================================================================================
MSG: std_msgs/Header
# Standard metadata for higher-level stamped data types.
# This is generally used to communicate timestamped data 
# in a particular coordinate frame.
# 
# sequence ID: consecutively increasing ID 
uint32 seq
#Two-integer timestamp that is expressed as:
# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')
# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')
# time-handling sugar is provided by the client library
time stamp
#Frame this data is associated with
string frame_id
"""
  # Pseudo-constants
  BLINKERS_OFF = 0
  BLINKERS_LEFT = 1
  BLINKERS_RIGHT = 2
  BLINKERS_HAZARD = 3
  HEADLIGHTS_OFF = 0
  HEADLIGHTS_LOW = 1
  HEADLIGHTS_HIGH = 2
  WIPERS_OFF = 0
  WIPERS_LOW = 1
  WIPERS_MED = 2
  WIPERS_HIGH = 3
  GEAR_NEUTRAL = 0
  GEAR_DRIVE = 1
  GEAR_REVERSE = 2
  GEAR_PARKING = 3
  GEAR_LOW = 4
  VEHICLE_MODE_COMPLETE_MANUAL = 0
  VEHICLE_MODE_COMPLETE_AUTO_DRIVE = 1
  VEHICLE_MODE_AUTO_STEER_ONLY = 2
  VEHICLE_MODE_AUTO_SPEED_ONLY = 3
  VEHICLE_MODE_EMERGENCY_MODE = 4

  __slots__ = ['header','blinker_state','headlight_state','wiper_state','current_gear','vehicle_mode','hand_brake_active','horn_active','autonomous_mode_active']
  _slot_types = ['std_msgs/Header','uint8','uint8','uint8','uint8','uint8','bool','bool','bool']

  def __init__(self, *args, **kwds):
    """
    Constructor. Any message fields that are implicitly/explicitly
    set to None will be assigned a default value. The recommend
    use is keyword arguments as this is more robust to future message
    changes.  You cannot mix in-order arguments and keyword arguments.

    The available fields are:
       header,blinker_state,headlight_state,wiper_state,current_gear,vehicle_mode,hand_brake_active,horn_active,autonomous_mode_active

    :param args: complete set of field values, in .msg order
    :param kwds: use keyword arguments corresponding to message field names
    to set specific fields.
    """
    if args or kwds:
      super(VehicleStateData, self).__init__(*args, **kwds)
      # message fields cannot be None, assign default values for those that are
      if self.header is None:
        self.header = std_msgs.msg.Header()
      if self.blinker_state is None:
        self.blinker_state = 0
      if self.headlight_state is None:
        self.headlight_state = 0
      if self.wiper_state is None:
        self.wiper_state = 0
      if self.current_gear is None:
        self.current_gear = 0
      if self.vehicle_mode is None:
        self.vehicle_mode = 0
      if self.hand_brake_active is None:
        self.hand_brake_active = False
      if self.horn_active is None:
        self.horn_active = False
      if self.autonomous_mode_active is None:
        self.autonomous_mode_active = False
    else:
      self.header = std_msgs.msg.Header()
      self.blinker_state = 0
      self.headlight_state = 0
      self.wiper_state = 0
      self.current_gear = 0
      self.vehicle_mode = 0
      self.hand_brake_active = False
      self.horn_active = False
      self.autonomous_mode_active = False

  def _get_types(self):
    """
    internal API method
    """
    return self._slot_types

  def serialize(self, buff):
    """
    serialize message into buffer
    :param buff: buffer, ``StringIO``
    """
    try:
      _x = self
      buff.write(_get_struct_3I().pack(_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs))
      _x = self.header.frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_8B().pack(_x.blinker_state, _x.headlight_state, _x.wiper_state, _x.current_gear, _x.vehicle_mode, _x.hand_brake_active, _x.horn_active, _x.autonomous_mode_active))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize(self, str):
    """
    unpack serialized message in str into this message instance
    :param str: byte array of serialized message, ``str``
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.header is None:
        self.header = std_msgs.msg.Header()
      end = 0
      _x = self
      start = end
      end += 12
      (_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs,) = _get_struct_3I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.header.frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.header.frame_id = str[start:end]
      _x = self
      start = end
      end += 8
      (_x.blinker_state, _x.headlight_state, _x.wiper_state, _x.current_gear, _x.vehicle_mode, _x.hand_brake_active, _x.horn_active, _x.autonomous_mode_active,) = _get_struct_8B().unpack(str[start:end])
      self.hand_brake_active = bool(self.hand_brake_active)
      self.horn_active = bool(self.horn_active)
      self.autonomous_mode_active = bool(self.autonomous_mode_active)
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill


  def serialize_numpy(self, buff, numpy):
    """
    serialize message with numpy array types into buffer
    :param buff: buffer, ``StringIO``
    :param numpy: numpy python module
    """
    try:
      _x = self
      buff.write(_get_struct_3I().pack(_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs))
      _x = self.header.frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_8B().pack(_x.blinker_state, _x.headlight_state, _x.wiper_state, _x.current_gear, _x.vehicle_mode, _x.hand_brake_active, _x.horn_active, _x.autonomous_mode_active))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize_numpy(self, str, numpy):
    """
    unpack serialized message in str into this message instance using numpy for array types
    :param str: byte array of serialized message, ``str``
    :param numpy: numpy python module
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.header is None:
        self.header = std_msgs.msg.Header()
      end = 0
      _x = self
      start = end
      end += 12
      (_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs,) = _get_struct_3I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.header.frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.header.frame_id = str[start:end]
      _x = self
      start = end
      end += 8
      (_x.blinker_state, _x.headlight_state, _x.wiper_state, _x.current_gear, _x.vehicle_mode, _x.hand_brake_active, _x.horn_active, _x.autonomous_mode_active,) = _get_struct_8B().unpack(str[start:end])
      self.hand_brake_active = bool(self.hand_brake_active)
      self.horn_active = bool(self.horn_active)
      self.autonomous_mode_active = bool(self.autonomous_mode_active)
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill

_struct_I = genpy.struct_I
def _get_struct_I():
    global _struct_I
    return _struct_I
_struct_3I = None
def _get_struct_3I():
    global _struct_3I
    if _struct_3I is None:
        _struct_3I = struct.Struct("<3I")
    return _struct_3I
_struct_8B = None
def _get_struct_8B():
    global _struct_8B
    if _struct_8B is None:
        _struct_8B = struct.Struct("<8B")
    return _struct_8B
