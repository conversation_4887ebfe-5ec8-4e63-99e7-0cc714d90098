# This Python file uses the following encoding: utf-8
"""autogenerated by genpy from lgsvl_msgs/VehicleControlData.msg. Do not edit."""
import codecs
import sys
python3 = True if sys.hexversion > 0x03000000 else False
import genpy
import struct

import std_msgs.msg

class VehicleControlData(genpy.Message):
  _md5sum = "80f174108b1ade1a32a40bb254bac7ba"
  _type = "lgsvl_msgs/VehicleControlData"
  _has_header = True  # flag to mark the presence of a Header object
  _full_text = """std_msgs/Header header

float32 acceleration_pct  # 0 to 1
float32 braking_pct  # 0 to 1
float32 target_wheel_angle  # radians
float32 target_wheel_angular_rate  # radians / second
float32 ctr_cmd_acc # map control real expact acc value
uint8 target_gear

uint8 GEAR_NEUTRAL = 0
uint8 GEAR_DRIVE = 1
uint8 GEAR_REVERSE = 2
uint8 GEAR_PARKING = 3
uint8 GEAR_LOW = 4



================================================================================
MSG: std_msgs/Header
# Standard metadata for higher-level stamped data types.
# This is generally used to communicate timestamped data 
# in a particular coordinate frame.
# 
# sequence ID: consecutively increasing ID 
uint32 seq
#Two-integer timestamp that is expressed as:
# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')
# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')
# time-handling sugar is provided by the client library
time stamp
#Frame this data is associated with
string frame_id
"""
  # Pseudo-constants
  GEAR_NEUTRAL = 0
  GEAR_DRIVE = 1
  GEAR_REVERSE = 2
  GEAR_PARKING = 3
  GEAR_LOW = 4

  __slots__ = ['header','acceleration_pct','braking_pct','target_wheel_angle','target_wheel_angular_rate','ctr_cmd_acc','target_gear']
  _slot_types = ['std_msgs/Header','float32','float32','float32','float32','float32','uint8']

  def __init__(self, *args, **kwds):
    """
    Constructor. Any message fields that are implicitly/explicitly
    set to None will be assigned a default value. The recommend
    use is keyword arguments as this is more robust to future message
    changes.  You cannot mix in-order arguments and keyword arguments.

    The available fields are:
       header,acceleration_pct,braking_pct,target_wheel_angle,target_wheel_angular_rate,ctr_cmd_acc,target_gear

    :param args: complete set of field values, in .msg order
    :param kwds: use keyword arguments corresponding to message field names
    to set specific fields.
    """
    if args or kwds:
      super(VehicleControlData, self).__init__(*args, **kwds)
      # message fields cannot be None, assign default values for those that are
      if self.header is None:
        self.header = std_msgs.msg.Header()
      if self.acceleration_pct is None:
        self.acceleration_pct = 0.
      if self.braking_pct is None:
        self.braking_pct = 0.
      if self.target_wheel_angle is None:
        self.target_wheel_angle = 0.
      if self.target_wheel_angular_rate is None:
        self.target_wheel_angular_rate = 0.
      if self.ctr_cmd_acc is None:
        self.ctr_cmd_acc = 0.
      if self.target_gear is None:
        self.target_gear = 0
    else:
      self.header = std_msgs.msg.Header()
      self.acceleration_pct = 0.
      self.braking_pct = 0.
      self.target_wheel_angle = 0.
      self.target_wheel_angular_rate = 0.
      self.ctr_cmd_acc = 0.
      self.target_gear = 0

  def _get_types(self):
    """
    internal API method
    """
    return self._slot_types

  def serialize(self, buff):
    """
    serialize message into buffer
    :param buff: buffer, ``StringIO``
    """
    try:
      _x = self
      buff.write(_get_struct_3I().pack(_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs))
      _x = self.header.frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_5fB().pack(_x.acceleration_pct, _x.braking_pct, _x.target_wheel_angle, _x.target_wheel_angular_rate, _x.ctr_cmd_acc, _x.target_gear))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize(self, str):
    """
    unpack serialized message in str into this message instance
    :param str: byte array of serialized message, ``str``
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.header is None:
        self.header = std_msgs.msg.Header()
      end = 0
      _x = self
      start = end
      end += 12
      (_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs,) = _get_struct_3I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.header.frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.header.frame_id = str[start:end]
      _x = self
      start = end
      end += 21
      (_x.acceleration_pct, _x.braking_pct, _x.target_wheel_angle, _x.target_wheel_angular_rate, _x.ctr_cmd_acc, _x.target_gear,) = _get_struct_5fB().unpack(str[start:end])
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill


  def serialize_numpy(self, buff, numpy):
    """
    serialize message with numpy array types into buffer
    :param buff: buffer, ``StringIO``
    :param numpy: numpy python module
    """
    try:
      _x = self
      buff.write(_get_struct_3I().pack(_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs))
      _x = self.header.frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_5fB().pack(_x.acceleration_pct, _x.braking_pct, _x.target_wheel_angle, _x.target_wheel_angular_rate, _x.ctr_cmd_acc, _x.target_gear))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize_numpy(self, str, numpy):
    """
    unpack serialized message in str into this message instance using numpy for array types
    :param str: byte array of serialized message, ``str``
    :param numpy: numpy python module
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.header is None:
        self.header = std_msgs.msg.Header()
      end = 0
      _x = self
      start = end
      end += 12
      (_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs,) = _get_struct_3I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.header.frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.header.frame_id = str[start:end]
      _x = self
      start = end
      end += 21
      (_x.acceleration_pct, _x.braking_pct, _x.target_wheel_angle, _x.target_wheel_angular_rate, _x.ctr_cmd_acc, _x.target_gear,) = _get_struct_5fB().unpack(str[start:end])
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill

_struct_I = genpy.struct_I
def _get_struct_I():
    global _struct_I
    return _struct_I
_struct_3I = None
def _get_struct_3I():
    global _struct_3I
    if _struct_3I is None:
        _struct_3I = struct.Struct("<3I")
    return _struct_3I
_struct_5fB = None
def _get_struct_5fB():
    global _struct_5fB
    if _struct_5fB is None:
        _struct_5fB = struct.Struct("<5fB")
    return _struct_5fB
