# This Python file uses the following encoding: utf-8
"""autogenerated by genpy from lgsvl_msgs/DetectedRadarObject.msg. Do not edit."""
import codecs
import sys
python3 = True if sys.hexversion > 0x03000000 else False
import genpy
import struct

import geometry_msgs.msg

class DetectedRadarObject(genpy.Message):
  _md5sum = "ce4b6b01290125e6b2cd22dde070d1fb"
  _type = "lgsvl_msgs/DetectedRadarObject"
  _has_header = False  # flag to mark the presence of a Header object
  _full_text = """int32 id

geometry_msgs/Vector3 sensor_aim
geometry_msgs/Vector3 sensor_right
geometry_msgs/Point sensor_position
geometry_msgs/Vector3 sensor_velocity
float64 sensor_angle

geometry_msgs/Point object_position
geometry_msgs/Vector3 object_velocity
geometry_msgs/Point object_relative_position
geometry_msgs/Vector3 object_relative_velocity
geometry_msgs/Vector3 object_collider_size
uint8 object_state
bool new_detection

uint8 STATE_MOVING = 0
uint8 STATE_STATIONARY = 1

================================================================================
MSG: geometry_msgs/Vector3
# This represents a vector in free space. 
# It is only meant to represent a direction. Therefore, it does not
# make sense to apply a translation to it (e.g., when applying a 
# generic rigid transformation to a Vector3, tf2 will only apply the
# rotation). If you want your data to be translatable too, use the
# geometry_msgs/Point message instead.

float64 x
float64 y
float64 z
================================================================================
MSG: geometry_msgs/Point
# This contains the position of a point in free space
float64 x
float64 y
float64 z
"""
  # Pseudo-constants
  STATE_MOVING = 0
  STATE_STATIONARY = 1

  __slots__ = ['id','sensor_aim','sensor_right','sensor_position','sensor_velocity','sensor_angle','object_position','object_velocity','object_relative_position','object_relative_velocity','object_collider_size','object_state','new_detection']
  _slot_types = ['int32','geometry_msgs/Vector3','geometry_msgs/Vector3','geometry_msgs/Point','geometry_msgs/Vector3','float64','geometry_msgs/Point','geometry_msgs/Vector3','geometry_msgs/Point','geometry_msgs/Vector3','geometry_msgs/Vector3','uint8','bool']

  def __init__(self, *args, **kwds):
    """
    Constructor. Any message fields that are implicitly/explicitly
    set to None will be assigned a default value. The recommend
    use is keyword arguments as this is more robust to future message
    changes.  You cannot mix in-order arguments and keyword arguments.

    The available fields are:
       id,sensor_aim,sensor_right,sensor_position,sensor_velocity,sensor_angle,object_position,object_velocity,object_relative_position,object_relative_velocity,object_collider_size,object_state,new_detection

    :param args: complete set of field values, in .msg order
    :param kwds: use keyword arguments corresponding to message field names
    to set specific fields.
    """
    if args or kwds:
      super(DetectedRadarObject, self).__init__(*args, **kwds)
      # message fields cannot be None, assign default values for those that are
      if self.id is None:
        self.id = 0
      if self.sensor_aim is None:
        self.sensor_aim = geometry_msgs.msg.Vector3()
      if self.sensor_right is None:
        self.sensor_right = geometry_msgs.msg.Vector3()
      if self.sensor_position is None:
        self.sensor_position = geometry_msgs.msg.Point()
      if self.sensor_velocity is None:
        self.sensor_velocity = geometry_msgs.msg.Vector3()
      if self.sensor_angle is None:
        self.sensor_angle = 0.
      if self.object_position is None:
        self.object_position = geometry_msgs.msg.Point()
      if self.object_velocity is None:
        self.object_velocity = geometry_msgs.msg.Vector3()
      if self.object_relative_position is None:
        self.object_relative_position = geometry_msgs.msg.Point()
      if self.object_relative_velocity is None:
        self.object_relative_velocity = geometry_msgs.msg.Vector3()
      if self.object_collider_size is None:
        self.object_collider_size = geometry_msgs.msg.Vector3()
      if self.object_state is None:
        self.object_state = 0
      if self.new_detection is None:
        self.new_detection = False
    else:
      self.id = 0
      self.sensor_aim = geometry_msgs.msg.Vector3()
      self.sensor_right = geometry_msgs.msg.Vector3()
      self.sensor_position = geometry_msgs.msg.Point()
      self.sensor_velocity = geometry_msgs.msg.Vector3()
      self.sensor_angle = 0.
      self.object_position = geometry_msgs.msg.Point()
      self.object_velocity = geometry_msgs.msg.Vector3()
      self.object_relative_position = geometry_msgs.msg.Point()
      self.object_relative_velocity = geometry_msgs.msg.Vector3()
      self.object_collider_size = geometry_msgs.msg.Vector3()
      self.object_state = 0
      self.new_detection = False

  def _get_types(self):
    """
    internal API method
    """
    return self._slot_types

  def serialize(self, buff):
    """
    serialize message into buffer
    :param buff: buffer, ``StringIO``
    """
    try:
      _x = self
      buff.write(_get_struct_i28d2B().pack(_x.id, _x.sensor_aim.x, _x.sensor_aim.y, _x.sensor_aim.z, _x.sensor_right.x, _x.sensor_right.y, _x.sensor_right.z, _x.sensor_position.x, _x.sensor_position.y, _x.sensor_position.z, _x.sensor_velocity.x, _x.sensor_velocity.y, _x.sensor_velocity.z, _x.sensor_angle, _x.object_position.x, _x.object_position.y, _x.object_position.z, _x.object_velocity.x, _x.object_velocity.y, _x.object_velocity.z, _x.object_relative_position.x, _x.object_relative_position.y, _x.object_relative_position.z, _x.object_relative_velocity.x, _x.object_relative_velocity.y, _x.object_relative_velocity.z, _x.object_collider_size.x, _x.object_collider_size.y, _x.object_collider_size.z, _x.object_state, _x.new_detection))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize(self, str):
    """
    unpack serialized message in str into this message instance
    :param str: byte array of serialized message, ``str``
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.sensor_aim is None:
        self.sensor_aim = geometry_msgs.msg.Vector3()
      if self.sensor_right is None:
        self.sensor_right = geometry_msgs.msg.Vector3()
      if self.sensor_position is None:
        self.sensor_position = geometry_msgs.msg.Point()
      if self.sensor_velocity is None:
        self.sensor_velocity = geometry_msgs.msg.Vector3()
      if self.object_position is None:
        self.object_position = geometry_msgs.msg.Point()
      if self.object_velocity is None:
        self.object_velocity = geometry_msgs.msg.Vector3()
      if self.object_relative_position is None:
        self.object_relative_position = geometry_msgs.msg.Point()
      if self.object_relative_velocity is None:
        self.object_relative_velocity = geometry_msgs.msg.Vector3()
      if self.object_collider_size is None:
        self.object_collider_size = geometry_msgs.msg.Vector3()
      end = 0
      _x = self
      start = end
      end += 230
      (_x.id, _x.sensor_aim.x, _x.sensor_aim.y, _x.sensor_aim.z, _x.sensor_right.x, _x.sensor_right.y, _x.sensor_right.z, _x.sensor_position.x, _x.sensor_position.y, _x.sensor_position.z, _x.sensor_velocity.x, _x.sensor_velocity.y, _x.sensor_velocity.z, _x.sensor_angle, _x.object_position.x, _x.object_position.y, _x.object_position.z, _x.object_velocity.x, _x.object_velocity.y, _x.object_velocity.z, _x.object_relative_position.x, _x.object_relative_position.y, _x.object_relative_position.z, _x.object_relative_velocity.x, _x.object_relative_velocity.y, _x.object_relative_velocity.z, _x.object_collider_size.x, _x.object_collider_size.y, _x.object_collider_size.z, _x.object_state, _x.new_detection,) = _get_struct_i28d2B().unpack(str[start:end])
      self.new_detection = bool(self.new_detection)
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill


  def serialize_numpy(self, buff, numpy):
    """
    serialize message with numpy array types into buffer
    :param buff: buffer, ``StringIO``
    :param numpy: numpy python module
    """
    try:
      _x = self
      buff.write(_get_struct_i28d2B().pack(_x.id, _x.sensor_aim.x, _x.sensor_aim.y, _x.sensor_aim.z, _x.sensor_right.x, _x.sensor_right.y, _x.sensor_right.z, _x.sensor_position.x, _x.sensor_position.y, _x.sensor_position.z, _x.sensor_velocity.x, _x.sensor_velocity.y, _x.sensor_velocity.z, _x.sensor_angle, _x.object_position.x, _x.object_position.y, _x.object_position.z, _x.object_velocity.x, _x.object_velocity.y, _x.object_velocity.z, _x.object_relative_position.x, _x.object_relative_position.y, _x.object_relative_position.z, _x.object_relative_velocity.x, _x.object_relative_velocity.y, _x.object_relative_velocity.z, _x.object_collider_size.x, _x.object_collider_size.y, _x.object_collider_size.z, _x.object_state, _x.new_detection))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize_numpy(self, str, numpy):
    """
    unpack serialized message in str into this message instance using numpy for array types
    :param str: byte array of serialized message, ``str``
    :param numpy: numpy python module
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.sensor_aim is None:
        self.sensor_aim = geometry_msgs.msg.Vector3()
      if self.sensor_right is None:
        self.sensor_right = geometry_msgs.msg.Vector3()
      if self.sensor_position is None:
        self.sensor_position = geometry_msgs.msg.Point()
      if self.sensor_velocity is None:
        self.sensor_velocity = geometry_msgs.msg.Vector3()
      if self.object_position is None:
        self.object_position = geometry_msgs.msg.Point()
      if self.object_velocity is None:
        self.object_velocity = geometry_msgs.msg.Vector3()
      if self.object_relative_position is None:
        self.object_relative_position = geometry_msgs.msg.Point()
      if self.object_relative_velocity is None:
        self.object_relative_velocity = geometry_msgs.msg.Vector3()
      if self.object_collider_size is None:
        self.object_collider_size = geometry_msgs.msg.Vector3()
      end = 0
      _x = self
      start = end
      end += 230
      (_x.id, _x.sensor_aim.x, _x.sensor_aim.y, _x.sensor_aim.z, _x.sensor_right.x, _x.sensor_right.y, _x.sensor_right.z, _x.sensor_position.x, _x.sensor_position.y, _x.sensor_position.z, _x.sensor_velocity.x, _x.sensor_velocity.y, _x.sensor_velocity.z, _x.sensor_angle, _x.object_position.x, _x.object_position.y, _x.object_position.z, _x.object_velocity.x, _x.object_velocity.y, _x.object_velocity.z, _x.object_relative_position.x, _x.object_relative_position.y, _x.object_relative_position.z, _x.object_relative_velocity.x, _x.object_relative_velocity.y, _x.object_relative_velocity.z, _x.object_collider_size.x, _x.object_collider_size.y, _x.object_collider_size.z, _x.object_state, _x.new_detection,) = _get_struct_i28d2B().unpack(str[start:end])
      self.new_detection = bool(self.new_detection)
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill

_struct_I = genpy.struct_I
def _get_struct_I():
    global _struct_I
    return _struct_I
_struct_i28d2B = None
def _get_struct_i28d2B():
    global _struct_i28d2B
    if _struct_i28d2B is None:
        _struct_i28d2B = struct.Struct("<i28d2B")
    return _struct_i28d2B
