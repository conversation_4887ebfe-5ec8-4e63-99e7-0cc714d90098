#/usr/bin/python3
# -*- coding: utf-8 -*-


# rabbitmq 配置信息
mq_config = {
    "host": "**********",
    "port": 5672,
    "vhost": "/",
    "user": "admin",
    "passwd": "admin"
}

# 火山云mq配置
mq_config_hs = {
    "host": "*************",
    "port": 56720
}

# cos 配置信息
cos_config = {
    "secret_id": 'AKIDkOoyrTqwkHYYHjvqUrwlJaaKnztWsxlw',
    "secret_key": 'xd18zauwiFVTAbkcDZdQwBArHQItDG2Y',
    "region": 'ap-beijing',
    "bucketName": 'autocar-mogosim-1255510688'
}


# ssh file transfer protocol 配置信息
sftp_config = {
    "port": 22,
    "username": "work",
    "password": "<EMAIL>"
}

#
logstash_config = {
    "port": 9093,
    "ip": "*********"
}

# redis 配置信息
redis_config = {
    "host": "**********",
    "port": 6379,
    "db": 10,
    "password": "MlXC0OUnYKbuzdVJ"
}