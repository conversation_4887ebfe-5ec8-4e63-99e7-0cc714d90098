# /usr/bin/python3
# -*- coding: utf-8 -*-

import os
import time
import yaml
from utils.loggers import LoggerProxy

logger = LoggerProxy("lgsvl")

from utils.file_utils import get_setup_value
from utils.platform.platform_service import PlatformService
from utils.report import warn_report


config_file = os.path.expanduser("~/.lgsvl/config")
platform_service = PlatformService(get_setup_value("cloud", "cloud_config"))


class Config(object):  # 默认配置
    DEBUG = False

    # get attribute
    def __getitem__(self, key):
        return self.__getattribute__(key)


class ProductionConfig(Config):  # 生产环境
    SIM_HOST = "https://mogosim-open.zhidaoauto.com"
    LGSVL_SIM_IP = ""
    ROS_BRIDGE_IP = os.environ["MATHER_HOST_IP"]
    ROS_BRIDGE_PORT = os.environ["ROSBRIDGE_PORT"]
    SENSOR_CONF_ID = "DefaultVehicleConfig.json"
    MYFOLW_SERVER_ENDPOINT = (
        "https://myflow.zhidaoauto.com/backend/simulation/callSimulation"
    )
    SIM_TASK_RESULT_URL = SIM_HOST + "/admin/v1/taskManagement/simTask/finishSimAds"
    SIM_TASK_CASE_LOG_URL = (
        SIM_HOST + "/admin/v1/taskManagement/simTask/updateSimAdsFilePath"
    )
    SIM_ADS_DOWNLOAD_URL = SIM_HOST + "/admin/v1/scenarioManagement/case/download"
    SIM_SUB_TASK_PROGRESS_STATE_URL = (
        SIM_HOST + "/admin/v1/taskManagement/simTask/updateSimSubTaskProgress"
    )
    SIM_TASK_PROGRESS_STATE_URL = (
        SIM_HOST + "/admin/v1/taskManagement/simTask/updateSimTaskProgress"
    )
    SIM_TASK_DETAIL_URL = SIM_HOST + "/admin/v1/taskManagement/simTask"
    SUB_SIM_ADS_DETAIL_URL = (
        SIM_HOST + "/admin/v1/taskManagement/simTask/simAdsBySimSubTaskIdInPage"
    )
    CASE_REPORT = SIM_HOST + "/admin/v1/taskManagement/simTaskMonitor/report"
    CASE_START_REPORT = SIM_HOST + "/admin/v1/taskManagement/simTask/startSimAds"

    MCC_GET_URL = SIM_HOST + "/admin/mcc/config/get"
    TASK_ATTACHMENT_URL = SIM_HOST + "/admin/mcc/task-cfg/get_attach"
    TASK_MQ_QUEUE = "worldsim_prod"
    TASK_MQ_SWITCH = True
    ENV = "prod"
    APP = "worldSimClient"
    SIM_RUN_TIME_SCALE = 1
    SIM_TIME_OUT_SCALE = 2
    WARN_NOTIFY_SWITCH = True
    SERVER_PORT = 50008
    WIN_SIM_RESTART_URL = ""
    LGSVL_EXT_CMDS = {"all"}
    SPECIAL_SIM_IP_QUEUE = False
    VIDEO_OPEN = True
    SIM_RESTART_OPEN = True


class TestConfig(Config):  # TEST环境
    SIM_HOST = "http://**********:8080"
    LGSVL_SIM_IP = ""
    ROS_BRIDGE_IP = os.environ["MATHER_HOST_IP"]
    ROS_BRIDGE_PORT = os.environ["ROSBRIDGE_PORT"]
    SENSOR_CONF_ID = "DefaultVehicleConfig.json"
    MYFOLW_SERVER_ENDPOINT = (
        "https://myflow.zhidaoauto.com/backend/simulation/callSimulation"
    )
    SIM_TASK_RESULT_URL = SIM_HOST + "/admin/v1/taskManagement/simTask/finishSimAds"
    SIM_TASK_CASE_LOG_URL = (
        SIM_HOST + "/admin/v1/taskManagement/simTask/updateSimAdsFilePath"
    )
    SIM_ADS_DOWNLOAD_URL = SIM_HOST + "/admin/v1/scenarioManagement/case/download"
    SIM_SUB_TASK_PROGRESS_STATE_URL = (
        SIM_HOST + "/admin/v1/taskManagement/simTask/updateSimSubTaskProgress"
    )
    SIM_TASK_PROGRESS_STATE_URL = (
        SIM_HOST + "/admin/v1/taskManagement/simTask/updateSimTaskProgress"
    )
    SIM_TASK_DETAIL_URL = SIM_HOST + "/admin/v1/taskManagement/simTask"
    SUB_SIM_ADS_DETAIL_URL = (
        SIM_HOST + "/admin/v1/taskManagement/simTask/simAdsBySimSubTaskIdInPage"
    )
    CASE_REPORT = SIM_HOST + "/admin/v1/taskManagement/simTaskMonitor/report"
    CASE_START_REPORT = SIM_HOST + "/admin/v1/taskManagement/simTask/startSimAds"

    MCC_GET_URL = SIM_HOST + "/admin/mcc/config/get"
    TASK_ATTACHMENT_URL = SIM_HOST + "/admin/mcc/task-cfg/get_attach"
    TASK_MQ_QUEUE = "worldsim_test"
    TASK_MQ_SWITCH = True
    ENV = "test"
    APP = "worldSimClient"
    SIM_RUN_TIME_SCALE = 1
    SIM_TIME_OUT_SCALE = 2
    WARN_NOTIFY_SWITCH = True
    SERVER_PORT = 50008
    WIN_SIM_RESTART_URL = ""
    LGSVL_EXT_CMDS = {"all"}
    SPECIAL_SIM_IP_QUEUE = False
    VIDEO_OPEN = True
    SIM_RESTART_OPEN = True


def load_conf():
    global config
    if not os.path.exists(config_file):
        logger.error("没有找到配置文件，请先配置配置文件 %s" % config_file)
        exit()
    cfh = open(config_file)
    conf_data = yaml.safe_load(cfh)
    cfh.close()
    if type(conf_data) is not dict:
        logger.error("cannot retrieve configuration from %s" % config_file)
        exit()
    config.TASK_MQ_SWITCH = conf_data.get("task_mq_switch", True)
    config.SIM_RUN_TIME_SCALE = conf_data.get("sim_run_time_scale", 1)
    config.SIM_TIME_OUT_SCALE = conf_data.get("sim_time_out_scale", 1.5)
    config.WARN_NOTIFY_SWITCH = conf_data.get("warn_notify_switch", True)
    config.VIDEO_OPEN = conf_data.get("video_open", True)
    config.SIM_RESTART_OPEN = conf_data.get("sim_restart_open", True)
    os.environ["warn_notify_switch"] = str(config.WARN_NOTIFY_SWITCH)
    config.SERVER_PORT = conf_data.get("server_port", 50008)
    _get_sim_ip_from_cos()
    config.WIN_SIM_RESTART_URL = "http://" + config.LGSVL_SIM_IP + ":8182/swm/restart"
    config.WIN_SIM_RECORD_START_URL = (
        "http://" + config.LGSVL_SIM_IP + ":8182/swm/record/start"
    )
    config.WIN_SIM_RECORD_STOP_URL = (
        "http://" + config.LGSVL_SIM_IP + ":8182/swm/record/stop"
    )
    config.WIN_SIM_SVN_UPDATE_URL = (
        "http://" + config.LGSVL_SIM_IP + ":8182/swm/svn/update"
    )
    config.WIN_SIM_VERSION = (
        "http://" + config.LGSVL_SIM_IP + ":8182/swm/version_checkout"
    )
    config.WIN_SIM_GET_VERSION = (
        "http://" + config.LGSVL_SIM_IP + ":8182/swm/cur_branch"
    )
    config.WIN_SIM_AUTO_UPGRADE_URL = (
        "http://" + config.LGSVL_SIM_IP + ":8182/swm/self_update_restart"
    )
    config.LGSVL_EXT_CMDS = set(conf_data.get("lgsvl_ext_cmds", {"all"}))
    config.LGSVL_EXT_CMDS.add("EXT_MAP_NAME")
    config.LGSVL_EXT_CMDS.add("EXT_API_MODE")
    config.LGSVL_EXT_CMDS.add("EXT_INIT_V")
    config.LGSVL_EXT_CMDS.add("EXT_META")
    config.LGSVL_EXT_CMDS.add("EXT_CTR")
    config.LGSVL_EXT_CMDS.add("EXT_ENV")
    config.LGSVL_EXT_CMDS.add("EXT_PREDICT")
    config.LGSVL_EXT_CMDS.add("EXT_CUSTOM_DATA")
    config.LGSVL_EXT_CMDS.add("EXT_CONTOURS")
    config.LGSVL_EXT_CMDS.add("EXT_CASE_CONTENT")
    config.SPECIAL_SIM_IP_QUEUE = conf_data.get("special_sim_ip_open", False)


def _get_sim_ip_from_cos():
    global config_file
    while len(config.LGSVL_SIM_IP) <= 0:
        try:
            mather_host_ip = os.environ["MATHER_HOST_IP"]
            ros_bridge_port = os.environ["ROSBRIDGE_PORT"]
            conf_key = mather_host_ip + "_" + ros_bridge_port

            # 可以cos在线编辑配置文件内容
            cos_config_file = "lgsvl_client_sim_config.yaml"
            local_cos_config_path = os.path.join(
                os.path.dirname(config_file), cos_config_file
            )
            platform_service.download_from_cloud(
                config.ENV, cos_config_file, local_cos_config_path
            )
            cfh = open(local_cos_config_path)
            conf_data = yaml.safe_load(cfh)
            cfh.close()
            config.LGSVL_SIM_IP = conf_data.get(conf_key, "")
            if len(config.LGSVL_SIM_IP) <= 0:
                warn_report("获取模拟器IP失败，请在cos配置文件中进行配置:" + conf_key)
                time.sleep(5)
            else:
                os.environ["SIM_IP"] = config.LGSVL_SIM_IP
                logger.info("获取模拟器IP成功:" + conf_key + ":" + config.LGSVL_SIM_IP)
        except Exception as e:
            logger.exception(e)

            time.sleep(5)


# 环境映射关系
mapping = {"test": TestConfig, "prod": ProductionConfig, "default": TestConfig}

env = os.environ.get("SIM_ENV")
logger.info("starting with env %s" % env)
if env != "test" and env != "prod":
    logger.error("参数错误,必须传环境变量!比如: python xx.py test|prod")
    exit()

APP_ENV = env.lower()
config = mapping[APP_ENV]()  # 实例化对应的环境
load_conf()
logger.info("start env {} config {}".format(env, config_file))
print("start with env:", env)
