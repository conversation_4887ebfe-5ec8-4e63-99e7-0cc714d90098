# /usr/bin/python3
# -*- coding: utf-8 -*-

import os
import time
import rospy
from utils.loggers import LoggerProxy

logger = LoggerProxy("routing_request")
import hashlib

# autopilot msgs
from autopilot_msgs.msg import BinaryData
from common.localization_pb2 import Localization

# helper function
from api.lgsvl_client.utils.lgsvl_utils import frameTransform


# 本地实现task_env模块功能
def get_map_version():
    """返回地图版本号"""
    return "2040"


def get_vehicle_type():
    """返回车辆类型"""
    return "bus"


class lgsvlRoutingRequest(object):
    def __init__(
        self,
        start_x=0,
        start_y=0,
        start_yaw=0,
        dest_x: float = 0.0,
        dest_y: float = 0.0,
        utm_zone: int = 50,
    ):
        logger.info(
            "[lgsvlRoutingRequest] 初始化开始 - 起点:({}, {}), 终点:({}, {}), yaw:{}, UTM区域:{}",
            start_x,
            start_y,
            dest_x,
            dest_y,
            start_yaw,
            utm_zone,
        )
        self.start_x = start_x
        self.start_y = start_y
        self.start_yaw = start_yaw
        self.dest_x = dest_x
        self.dest_y = dest_y
        self.utm_zone = utm_zone
        self.routing_success = False
        self.sim_time_estimate = None
        self.routing_sub = None
        self.version = 3

        try:
            self.routing_request_pub = rospy.Publisher(
                "/routing/request", BinaryData, queue_size=1
            )
            logger.info("[lgsvlRoutingRequest] 路径规划请求发布者创建成功: /routing/request")
            
            self.ego_loc_pub = rospy.Publisher(
                "/localization/global", BinaryData, queue_size=1, tcp_nodelay=True
            )
            logger.info("[lgsvlRoutingRequest] 定位信息发布者创建成功: /localization/global")
        except Exception as e:
            logger.error("[lgsvlRoutingRequest] 创建 ROS 发布者失败: {}", e)
            raise

        # 由于get_map_version()返回"2040"，直接使用routing_250
        self.autopilot_version = 5
        try:
            import importlib
            self.rr = importlib.import_module("api.lgsvl_client.routing_250.routing_pb2")
            logger.info("[lgsvlRoutingRequest] 成功导入 routing_250.routing_pb2 模块")
        except ImportError as e:
            logger.error("[lgsvlRoutingRequest] 导入 routing_250.routing_pb2 模块失败: {}", e)
            raise
        
        logger.info("[lgsvlRoutingRequest] 初始化完成")

    def reset_navigation(
        self,
        start_x=0,
        start_y=0,
        dest_x: float = 0.0,
        dest_y: float = 0.0,
        utm_zone: int = 50,
    ):
        logger.info(
            "[lgsvlRoutingRequest] 重置导航参数 - 起点:({}, {}), 终点:({}, {}), UTM区域:{}",
            start_x, start_y, dest_x, dest_y, utm_zone
        )
        self.start_x = start_x
        self.start_y = start_y
        self.dest_x = dest_x
        self.dest_y = dest_y
        self.utm_zone = utm_zone
        self.routing_success = False
        self.sim_time_estimate = None
        logger.info("[lgsvlRoutingRequest] 导航参数重置完成")

    def send_routing_request(self):
        logger.info("[lgsvlRoutingRequest] 方法开始执行")
        logger.info("[lgsvlRoutingRequest] 开始发送路径规划请求")
        logger.info(
            "[lgsvlRoutingRequest] 请求参数 - 起点UTM:({}, {}), 终点UTM:({}, {}), 起点yaw:{}, UTM区域:{}",
            self.start_x, self.start_y, self.dest_x, self.dest_y, self.start_yaw, self.utm_zone
        )
        
        logger.info("[lgsvlRoutingRequest] 准备获取ROS时间")
        try:
            now = rospy.Time.now()
            logger.info("[lgsvlRoutingRequest] 获取当前 ROS 时间成功: {}.{}", now.secs, now.nsecs)
            
            logger.info("[lgsvlRoutingRequest] 准备创建坐标转换器")
            ft = frameTransform(self.utm_zone)
            logger.info("[lgsvlRoutingRequest] 创建坐标系转换器成功 - UTM区域: {}", self.utm_zone)
            
            logger.info("[lgsvlRoutingRequest] 准备进行坐标转换")
            s_lon, s_lat = ft.UtmXYToLatlon(self.start_x, self.start_y)
            logger.info("[lgsvlRoutingRequest] 起点坐标转换完成")
            e_lon, e_lat = ft.UtmXYToLatlon(self.dest_x, self.dest_y)
            logger.info("[lgsvlRoutingRequest] 终点坐标转换完成")
            logger.info(
                "[lgsvlRoutingRequest] 坐标转换完成 - 起点经纬度:({}, {}), 终点经纬度:({}, {})",
                s_lon, s_lat, e_lon, e_lat
            )
        except Exception as e:
            logger.error("[lgsvlRoutingRequest] 坐标转换失败: {}", e)
            return
        try:
            routing_request_msg = self.rr.RoutingRequest()
            logger.info("[lgsvlRoutingRequest] 创建 RoutingRequest 消息对象成功")
        except Exception as e:
            logger.error("[lgsvlRoutingRequest] 创建 RoutingRequest 消息对象失败: {}", e)
            return
        routing_request_msg.header.stamp.sec = now.secs
        routing_request_msg.header.stamp.nsec = now.nsecs
        routing_request_msg.header.frame_id = "map"
        routing_request_msg.header.module_name = "hadmap.RoutingRequest"
        routing_request_msg.start.pose.x = self.start_x
        routing_request_msg.start.pose.y = self.start_y
        routing_request_msg.end.pose.x = self.dest_x
        routing_request_msg.end.pose.y = self.dest_y
        routing_request_msg.start.pos_lon_lat.x = s_lon
        routing_request_msg.start.pos_lon_lat.y = s_lat
        routing_request_msg.end.pos_lon_lat.x = e_lon
        routing_request_msg.end.pos_lon_lat.y = e_lat
        routing_request_msg.start_point.x = s_lon
        routing_request_msg.start_point.y = s_lat
        routing_request_msg.end_point.x = e_lon
        routing_request_msg.end_point.y = e_lat
        routing_request_msg.dpqp = False
        vehicle_type_str = get_vehicle_type()
        routing_request_msg.vehicleType = 9 if vehicle_type_str == "taxi" else 10
        logger.info(
            "[lgsvlRoutingRequest] 设置车辆类型 - 类型字符串:{}, 类型代码:{}",
            vehicle_type_str, routing_request_msg.vehicleType
        )
        routing_request_msg.bus_routeid = 0
        routing_request_msg.bus_routename = "{}_{}_{}_{}".format(
            self.round5(s_lon),
            self.round5(s_lat),
            self.round5(e_lon),
            self.round5(e_lat),
        )
        try:
            traj_path = "/data1/traj_files/traj_1.csv"
            if os.path.exists(traj_path):
                routing_request_msg.traj_md5 = get_directory_md5(traj_path)
                logger.info("[lgsvlRoutingRequest] 轨迹文件MD5计算成功: {}", routing_request_msg.traj_md5)
            else:
                logger.warning("[lgsvlRoutingRequest] 轨迹文件不存在: {}", traj_path)
                routing_request_msg.traj_md5 = ""
        except Exception as e:
            logger.error("[lgsvlRoutingRequest] 计算轨迹文件MD5失败: {}", e)
            routing_request_msg.traj_md5 = ""
        routing_request_msg.lineid = 1
        try:
            routing_request_data = routing_request_msg.SerializeToString()
            logger.info("[lgsvlRoutingRequest] RoutingRequest 消息序列化成功，数据大小: {} 字节", len(routing_request_data))
            
            routing_request = BinaryData()
            routing_request.header.stamp = now
            routing_request.header.frame_id = "utm"
            routing_request.name = "hadmap.RoutingRequest"
            routing_request.data = routing_request_data
            routing_request.size = len(routing_request_data)
            logger.info("[lgsvlRoutingRequest] BinaryData 消息封装成功")
        except Exception as e:
            logger.error("[lgsvlRoutingRequest] 消息序列化或封装失败: {}", e)
            return

        try:
            location_msg = Localization()
            location_msg.header.stamp.sec = now.secs
            location_msg.header.stamp.nsec = now.nsecs
            location_msg.header.frame_id = "utm"
            location_msg.header.module_name = "localization.Localization"
            location_msg.position.x = self.start_x
            location_msg.position.y = self.start_y
            logger.info("[lgsvlRoutingRequest] 创建定位消息 - 位置:({}, {})", self.start_x, self.start_y)
            
            try:
                yaw = self.start_yaw
                if yaw is not None:
                    location_msg.yaw = float(yaw)
                    logger.info("[lgsvlRoutingRequest] 设置 yaw 角: {}", yaw)
                else:
                    location_msg.yaw = 0
                    logger.warning("[lgsvlRoutingRequest] yaw 角为空，设置为 0")
            except Exception as e:
                logger.error("[lgsvlRoutingRequest] 设置 yaw 角失败: {}", e)
                location_msg.yaw = 0
            
            location_msg.utm_zone = self.utm_zone
            location_msg.longitude = s_lon
            location_msg.latitude = s_lat
            logger.info(
                "[lgsvlRoutingRequest] 定位信息设置完成 - UTM区域:{}, 经纬度:({}, {}), yaw:{}",
                self.utm_zone, s_lon, s_lat, location_msg.yaw
            )
        except Exception as e:
            logger.error("[lgsvlRoutingRequest] 创建定位消息失败: {}", e)
            return

        logger.info("[lgsvlRoutingRequest] 路径规划请求消息内容: {}", routing_request_msg)
        logger.info("[lgsvlRoutingRequest] 定位消息内容: {}", location_msg)

        # protobuf
        try:
            location_data = location_msg.SerializeToString()
            logger.info("[lgsvlRoutingRequest] 定位消息序列化成功，数据大小: {} 字节", len(location_data))
            
            location = BinaryData()
            location.header.stamp = now
            location.header.frame_id = "utm"
            location.name = "localization.Localization"
            location.size = len(location_data)
            location.data = location_data
            logger.info("[lgsvlRoutingRequest] 定位 BinaryData 消息封装成功")
        except Exception as e:
            logger.error("[lgsvlRoutingRequest] 定位消息序列化或封装失败: {}", e)
            return

        # send request iterately. loop wait for 5s
        logger.info("[lgsvlRoutingRequest] 开始发送循环 - 频率:100Hz, 最大重试次数:20")
        try:
            retry_cnt = 0
            
            while (
                retry_cnt < 20
                and rospy.get_param("/routing/request/rsp", 0) != 1
                and self.routing_success == False
            ):
                rsp_param = rospy.get_param("/routing/request/rsp", 0)
                logger.info("[lgsvlRoutingRequest] 循环{}次 - ROS参数 rsp_param = {}", retry_cnt, rsp_param)
                if rsp_param == 1:
                    self.routing_success = True
                    logger.info("[lgsvlRoutingRequest] 通过ROS参数检测到路径规划成功，退出循环 - 第{}次", retry_cnt)
                    break
                    
                logger.info(
                    "[lgsvlRoutingRequest] 发送循环状态 - 第{}次, ROS参数响应:{}, 路径规划成功:{}",
                    retry_cnt, rsp_param, self.routing_success
                )
                try:
                    now = rospy.Time.now()
                    location_msg.header.stamp.sec = now.secs
                    location_msg.header.stamp.nsec = now.nsecs
                    location_data = location_msg.SerializeToString()
                    location.header.stamp = now
                    location.data = location_data
                    
                    # publish
                    self.ego_loc_pub.publish(location)
                    
                    if retry_cnt % 10 == 0:  # 每10次发送一次路径规划请求
                        logger.info(
                            "[lgsvlRoutingRequest] 发送路径规划请求 - 第{}次循环",
                            retry_cnt + 1
                        )
                        self.ego_loc_pub.publish(location)
                        # 等定位处理完，有的版本轨迹处理依赖定位信息
                        time.sleep(0.1)
                        routing_request.header.stamp = rospy.Time.now()
                        self.routing_request_pub.publish(routing_request)
                        logger.info("[lgsvlRoutingRequest] 路径规划请求已发送")
                    
                    # 使用简单的time.sleep避免ROS Rate阻塞问题
                    time.sleep(0.01)  # 10毫秒延时，等效于100Hz
                    
                except Exception as e:
                    logger.error("[lgsvlRoutingRequest] 发送循环中出错 - 第{}次: {}", retry_cnt, e)
                
                # 无论正常还是异常，都需要增加计数器
                retry_cnt += 1
        except Exception as e:
            logger.error("[lgsvlRoutingRequest] 发送循环初始化失败: {}", e)
            return
        # 检查最终状态
        logger.info("[lgsvlRoutingRequest] 准备检查最终状态")
        final_rsp_param = rospy.get_param("/routing/request/rsp", 0)
        logger.info("[lgsvlRoutingRequest] 获取到最终ROS参数: {}", final_rsp_param)
        if retry_cnt >= 20:
            logger.info("[lgsvlRoutingRequest] 循环结束原因: 达到最大重试次数20次")
        elif self.routing_success:
            logger.info("[lgsvlRoutingRequest] 循环结束原因: 检测到路径规划成功")
        else:
            logger.info("[lgsvlRoutingRequest] 循环结束原因: 未知")
            
        logger.info(
            "[lgsvlRoutingRequest] 发送循环结束 - 总重试次数:{}, ROS参数响应:{}, 路径规划成功:{}",
            retry_cnt, final_rsp_param, self.routing_success
        )
        
        # 最后等待阶段
        logger.info("[lgsvlRoutingRequest] 进入最后等待阶段 - 等待5秒")
        wait_cnt = 5
        try:
            while self.routing_success == False and wait_cnt > 0:
                # 检查ROS参数状态
                rsp_param = rospy.get_param("/routing/request/rsp", 0)
                if rsp_param == 1:
                    self.routing_success = True
                    logger.info("[lgsvlRoutingRequest] 通过ROS参数检测到路径规划成功")
                    break
                    
                logger.info(
                    "[lgsvlRoutingRequest] 等待中 - 剩余时间:{}秒, 路径规划成功:{}, ROS参数:{}",
                    wait_cnt, self.routing_success, rsp_param
                )
                time.sleep(1)  # 1秒延时
                wait_cnt -= 1
        except Exception as e:
            logger.error("[lgsvlRoutingRequest] 最后等待阶段出错: {}", e)
        
        # 最终状态检查
        if self.routing_success:
            logger.info("[lgsvlRoutingRequest] 路径规划请求成功完成")
        else:
            logger.warning("[lgsvlRoutingRequest] 路径规划请求失败或超时")
        
        logger.info("[lgsvlRoutingRequest] send_routing_request 方法执行完成")

    def round5(self, input):
        return "{:.5f}".format(input - input % 0.00001)


def get_directory_md5(directory):
    """
    计算目录或文件的MD5值
    
    参数:
        directory (str): 目录或文件路径
        
    返回:
        str: MD5字符串
    """
    logger.info("[get_directory_md5] 开始计算MD5 - 路径: {}", directory)
    
    try:
        if not os.path.exists(directory):
            logger.error("[get_directory_md5] 路径不存在: {}", directory)
            return ""
        
        md5 = hashlib.md5()
        file_count = 0
        total_size = 0
        
        if os.path.isfile(directory):
            # 如果是文件，直接计算文件MD5
            logger.info("[get_directory_md5] 检测到文件，计算文件MD5: {}", directory)
            try:
                with open(directory, "rb") as f:
                    for chunk in iter(lambda: f.read(4096), b""):
                        md5.update(chunk)
                        total_size += len(chunk)
                file_count = 1
            except IOError as e:
                logger.error("[get_directory_md5] 读取文件失败: {}", e)
                return ""
        else:
            # 如果是目录，遍历所有文件
            logger.info("[get_directory_md5] 检测到目录，遍历所有文件: {}", directory)
            for dirpath, _, fnames in os.walk(directory):
                for fname in fnames:
                    file_path = os.path.join(dirpath, fname)
                    logger.debug("[get_directory_md5] 处理文件: {}", file_path)
                    try:
                        with open(file_path, "rb") as f:
                            for chunk in iter(lambda: f.read(4096), b""):
                                md5.update(chunk)
                                total_size += len(chunk)
                        file_count += 1
                    except IOError as e:
                        logger.warning("[get_directory_md5] 跳过无法读取的文件 {}: {}", file_path, e)
                        continue
        
        result = md5.hexdigest()
        logger.info(
            "[get_directory_md5] MD5计算完成 - 文件数:{}, 总大小:{}字节, MD5:{}",
            file_count, total_size, result
        )
        return result
        
    except Exception as e:
        logger.error("[get_directory_md5] MD5计算过程中出错: {}", e)
        return ""
