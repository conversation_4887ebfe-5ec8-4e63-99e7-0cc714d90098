#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import argparse
import os
import math
import json
import rosbag

from google.protobuf.message import DecodeError

# 假设 my_proto 目录与此脚本在同一路径下或在 PYTHONPATH 中
try:
    from common.localization_pb2 import Localization
    from common.trajectory_pb2 import Trajectory
except ImportError:
    print("错误：无法导入 my_proto 中的protobuf定义。")
    print("请确保 'my_proto' 文件夹与此脚本位于同一目录，或已添加到 PYTHONPATH。")
    print("并且该文件夹包含编译后的 localization_pb2.py 和 trajectory_pb2.py。")
    exit(1)

# 全局变量，用于存储从bag文件中解析出来的数据
ego_info_list = []  # 存储自车位姿信息
trajectory_info_list = []  # 存储轨迹点信息


# 加载区域配置文件
def load_zones(zones_file="zone.json"):
    """加载并解析区域配置文件"""
    try:
        with open(zones_file, "r") as f:
            return json.load(f)
    except FileNotFoundError:
        print("警告: 区域配置文件 {} 未找到，将使用原始坐标数据".format(zones_file))
        return []
    except json.JSONDecodeError:
        print("错误: 区域配置文件 {} 格式错误，将使用原始坐标数据".format(zones_file))
        return []


# 根据坐标查找对应的区域
def find_zone_for_point(x, y, zones):
    """根据坐标点查找对应的区域"""
    for zone in zones:
        if (
            zone["Left_East"] <= x <= zone["Right_East"]
            and zone["Left_North"] <= y <= zone["Right_North"]
        ):
            return zone
    return None


def parse_bag_file(bag_file_path):
    """
    解析指定的 bag 文件，提取自车位姿和规划轨迹信息。

    参数:
        bag_file_path (str): bag 文件的路径。
    """
    print("开始解析 bag 文件: {}".format(bag_file_path))
    found_localization = False
    found_trajectory = False

    # 加载区域配置（只需加载一次）
    zones = load_zones()

    try:
        with rosbag.Bag(bag_file_path, "r") as bag:
            for topic, msg, t in bag.read_messages(
                topics=["/localization/global", "/planning/global_trajectory"]
            ):
                if topic == "/localization/global":
                    found_localization = True
                    try:
                        localization_data = Localization()
                        localization_data.ParseFromString(msg.data)

                        timestamp_secs = localization_data.header.stamp.sec
                        timestamp_nsecs = localization_data.header.stamp.nsec
                        full_timestamp = float(
                            "{}.{}".format(
                                timestamp_secs, str(timestamp_nsecs).zfill(9)
                            )
                        )

                        # 获取原始坐标
                        raw_x = localization_data.position.x
                        raw_y = localization_data.position.y

                        # 使用已加载的区域配置
                        # 查找对应的区域
                        zone = find_zone_for_point(raw_x, raw_y, zones)

                        if zone:
                            # 计算相对于区域原点的坐标
                            adjusted_x = raw_x - zone["Origin_East"]
                            adjusted_y = raw_y - zone["Origin_North"]
                            # 打印详细日志
                            # print("坐标点 ({}, {}) 位于区域 {}，原点为 ({}, {})，调整后坐标为 ({}, {})".format(
                            #     raw_x, raw_y, zone['Name'], zone['Origin_East'], zone['Origin_North'], adjusted_x, adjusted_y))
                        else:
                            # 如果没有找到匹配区域，使用原始坐标并打印警告
                            adjusted_x = raw_x
                            adjusted_y = raw_y
                            print(
                                "警告: 在区域中未找到坐标点 ({}, {})，使用原始坐标".format(
                                    raw_x, raw_y
                                )
                            )

                        ego_entry = {
                            "timestamp": full_timestamp,
                            "x": adjusted_x,
                            "y": adjusted_y,
                            "z": localization_data.position.z,
                            "pitch": localization_data.pitch,
                            "yaw": localization_data.yaw,  # yaw in radians
                            "roll": localization_data.roll,
                        }
                        ego_info_list.append(ego_entry)
                    except DecodeError:
                        print("警告: 解码 /localization/global 消息失败。")
                    except Exception as e:
                        print("处理 /localization/global 消息时发生错误: {}".format(e))

                elif topic == "/planning/global_trajectory":
                    found_trajectory = True
                    try:
                        trajectory_data = Trajectory()
                        trajectory_data.ParseFromString(msg.data)

                        for point in trajectory_data.points:
                            # 参考 logsim2worldsimApi.py 中的 trajectory_i 结构
                            # [x, y, 0,0,0, kappa, 0,0, theta, 0, s, 0,0,0,0,0,0,0,0,0,0,0]
                            # traj_1 (x), traj_2 (y), traj_6 (kappa), traj_9 (theta/yaw), traj_11 (s)
                            trajectory_point_formatted = [
                                point.x,
                                point.y,
                                0.0,
                                0.0,
                                0.0,
                                point.kappa,
                                0.0,
                                0.0,
                                point.theta,
                                0.0,
                                point.s,
                                0.0,
                                0.0,
                                0.0,
                                0.0,
                                0.0,
                                0.0,
                                0.0,
                                0.0,
                                0.0,
                                0.0,
                                0.0,
                            ]
                            if (
                                trajectory_point_formatted not in trajectory_info_list
                            ):  # 避免重复（如果源bag可能有重复）
                                trajectory_info_list.append(trajectory_point_formatted)
                    except DecodeError:
                        print("警告: 解码 /planning/global_trajectory 消息失败。")
                    except Exception as e:
                        print(
                            "处理 /planning/global_trajectory 消息时发生错误: {}".format(
                                e
                            )
                        )

        # 对ego_info_list按时间戳排序，确保ego_info_list[0]是最早的点
        if ego_info_list:
            ego_info_list.sort(key=lambda e: e["timestamp"])

    except rosbag.bag.BagException as e:
        print("错误: 无法读取 bag 文件: {}".format(e))
        return
    except Exception as e:
        print("解析 bag 文件时发生未知错误: {}".format(e))
        return

    if not found_localization:
        print("警告: Bag 文件中未找到 /localization/global topic。起点信息可能不完整。")
    if not found_trajectory:
        print(
            "警告: Bag 文件中未找到 /planning/global_trajectory topic。终点和轨迹文件可能无法生成。"
        )

    print("Bag 文件解析完成。")


def extract_and_print_positions():
    """
    提取并打印起点和终点位置。
    """
    print("\n--- 起点和终点位置 ---")
    start_pos_valid = False
    if ego_info_list:
        start_data = ego_info_list[0]
        start_x = start_data["x"]
        start_y = start_data["y"]
        start_yaw_rad = start_data["yaw"]
        start_yaw_deg = math.degrees(start_yaw_rad)
        print("起点位置 (从 /localization/global 获取):")
        print("  X: {:.6f}".format(start_x))
        print("  Y: {:.6f}".format(start_y))
        print("  Yaw (弧度): {:.6f}".format(start_yaw_rad))
        print("  Yaw (角度): {:.2f}°".format(start_yaw_deg))
        start_pos_valid = True
    else:
        print("未能从 /localization/global 提取起点信息 (ego_info_list 为空)。")

    end_pos_valid = False
    if trajectory_info_list:
        # trajectory_info_list 中的每个元素是一个包含22个浮点数的列表
        # 索引0是x, 索引1是y, 索引8是theta (yaw in radians)
        end_data_raw = trajectory_info_list[-1]
        raw_end_x = end_data_raw[0]
        raw_end_y = end_data_raw[1]
        end_yaw_rad = end_data_raw[8]
        end_yaw_deg = math.degrees(end_yaw_rad)

        # 加载区域配置
        zones = load_zones()
        # 查找对应的区域
        zone = find_zone_for_point(raw_end_x, raw_end_y, zones)

        if zone:
            # 计算相对于区域原点的坐标
            end_x = raw_end_x - zone["Origin_East"]
            end_y = raw_end_y - zone["Origin_North"]
            # 打印详细日志
            print(
                "终点坐标点 ({}, {}) 位于区域 {}，原点为 ({}, {})，调整后坐标为 ({}, {})".format(
                    raw_end_x,
                    raw_end_y,
                    zone["Name"],
                    zone["Origin_East"],
                    zone["Origin_North"],
                    end_x,
                    end_y,
                )
            )
        else:
            # 如果没有找到匹配区域，使用原始坐标并打印警告
            end_x = raw_end_x
            end_y = raw_end_y
            print(
                "警告: 在区域中未找到终点坐标点 ({}, {})，使用原始坐标".format(
                    raw_end_x, raw_end_y
                )
            )

        print("\n终点位置 (从 /planning/global_trajectory 的最后一个点获取):")
        print("  X: {:.6f}".format(end_x))
        print("  Y: {:.6f}".format(end_y))
        print("  Yaw (弧度): {:.6f}".format(end_yaw_rad))
        print("  Yaw (角度): {:.2f}°".format(end_yaw_deg))
        end_pos_valid = True
    else:
        print(
            "未能从 /planning/global_trajectory 提取终点信息 (trajectory_info_list 为空)。"
        )

    if not start_pos_valid and not end_pos_valid:
        print("\n未能提取任何有效的起点或终点信息。")


def save_trajectory_file(output_dir="."):
    """
    将提取的轨迹数据保存到 traj.csv 文件。

    参数:
        output_dir (str): traj.csv 文件保存的目录路径。默认为当前目录。
    """
    if not trajectory_info_list:
        print("\n轨迹信息为空，不生成 traj.csv 文件。")
        return

    output_path = os.path.join(output_dir, "traj.csv")

    try:
        print("\n正在保存轨迹文件到: {}".format(output_path))
        with open(output_path, "w") as f:
            for traj_point_data in trajectory_info_list:
                # traj_point_data 是一个包含22个数字的列表
                # 转换列表为字符串，并移除方括号，类似原始项目的做法
                traj_str = str(traj_point_data).replace("[", "").replace("]", "")
                f.write(traj_str + "\n")
        print(
            "轨迹文件 traj.csv 已成功保存。共 {} 个轨迹点。".format(
                len(trajectory_info_list)
            )
        )
    except IOError as e:
        print("错误: 无法写入轨迹文件 {}: {}".format(output_path, e))
    except Exception as e:
        print("保存轨迹文件时发生未知错误: {}".format(e))


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="解析bag文件，提取起点、终点位置并生成轨迹文件。"
    )
    parser.add_argument("bag_file", help="要解析的bag文件路径。")
    parser.add_argument(
        "--output_dir",
        default=".",
        help="轨迹文件 (traj.csv) 的输出目录。默认为当前目录。",
    )

    args = parser.parse_args()

    if not os.path.isfile(args.bag_file):
        print("错误: bag文件 '{}' 不存在或不是一个文件。".format(args.bag_file))
        exit(1)

    if not os.path.isdir(args.output_dir):
        try:
            os.makedirs(args.output_dir)
            print("输出目录 '{}' 不存在，已创建。".format(args.output_dir))
        except OSError as e:
            print("错误: 无法创建输出目录 '{}': {}".format(args.output_dir, e))
            exit(1)

    parse_bag_file(args.bag_file)
    extract_and_print_positions()
    save_trajectory_file(args.output_dir)
    print("\n处理完成。")
