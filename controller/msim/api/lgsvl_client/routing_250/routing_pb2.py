# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: routing.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
from google.protobuf import descriptor_pb2
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import geometry_pb2 as geometry__pb2
import header_pb2 as header__pb2
import hadmap_pb2 as hadmap__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='routing.proto',
  package='hadmap',
  syntax='proto2',
  serialized_pb=_b('\n\rrouting.proto\x12\x06hadmap\x1a\x0egeometry.proto\x1a\x0cheader.proto\x1a\x0chadmap.proto\"R\n\x0fGlobalTrajState\x12\x0f\n\x07line_id\x18\x01 \x02(\x04\x12\x0f\n\x07md5_sum\x18\x02 \x02(\t\x12\r\n\x05is_ok\x18\x03 \x02(\x08\x12\x0e\n\x06reason\x18\x04 \x01(\t\"E\n\x12StartPilotResponse\x12\x0f\n\x07line_id\x18\x01 \x01(\x04\x12\x0e\n\x06status\x18\x02 \x01(\x05\x12\x0e\n\x06reason\x18\x03 \x01(\t\"\x86\x01\n\x08Waypoint\x12\n\n\x02id\x18\x01 \x01(\t\x12\t\n\x01s\x18\x02 \x01(\x01\x12\x1d\n\x04pose\x18\x03 \x01(\x0b\x32\x0f.geometry.Point\x12\r\n\x05theta\x18\x04 \x01(\x01\x12\x0f\n\x07station\x18\x05 \x01(\x08\x12$\n\x0bpos_lon_lat\x18\x06 \x01(\x0b\x32\x0f.geometry.Point\"\xba\x04\n\x0eRoutingRequest\x12\x1e\n\x06header\x18\x01 \x01(\x0b\x32\x0e.common.Header\x12\x0b\n\x03map\x18\x02 \x01(\t\x12\x0f\n\x07\x66\x65\x61ture\x18\x03 \x01(\t\x12\x1f\n\x05start\x18\x04 \x01(\x0b\x32\x10.hadmap.Waypoint\x12\x1d\n\x03\x65nd\x18\x05 \x01(\x0b\x32\x10.hadmap.Waypoint\x12\"\n\x08waypoint\x18\x06 \x03(\x0b\x32\x10.hadmap.Waypoint\x12\x12\n\nspeedlimit\x18\x07 \x01(\x01\x12\x11\n\tstartName\x18\x08 \x01(\t\x12\x0f\n\x07\x65ndName\x18\t \x01(\t\x12\x13\n\x0bvehicleType\x18\n \x01(\x05\x12\x13\n\x0b\x62us_routeid\x18\x0b \x01(\r\x12\x15\n\rbus_routename\x18\x0c \x01(\t\x12\x0e\n\x06lineid\x18\r \x01(\x04\x12\x12\n\x04\x64pqp\x18\x0e \x01(\x08:\x04true\x12\x1c\n\rexist_xy_flag\x18\x0f \x01(\x08:\x05\x66\x61lse\x12$\n\x0bstart_point\x18\x10 \x01(\x0b\x32\x0f.geometry.Point\x12\"\n\tend_point\x18\x11 \x01(\x0b\x32\x0f.geometry.Point\x12\x10\n\x08traj_md5\x18\x12 \x01(\t\x12&\n\x0c\x62lack_points\x18\x14 \x03(\x0b\x32\x10.hadmap.Waypoint\x12\x17\n\x0fstation_request\x18\x15 \x01(\x08\x12\x1c\n\x14\x66irst_autopilot_flag\x18\x16 \x01(\x08\x12\x10\n\x08order_id\x18\x17 \x01(\t\"X\n\rRouteRoadLink\x12 \n\nroute_road\x18\x01 \x01(\x0b\x32\x0c.hadmap.Road\x12%\n\x0broute_links\x18\x02 \x03(\x0b\x32\x10.hadmap.LaneLink\"p\n\tRoutePath\x12\x10\n\x08\x64istance\x18\x01 \x01(\x01\x12\x0c\n\x04time\x18\x02 \x01(\x01\x12\x13\n\x0bmin_weights\x18\x03 \x01(\r\x12.\n\x0froute_roadlinks\x18\x04 \x03(\x0b\x32\x15.hadmap.RouteRoadLink\"\xf4\x01\n\rRoutingResult\x12\x1e\n\x06header\x18\x01 \x01(\x0b\x32\x0e.common.Header\x12\x0f\n\x07version\x18\x02 \x01(\t\x12+\n\x0broute_state\x18\x03 \x01(\x0e\x32\x16.hadmap.LoadRouteState\x12\x1f\n\x05start\x18\x04 \x01(\x0b\x32\x10.hadmap.Waypoint\x12\x1d\n\x03\x65nd\x18\x05 \x01(\x0b\x32\x10.hadmap.Waypoint\x12#\n\twaypoints\x18\x06 \x03(\x0b\x32\x10.hadmap.Waypoint\x12 \n\x05paths\x18\x07 \x03(\x0b\x32\x11.hadmap.RoutePath*y\n\x0eLoadRouteState\x12\x12\n\x0eLoad_file_Succ\x10\x00\x12\x12\n\x0eLoad_file_Fail\x10\x01\x12\x15\n\x11Load_file_Unknown\x10\x02\x12\x0b\n\x07RP_Succ\x10\x03\x12\x0b\n\x07RP_Fail\x10\x04\x12\x0e\n\nRP_Unknown\x10\x05')
  ,
  dependencies=[geometry__pb2.DESCRIPTOR,header__pb2.DESCRIPTOR,hadmap__pb2.DESCRIPTOR,])
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

_LOADROUTESTATE = _descriptor.EnumDescriptor(
  name='LoadRouteState',
  full_name='hadmap.LoadRouteState',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='Load_file_Succ', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='Load_file_Fail', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='Load_file_Unknown', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='RP_Succ', index=3, number=3,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='RP_Fail', index=4, number=4,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='RP_Unknown', index=5, number=5,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=1385,
  serialized_end=1506,
)
_sym_db.RegisterEnumDescriptor(_LOADROUTESTATE)

LoadRouteState = enum_type_wrapper.EnumTypeWrapper(_LOADROUTESTATE)
Load_file_Succ = 0
Load_file_Fail = 1
Load_file_Unknown = 2
RP_Succ = 3
RP_Fail = 4
RP_Unknown = 5



_GLOBALTRAJSTATE = _descriptor.Descriptor(
  name='GlobalTrajState',
  full_name='hadmap.GlobalTrajState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='line_id', full_name='hadmap.GlobalTrajState.line_id', index=0,
      number=1, type=4, cpp_type=4, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='md5_sum', full_name='hadmap.GlobalTrajState.md5_sum', index=1,
      number=2, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='is_ok', full_name='hadmap.GlobalTrajState.is_ok', index=2,
      number=3, type=8, cpp_type=7, label=2,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='reason', full_name='hadmap.GlobalTrajState.reason', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=69,
  serialized_end=151,
)


_STARTPILOTRESPONSE = _descriptor.Descriptor(
  name='StartPilotResponse',
  full_name='hadmap.StartPilotResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='line_id', full_name='hadmap.StartPilotResponse.line_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='status', full_name='hadmap.StartPilotResponse.status', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='reason', full_name='hadmap.StartPilotResponse.reason', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=153,
  serialized_end=222,
)


_WAYPOINT = _descriptor.Descriptor(
  name='Waypoint',
  full_name='hadmap.Waypoint',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='hadmap.Waypoint.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='s', full_name='hadmap.Waypoint.s', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='pose', full_name='hadmap.Waypoint.pose', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='theta', full_name='hadmap.Waypoint.theta', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='station', full_name='hadmap.Waypoint.station', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='pos_lon_lat', full_name='hadmap.Waypoint.pos_lon_lat', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=225,
  serialized_end=359,
)


_ROUTINGREQUEST = _descriptor.Descriptor(
  name='RoutingRequest',
  full_name='hadmap.RoutingRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='hadmap.RoutingRequest.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='map', full_name='hadmap.RoutingRequest.map', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='feature', full_name='hadmap.RoutingRequest.feature', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='start', full_name='hadmap.RoutingRequest.start', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='end', full_name='hadmap.RoutingRequest.end', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='waypoint', full_name='hadmap.RoutingRequest.waypoint', index=5,
      number=6, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='speedlimit', full_name='hadmap.RoutingRequest.speedlimit', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='startName', full_name='hadmap.RoutingRequest.startName', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='endName', full_name='hadmap.RoutingRequest.endName', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='vehicleType', full_name='hadmap.RoutingRequest.vehicleType', index=9,
      number=10, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='bus_routeid', full_name='hadmap.RoutingRequest.bus_routeid', index=10,
      number=11, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='bus_routename', full_name='hadmap.RoutingRequest.bus_routename', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='lineid', full_name='hadmap.RoutingRequest.lineid', index=12,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='dpqp', full_name='hadmap.RoutingRequest.dpqp', index=13,
      number=14, type=8, cpp_type=7, label=1,
      has_default_value=True, default_value=True,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='exist_xy_flag', full_name='hadmap.RoutingRequest.exist_xy_flag', index=14,
      number=15, type=8, cpp_type=7, label=1,
      has_default_value=True, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='start_point', full_name='hadmap.RoutingRequest.start_point', index=15,
      number=16, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='end_point', full_name='hadmap.RoutingRequest.end_point', index=16,
      number=17, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='traj_md5', full_name='hadmap.RoutingRequest.traj_md5', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='black_points', full_name='hadmap.RoutingRequest.black_points', index=18,
      number=20, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='station_request', full_name='hadmap.RoutingRequest.station_request', index=19,
      number=21, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='first_autopilot_flag', full_name='hadmap.RoutingRequest.first_autopilot_flag', index=20,
      number=22, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='order_id', full_name='hadmap.RoutingRequest.order_id', index=21,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=362,
  serialized_end=932,
)


_ROUTEROADLINK = _descriptor.Descriptor(
  name='RouteRoadLink',
  full_name='hadmap.RouteRoadLink',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='route_road', full_name='hadmap.RouteRoadLink.route_road', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='route_links', full_name='hadmap.RouteRoadLink.route_links', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=934,
  serialized_end=1022,
)


_ROUTEPATH = _descriptor.Descriptor(
  name='RoutePath',
  full_name='hadmap.RoutePath',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='distance', full_name='hadmap.RoutePath.distance', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='time', full_name='hadmap.RoutePath.time', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='min_weights', full_name='hadmap.RoutePath.min_weights', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='route_roadlinks', full_name='hadmap.RoutePath.route_roadlinks', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1024,
  serialized_end=1136,
)


_ROUTINGRESULT = _descriptor.Descriptor(
  name='RoutingResult',
  full_name='hadmap.RoutingResult',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='hadmap.RoutingResult.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='version', full_name='hadmap.RoutingResult.version', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='route_state', full_name='hadmap.RoutingResult.route_state', index=2,
      number=3, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='start', full_name='hadmap.RoutingResult.start', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='end', full_name='hadmap.RoutingResult.end', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='waypoints', full_name='hadmap.RoutingResult.waypoints', index=5,
      number=6, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='paths', full_name='hadmap.RoutingResult.paths', index=6,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1139,
  serialized_end=1383,
)

_WAYPOINT.fields_by_name['pose'].message_type = geometry__pb2._POINT
_WAYPOINT.fields_by_name['pos_lon_lat'].message_type = geometry__pb2._POINT
_ROUTINGREQUEST.fields_by_name['header'].message_type = header__pb2._HEADER
_ROUTINGREQUEST.fields_by_name['start'].message_type = _WAYPOINT
_ROUTINGREQUEST.fields_by_name['end'].message_type = _WAYPOINT
_ROUTINGREQUEST.fields_by_name['waypoint'].message_type = _WAYPOINT
_ROUTINGREQUEST.fields_by_name['start_point'].message_type = geometry__pb2._POINT
_ROUTINGREQUEST.fields_by_name['end_point'].message_type = geometry__pb2._POINT
_ROUTINGREQUEST.fields_by_name['black_points'].message_type = _WAYPOINT
_ROUTEROADLINK.fields_by_name['route_road'].message_type = hadmap__pb2._ROAD
_ROUTEROADLINK.fields_by_name['route_links'].message_type = hadmap__pb2._LANELINK
_ROUTEPATH.fields_by_name['route_roadlinks'].message_type = _ROUTEROADLINK
_ROUTINGRESULT.fields_by_name['header'].message_type = header__pb2._HEADER
_ROUTINGRESULT.fields_by_name['route_state'].enum_type = _LOADROUTESTATE
_ROUTINGRESULT.fields_by_name['start'].message_type = _WAYPOINT
_ROUTINGRESULT.fields_by_name['end'].message_type = _WAYPOINT
_ROUTINGRESULT.fields_by_name['waypoints'].message_type = _WAYPOINT
_ROUTINGRESULT.fields_by_name['paths'].message_type = _ROUTEPATH
DESCRIPTOR.message_types_by_name['GlobalTrajState'] = _GLOBALTRAJSTATE
DESCRIPTOR.message_types_by_name['StartPilotResponse'] = _STARTPILOTRESPONSE
DESCRIPTOR.message_types_by_name['Waypoint'] = _WAYPOINT
DESCRIPTOR.message_types_by_name['RoutingRequest'] = _ROUTINGREQUEST
DESCRIPTOR.message_types_by_name['RouteRoadLink'] = _ROUTEROADLINK
DESCRIPTOR.message_types_by_name['RoutePath'] = _ROUTEPATH
DESCRIPTOR.message_types_by_name['RoutingResult'] = _ROUTINGRESULT
DESCRIPTOR.enum_types_by_name['LoadRouteState'] = _LOADROUTESTATE

GlobalTrajState = _reflection.GeneratedProtocolMessageType('GlobalTrajState', (_message.Message,), dict(
  DESCRIPTOR = _GLOBALTRAJSTATE,
  __module__ = 'routing_pb2'
  # @@protoc_insertion_point(class_scope:hadmap.GlobalTrajState)
  ))
_sym_db.RegisterMessage(GlobalTrajState)

StartPilotResponse = _reflection.GeneratedProtocolMessageType('StartPilotResponse', (_message.Message,), dict(
  DESCRIPTOR = _STARTPILOTRESPONSE,
  __module__ = 'routing_pb2'
  # @@protoc_insertion_point(class_scope:hadmap.StartPilotResponse)
  ))
_sym_db.RegisterMessage(StartPilotResponse)

Waypoint = _reflection.GeneratedProtocolMessageType('Waypoint', (_message.Message,), dict(
  DESCRIPTOR = _WAYPOINT,
  __module__ = 'routing_pb2'
  # @@protoc_insertion_point(class_scope:hadmap.Waypoint)
  ))
_sym_db.RegisterMessage(Waypoint)

RoutingRequest = _reflection.GeneratedProtocolMessageType('RoutingRequest', (_message.Message,), dict(
  DESCRIPTOR = _ROUTINGREQUEST,
  __module__ = 'routing_pb2'
  # @@protoc_insertion_point(class_scope:hadmap.RoutingRequest)
  ))
_sym_db.RegisterMessage(RoutingRequest)

RouteRoadLink = _reflection.GeneratedProtocolMessageType('RouteRoadLink', (_message.Message,), dict(
  DESCRIPTOR = _ROUTEROADLINK,
  __module__ = 'routing_pb2'
  # @@protoc_insertion_point(class_scope:hadmap.RouteRoadLink)
  ))
_sym_db.RegisterMessage(RouteRoadLink)

RoutePath = _reflection.GeneratedProtocolMessageType('RoutePath', (_message.Message,), dict(
  DESCRIPTOR = _ROUTEPATH,
  __module__ = 'routing_pb2'
  # @@protoc_insertion_point(class_scope:hadmap.RoutePath)
  ))
_sym_db.RegisterMessage(RoutePath)

RoutingResult = _reflection.GeneratedProtocolMessageType('RoutingResult', (_message.Message,), dict(
  DESCRIPTOR = _ROUTINGRESULT,
  __module__ = 'routing_pb2'
  # @@protoc_insertion_point(class_scope:hadmap.RoutingResult)
  ))
_sym_db.RegisterMessage(RoutingResult)


# @@protoc_insertion_point(module_scope)
