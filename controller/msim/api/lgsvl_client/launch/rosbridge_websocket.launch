<launch>
  <arg default="9090" name="port" />
  <arg default="localhost" name="address" />
  <arg default="false" name="ssl" />
  <arg default="" name="certfile" />
  <arg default="" name="keyfile" />

  <arg default="5" name="retry_startup_delay" />

  <arg default="600" name="fragment_timeout" />
  <arg default="0" name="delay_between_messages" />
  <arg default="None" name="max_message_size" />
  <arg default="10" name="unregister_timeout" />
  <arg default="None" name="websocket_external_port" />

  <arg default="false" name="use_compression" />

  <arg default="false" name="authenticate" />

  <arg default="0" name="websocket_ping_interval" />
  <arg default="30" name="websocket_ping_timeout" />
  <arg default="true" name="websocket_null_origin" />

  <arg default="[*]" name="topics_glob" />
  <arg default="[*]" name="services_glob" />
  <arg default="[*]" name="params_glob" />
  <arg default="false" name="bson_only_mode" />
  
  <arg default="screen" name="output" />

  
  <arg default="default" name="binary_encoder" unless="$(arg bson_only_mode)" />

  <group if="$(arg ssl)">
    <node name="rosbridge_websocket" output="$(arg output)" pkg="rosbridge_server" type="rosbridge_websocket">
      <param name="certfile" value="$(arg certfile)" />
      <param name="keyfile" value="$(arg keyfile)" />
      <param name="authenticate" value="$(arg authenticate)" />
      <param name="port" value="$(arg port)" />
      <param name="address" value="$(arg address)" />
      <param name="retry_startup_delay" value="$(arg retry_startup_delay)" />
      <param name="fragment_timeout" value="$(arg fragment_timeout)" />
      <param name="delay_between_messages" value="$(arg delay_between_messages)" />
      <param name="max_message_size" value="$(arg max_message_size)" />
      <param name="unregister_timeout" value="$(arg unregister_timeout)" />
      <param name="use_compression" value="$(arg use_compression)" />

      <param name="websocket_ping_interval" value="$(arg websocket_ping_interval)" />
      <param name="websocket_ping_timeout" value="$(arg websocket_ping_timeout)" />
      <param name="websocket_external_port" value="$(arg websocket_external_port)" />
      <param name="websocket_null_origin" value="$(arg websocket_null_origin)" />

      <param name="topics_glob" value="$(arg topics_glob)" />
      <param name="services_glob" value="$(arg services_glob)" />
      <param name="params_glob" value="$(arg params_glob)" />
    </node>
  </group>
  <group unless="$(arg ssl)">
    <node name="rosbridge_websocket" output="$(arg output)" pkg="rosbridge_server" type="rosbridge_websocket">
      <param name="authenticate" value="$(arg authenticate)" />
      <param name="port" value="$(arg port)" />
      <param name="address" value="$(arg address)" />
      <param name="retry_startup_delay" value="$(arg retry_startup_delay)" />
      <param name="fragment_timeout" value="$(arg fragment_timeout)" />
      <param name="delay_between_messages" value="$(arg delay_between_messages)" />
      <param name="max_message_size" value="$(arg max_message_size)" />
      <param name="unregister_timeout" value="$(arg unregister_timeout)" />
      <param name="use_compression" value="$(arg use_compression)" />

      <param name="websocket_ping_interval" value="$(arg websocket_ping_interval)" />
      <param name="websocket_ping_timeout" value="$(arg websocket_ping_timeout)" />
      <param name="websocket_external_port" value="$(arg websocket_external_port)" />

      <param name="topics_glob" value="$(arg topics_glob)" />
      <param name="services_glob" value="$(arg services_glob)" />
      <param name="params_glob" value="$(arg params_glob)" />

      <param name="bson_only_mode" value="$(arg bson_only_mode)" />
    </node>
  </group>

  <node name="rosapi" output="$(arg output)" pkg="rosapi" type="rosapi_node">
    <param name="topics_glob" value="$(arg topics_glob)" />
    <param name="services_glob" value="$(arg services_glob)" />
    <param name="params_glob" value="$(arg params_glob)" />
  </node>
</launch>