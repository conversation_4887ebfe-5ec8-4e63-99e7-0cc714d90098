#!/usr/bin/python3
# -*- coding: utf-8 -*-
"""
预测轨迹发布器模块

功能:
1. 订阅预测系统的障碍物预测数据
2. 转换为LGSVL兼容的预测轨迹格式
3. 发布到/lgsvl/predict_traj话题
4. 支持多次初始化和清理，避免全局变量状态问题

遵循SOLID原则:
- 单一职责原则: 专门处理预测轨迹数据转换和发布
- 开闭原则: 通过继承可扩展功能
- 依赖倒置原则: 依赖抽象接口而非具体实现
"""

import rospy
from autopilot_msgs.msg import BinaryData
from lgsvl_msgs.msg import PredictTrajectory, RefTrajectory, TrajectoryPoint

from utils.loggers import LoggerProxy
logger = LoggerProxy("pub_prediction")


def get_vehicle_type():
    """
    获取车辆类型

    返回:
        str: 车辆类型，固定返回'bus'
    """
    return "bus"


class PredictionPublisher:
    """
    预测轨迹发布器类

    职责:
    - 接收和解析预测系统数据
    - 转换为LGSVL预测轨迹格式
    - 发布预测轨迹消息
    - 管理资源生命周期
    """

    def __init__(self):
        """初始化预测轨迹发布器"""
        # 状态管理
        self.stopped = False

        # 统计信息
        self.processed_count = 0
        self.published_count = 0
        self.seq = 1
        self.current_time = rospy.Time(0)

        # 发布器
        self.predict_traj_pub = None

        # 订阅者
        self.prediction_sub = None
        self.clock_sub = None

        logger.info("预测轨迹发布器初始化完成")

    def on_clock(self, msg):
        """从/clock话题更新当前时间"""
        if self.stopped:
            return
        try:
            from rosgraph_msgs.msg import Clock

            self.current_time = msg.clock
        except Exception as e:
            logger.error(f"on_clock函数中出现错误: {e}")

    def on_prediction_received(self, msg):
        """
        处理预测数据的主回调函数

        参数:
            msg (BinaryData): 包含预测数据的二进制消息
        """
        if self.stopped:
            logger.debug("处理器已停止，跳过预测数据处理")
            return

        logger.debug(f"预测数据回调函数被调用，消息大小: {len(msg.data)} bytes")

        # 按照原始代码的方式解析预测数据
        try:
            # 尝试导入预测protobuf格式 - 根据原始代码逻辑
            # 注意：原始代码区分taxi和其他车型，这里简化处理
            try:
                from common.prediction_pb2 import PredictionObjects

                predict_objs = PredictionObjects()
            except ImportError:
                logger.error("无法导入prediction_pb2，跳过处理")
                return

            predict_objs.ParseFromString(msg.data)
            logger.debug(f"预测数据解析成功")

        except Exception as e:
            logger.error(f"预测数据解析失败: {e}")
            return

        self.processed_count += 1

        # 按照您的C#消息结构创建LGSVL预测轨迹消息
        predict_trajs = PredictTrajectory()
        predict_trajs.header.stamp = (
            self.current_time
            if self.current_time != rospy.Time(0)
            else rospy.Time.now()
        )
        predict_trajs.header.frame_id = "base_link"
        predict_trajs.trajectory = []
        # 按照C#定义，PredictTrajectory有这些字段（虽然不赋值，但需要设置默认值）
        # predict_trajs.planningVel = 0.0
        # predict_trajs.planningAcc = 0.0

        # 获取预测对象列表 - 按照原始代码逻辑
        objs = (
            predict_objs.predictionObject
            if hasattr(predict_objs, "predictionObject")
            else predict_objs.objs if hasattr(predict_objs, "objs") else []
        )

        logger.debug(f"找到预测对象数量: {len(objs)}")

        # 处理每个预测对象 - 完全按照原始代码逻辑
        trajectory_count = 0
        for obj in objs:
            try:
                # 检查预测对象是否有轨迹数据 - 完全按照原始代码的检查方式
                if get_vehicle_type() != "taxi" and (
                    obj.trajectories is None
                    or len(obj.trajectories.trajectory) <= 0
                    or len(obj.trajectories.trajectory[0].path_point) <= 0
                ):
                    continue
                if get_vehicle_type() == "taxi" and (
                    obj.trajectory is None
                    or len(obj.trajectory) <= 0
                    or len(obj.trajectory[0].prediction_trajectory) <= 0
                ):
                    continue

                # 创建参考轨迹 - 按照您提供的C#消息结构
                ref_traj = RefTrajectory()
                ref_traj.header.stamp = rospy.Time.now()
                ref_traj.header.frame_id = "base_link"
                ref_traj.trajectory = []
                ref_traj.horn = 0  # 按照C#定义，RefTrajectory只有horn字段

                # 获取预测轨迹点 - 完全按照原始代码逻辑
                predict_points = (
                    obj.trajectories.trajectory[0].path_point
                    if get_vehicle_type() != "taxi"
                    else obj.trajectory[0].prediction_trajectory
                )

                logger.debug(f"对象 {trajectory_count} 的轨迹点数量: {len(predict_points)}")

                # 处理预测轨迹点 - 按照您的C#结构和坐标映射规则
                for predict_point in predict_points[::20]:
                    traj_pt = TrajectoryPoint()

                    # 按照您的要求：原始消息中的x对应east，y对应north
                    traj_pt.east = getattr(predict_point, "x", 0.0)  # x → east
                    traj_pt.north = getattr(predict_point, "y", 0.0)  # y → north

                    # 设置TrajectoryPoint的其他字段
                    traj_pt.time = 0.0
                    traj_pt.speed = 0.0
                    traj_pt.acc = 0.0
                    traj_pt.yaw = 0.0

                    ref_traj.trajectory.append(traj_pt)

                # 添加到预测轨迹数组
                predict_trajs.trajectory.append(ref_traj)
                trajectory_count += 1

            except Exception as e:
                logger.error(f"处理预测对象时发生错误: {e}")
                continue

        # 发布预测轨迹 - 按照原始代码逻辑
        if len(predict_trajs.trajectory) > 0:
            self.predict_traj_pub.publish(predict_trajs)
            self.published_count += 1

            logger.info(
                f"✓ 预测轨迹发布完成: 发布了{trajectory_count}条轨迹, "
                f"总处理次数={self.processed_count}, 总发布次数={self.published_count}"
            )
        else:
            logger.debug("没有有效的预测轨迹数据，跳过发布")

        # 更新序列号
        self.seq += 1

    def start(self):
        """启动预测轨迹发布器"""
        try:
            # 创建发布器
            self.predict_traj_pub = rospy.Publisher(
                "/lgsvl/predict_traj", PredictTrajectory, queue_size=1
            )

            # 创建订阅者 - 订阅预测系统话题
            self.prediction_sub = rospy.Subscriber(
                "/prediction/PredictionObstacles",
                BinaryData,
                self.on_prediction_received,
            )

            # 订阅时钟话题以保持时间同步
            from rosgraph_msgs.msg import Clock

            self.clock_sub = rospy.Subscriber("/clock", Clock, self.on_clock)

            logger.info("预测轨迹发布器启动成功")
            return True

        except Exception as e:
            logger.error(f"预测轨迹发布器启动时发生错误: {e}")
            return False

    def stop(self):
        """停止预测轨迹发布器"""
        try:
            self.stopped = True

            # 清理订阅者
            if self.prediction_sub:
                self.prediction_sub.unregister()
                self.prediction_sub = None

            if self.clock_sub:
                self.clock_sub.unregister()
                self.clock_sub = None

            # 清理发布器
            if self.predict_traj_pub:
                self.predict_traj_pub.unregister()
                self.predict_traj_pub = None

            logger.info("预测轨迹发布器资源已清理完成")

        except Exception as e:
            logger.error(f"清理预测轨迹发布器资源时出错: {e}")

    def set_stop(self, stop=True):
        """
        控制处理器的启停状态

        参数:
            stop (bool): True=停止, False=启动
        """
        if stop:
            # 如果要停止，调用完整的stop方法进行资源清理
            self.stop()
        else:
            # 如果要启动，设置为未停止状态
            self.stopped = False

        status = "已停止" if stop else "已启动"
        logger.info(f"预测轨迹发布器{status}")

    def get_statistics(self):
        """
        获取处理器统计信息

        返回:
            dict: 包含处理和发布统计的字典
        """
        return {
            "processed_count": self.processed_count,
            "published_count": self.published_count,
            "stopped": self.stopped,
            "seq": self.seq,
        }


def create_prediction_publisher():
    """
    工厂函数：创建预测轨迹发布器实例

    返回:
        PredictionPublisher: 预测轨迹发布器实例
    """
    return PredictionPublisher()


if __name__ == "__main__":
    try:
        rospy.init_node("prediction_publisher_node", anonymous=True)
        publisher = create_prediction_publisher()
        publisher.start()

        logger.info("预测轨迹发布器节点启动，等待消息...")
        rospy.spin()

        publisher.stop()
    except rospy.ROSInterruptException:
        logger.info("预测轨迹发布器已关闭")
    except Exception as e:
        logger.error(f"预测轨迹发布器主程序中出现未处理异常: {e}")
