#!/usr/bin/python3
# -*- coding: utf-8 -*-

# 目标：使用进程控制lgsvl的启动、关闭以及日志管理
# 1. 使用一个变量表示lgsvl的根目录，其路径下分别有一个执行脚本和一个停止脚本：bin/start_env.sh和bin/kill.sh。
# 执行脚本需要在一个进程中运行，并保留进程。当进程结束时，需要关闭这个进程，然后执行kill.sh。
# 2. 清理日志：在每次执行start_env.sh之前，需要清理日志。删除目录下的所有日志文件,日志目录有两个：根路径下的/home/<USER>/sim_lgsvl/logs和/home/<USER>/sim_lgsvl/SVLSimulator_Data_2021.3/Log

import os
import shutil
import subprocess
import logging
from typing import Optional

from utils.loggers import LoggerProxy

logger = LoggerProxy("lgsvl")

from utils.path import get_task_record_path


class LgsvlController:
    """
    用于控制LGSVL仿真环境的启动、关闭、录屏及日志管理。

    参数:
        lgsvl_root (str): LGSVL根目录路径。
        task_id (str): 任务ID，用于获取录制路径。
    """

    def __init__(self, lgsvl_root: str, task_id: Optional[str] = None):
        self.lgsvl_root = lgsvl_root
        self.task_id = task_id
        self.log_dirs = [
            os.path.join(self.lgsvl_root, "logs"),
            os.path.join(self.lgsvl_root, "SVLSimulator_Data_2021.3", "Log"),
        ]
        self.start_script = os.path.join(self.lgsvl_root, "bin", "start_env.sh")
        self.kill_script = os.path.join(self.lgsvl_root, "bin", "kill.sh")
        self.record_start_script = os.path.join(
            self.lgsvl_root, "bin", "record_start_pdp.sh"
        )
        self.record_stop_script = os.path.join(self.lgsvl_root, "bin", "record_stop.sh")
        self.process: Optional[subprocess.Popen] = None
        self.record_process: Optional[subprocess.Popen] = None
        self.logger = logger

    def clear_logs(self):
        """
        清理所有日志目录下的日志文件和子目录。
        """
        for log_dir in self.log_dirs:
            if os.path.exists(log_dir):
                for filename in os.listdir(log_dir):
                    file_path = os.path.join(log_dir, filename)
                    try:
                        if os.path.isfile(file_path) or os.path.islink(file_path):
                            os.remove(file_path)
                        elif os.path.isdir(file_path):
                            shutil.rmtree(file_path)
                    except Exception as e:
                        self.logger.error(f"无法删除 {file_path}: {e}")
            else:
                self.logger.warning(f"日志目录不存在: {log_dir}")

    def start(self):
        """
        清理日志后启动LGSVL仿真环境。
        """
        self.clear_logs()
        try:
            # 准备环境变量
            env = os.environ.copy()

            # 添加ROS_PACKAGE_PATH环境变量
            current_ros_package_path = env.get("ROS_PACKAGE_PATH", "")
            additional_paths = [
                "/home/<USER>/workspace/mount/dist-packages",
                "/opt/msim/api/lgsvl_client",
            ]

            for path in additional_paths:
                if current_ros_package_path:
                    current_ros_package_path = f"{current_ros_package_path}:{path}"
                else:
                    current_ros_package_path = path

            env["ROS_PACKAGE_PATH"] = current_ros_package_path

            # 添加PYTHONPATH环境变量
            current_pythonpath = env.get("PYTHONPATH", "")
            if current_pythonpath:
                env["PYTHONPATH"] = f"{current_pythonpath}:/opt/msim/api/lgsvl_client"
            else:
                env["PYTHONPATH"] = "/opt/msim/api/lgsvl_client"

            self.process = subprocess.Popen(
                ["bash", self.start_script],
                cwd=self.lgsvl_root,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                env=env,
            )
            self.logger.info("LGSVL仿真环境已启动。")
        except Exception as e:
            self.logger.error(f"启动LGSVL失败: {e}")
            raise

    def record_start(self, record_save_path: str):
        """
        开始录屏。

        参数:
            record_save_path (str): 录屏保存路径
        """
        try:
            self.record_process = subprocess.Popen(
                ["bash", self.record_start_script, record_save_path],
                cwd=self.lgsvl_root,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
            )
            self.logger.info(f"录屏已开始，保存路径: {record_save_path}")
        except Exception as e:
            self.logger.error(f"开始录屏失败: {e}")
            raise

    def record_stop(self):
        """
        停止录屏。
        """
        try:
            # 如果有录屏进程在运行，先终止它
            if self.record_process and self.record_process.poll() is None:
                self.record_process.terminate()
                self.record_process.wait(timeout=5)
                self.logger.info("录屏进程已终止。")

            # 执行录屏停止脚本
            subprocess.run(
                ["bash", self.record_stop_script],
                cwd=self.lgsvl_root,
                check=True,
                timeout=10,
            )
            self.logger.info("录屏已停止。")
        except subprocess.TimeoutExpired:
            self.logger.warning("停止录屏脚本执行超时")
        except Exception as e:
            self.logger.error(f"停止录屏失败: {e}")
        finally:
            self.record_process = None

    def stop(self):
        """
        关闭LGSVL仿真环境。
        """
        if self.process and self.process.poll() is None:
            try:
                self.process.terminate()
                self.process.wait(timeout=10)
                self.logger.info("LGSVL主进程已终止。")
            except Exception as e:
                self.logger.error(f"终止LGSVL主进程失败: {e}")
        # 无论主进程是否存在，都执行kill脚本
        try:
            subprocess.run(["bash", self.kill_script], cwd=self.lgsvl_root, check=True)
            self.logger.info("已执行kill.sh脚本，LGSVL仿真环境关闭。")
        except Exception as e:
            self.logger.error(f"执行kill.sh失败: {e}")
        finally:
            # 拷贝日志到get_task_record_path中
            if self.task_id:
                self.logger.info(
                    f"开始拷贝LGSVL日志到任务记录路径，任务ID: {self.task_id}"
                )
                try:
                    record_path = get_task_record_path(self.task_id)
                    self.logger.info(f"获取到任务记录路径: {record_path}")

                    # 创建lgsvl_logs目录
                    lgsvl_logs_path = os.path.join(record_path, "lgsvl_logs")
                    if not os.path.exists(lgsvl_logs_path):
                        os.makedirs(lgsvl_logs_path)
                        self.logger.info(f"创建日志目录: {lgsvl_logs_path}")
                    else:
                        self.logger.info(f"日志目录已存在: {lgsvl_logs_path}")

                    # 拷贝日志
                    copied_count = 0
                    for log_dir in self.log_dirs:
                        if os.path.exists(log_dir):
                            dest_dir = os.path.join(
                                lgsvl_logs_path, os.path.basename(log_dir)
                            )
                            try:
                                if os.path.exists(dest_dir):
                                    shutil.rmtree(dest_dir)
                                    self.logger.info(
                                        f"删除已存在的目标目录: {dest_dir}"
                                    )

                                shutil.copytree(log_dir, dest_dir)
                                copied_count += 1
                                self.logger.info(
                                    f"成功拷贝日志目录: {log_dir} -> {dest_dir}"
                                )
                            except Exception as e:
                                self.logger.error(
                                    f"拷贝日志目录失败: {log_dir} -> {dest_dir}, 错误: {e}"
                                )
                        else:
                            self.logger.warning(
                                f"源日志目录不存在，跳过拷贝: {log_dir}"
                            )

                    self.logger.info(f"日志拷贝完成，共成功拷贝 {copied_count} 个目录")

                except Exception as e:
                    self.logger.error(f"拷贝日志过程中出现异常: {e}")
            else:
                self.logger.info("未指定任务ID，跳过日志拷贝")
