#!/usr/bin/python3
# -*- coding: utf-8 -*-
"""
定位数据发布器模块

功能:
1. 订阅LGSVL相关话题，转换为算法需要的格式
2. 发布定位、底盘、车辆状态等消息
3. 支持多次初始化和清理，避免全局变量状态问题

遵循SOLID原则:
- 单一职责原则: 专门处理定位数据转换和发布
- 开闭原则: 通过继承可扩展功能
- 依赖倒置原则: 依赖抽象接口而非具体实现
"""

import rospy
from math import pi
from lgsvl_msgs.msg import CanBusData
from nav_msgs.msg import Odometry
from sensor_msgs.msg import Imu
from rosgraph_msgs.msg import Clock
from autopilot_msgs.msg import BinaryData
from common.localization_pb2 import Localization
from common.chassis2025_pb2 import ChassisStates
from std_msgs.msg import Int32
from common.fsm2024_pb2 import FSMStateMsg

from utils.loggers import LoggerProxy

from common.vehicle_state_pb2 import VehicleState

logger = LoggerProxy("pub_localization")


class LocalizationPublisher:
    """
    定位数据发布器类
    
    职责:
    - 接收和解析LGSVL数据
    - 转换为标准格式
    - 发布定位、底盘、车辆状态消息
    - 管理资源生命周期
    """
    
    def __init__(self):
        """初始化定位数据发布器"""
        # 状态管理
        self.stopped = False
        
        # 定位数据状态
        self.longitude = 0.0
        self.latitude = 0.0
        self.longitude_speed = 0.0
        self.utm_zone_code = None
        self.seq = 1
        self.ego_acceleration_x = 0.0
        self.ego_angular_x = 0.0
        self.ego_angular_y = 0.0
        self.ego_angular_z = 0.0
        self.ego_odom_yaw = 0.0
        self.ego_odom_roll = 0.0
        self.ego_odom_pitch = 0.0
        self.maximum_steering = 500.0  # 需根据实际车型调整
        self.current_time = rospy.Time(0)
        
        # 发布器
        self.chassis25_pub = None
        self.ego_loc_pub = None
        self.ego_state_pub = None
        self.autopilot_pub = None
        self.FSM_state_pub = None
        
        # 订阅者和定时器
        self.clock_sub = None
        self.ego_state_sub = None
        self.gps_odom_sub = None
        self.imu_sub = None
        self.autopilot_timer = None


    def on_clock(self, msg):
        """从/clock话题更新当前时间"""
        if self.stopped:
            return
        try:
            self.current_time = msg.clock
        except Exception as e:
            logger.error(f"on_clock函数中出现错误: {e}", exc_info=True)


    def on_ego_state(self, msg):
        """处理/EgoState，发布/chassis/chassis2025和/chassis/vehicle_state"""
        if self.stopped:
            return
        try:
            self.longitude = msg.gps_longitude
            self.latitude = msg.gps_latitude
            self.longitude_speed = msg.speed_mps
            if self.utm_zone_code is None:
                self.utm_zone_code = int(self.longitude / 6) + 31

            FSM_state_msg = FSMStateMsg()
            self.seq = self.seq + 1
            FSM_state_msg.header.stamp.sec = self.current_time.secs
            FSM_state_msg.header.stamp.nsec = self.current_time.nsecs
            FSM_state_msg.header.seq = self.seq
            FSM_state_msg.header.frame_id = "fsm.FSMStateMsg"
            FSM_state_msg.header.module_name = "fsm.FSMStateMsg"
            FSM_state_msg.function_state = 6
            FSM_state_msg.fsm_safety_stop_mode = 0
            FSM_state_msg.active_mode = 1
            FSM_state_msg.beautiful_mode = False
            FSM_state_msg.version = "2.0"
            FSM_state_msg.new_msg_flag = True
            FSM_state_msg.pilot_standby_flag = True
            FSM_state_msg.parallel_standby_flag = True
            FSM_state_msg.m1steer_standby_flag = True
            FSM_state_msg.order_status = 2
            # protobuf
            FSM_state_data = FSM_state_msg.SerializeToString()
            FSM_state = BinaryData()

            FSM_state.header.stamp = self.current_time
            FSM_state.header.seq = self.seq
            FSM_state.header.frame_id = "fsm.FSMStateMsg"
            FSM_state.name = "fsm.FSMStateMsg"

            FSM_state.size = len(FSM_state_data)
            FSM_state.data = FSM_state_data
            # publish
            self.FSM_state_pub.publish(FSM_state)
            logger.info(f"seq:{self.seq},FSM_state_msg:{FSM_state_msg}")

            # 构造并发布Chassis2025消息
            chassis25_msg = ChassisStates()

            # 正确设置header时间戳 (sec和nsec分开)
            chassis25_msg.header.stamp.sec = self.current_time.secs
            chassis25_msg.header.stamp.nsec = self.current_time.nsecs
            chassis25_msg.header.frame_id = "chassis_base"
            chassis25_msg.header.module_name = "chassis25"

            chassis25_msg.chassis_wire_states.chassis_wire_enable = True
            chassis25_msg.drive_system_states.drive_enable = True
            chassis25_msg.brake_system_states.brake_enable = True
            chassis25_msg.steer_system_states.steer_enable = True
            chassis25_msg.gear_system_states.gear_enable = True
            chassis25_msg.epb_system_states.epb_enable = True

            chassis25_msg.steer_system_states.steer_wheel_angle = (
                msg.steer_pct * self.maximum_steering
            )
            chassis25_msg.vehicle_motion_states.speed = msg.speed_mps
            chassis25_msg.gear_system_states.gear = msg.selected_gear
            if msg.selected_gear == 1:  # gear N
                chassis25_msg.gear_system_states.gear = 4  # gear D

            chassis25_msg.vehicle_motion_states.acceleration = self.ego_acceleration_x
            chassis25_msg.drive_system_states.throttle = msg.throttle_pct
            chassis25_msg.brake_system_states.brake_pedal_position = msg.brake_pct

            chassis25_data = chassis25_msg.SerializeToString()
            # 与sensor_bridge.py保持完全一致的BinaryData构造方式
            chassis25_state = BinaryData()
            chassis25_state.header.stamp = self.current_time
            chassis25_state.header.frame_id = "chassis_base"
            chassis25_state.name = "chassis25.ChassisStates"
            chassis25_state.size = len(chassis25_data)
            chassis25_state.data = chassis25_data
            self.chassis25_pub.publish(chassis25_state)

            vehicle_state_msg = VehicleState()
            vehicle_state_msg.header.stamp.sec = self.current_time.secs
            vehicle_state_msg.header.stamp.nsec = self.current_time.nsecs
            vehicle_state_msg.header.frame_id = "chassis_base"
            vehicle_state_msg.header.module_name = "chassis_VehicleState"
            # 2.3x版本或新版本taxi
            vehicle_state_msg.steering = msg.steer_pct * self.maximum_steering

            vehicle_state_msg.pilot_mode = 1
            vehicle_state_msg.speed = msg.speed_mps
            vehicle_state_msg.gear = msg.selected_gear

            vehicle_state_msg.accel = self.ego_acceleration_x
            vehicle_state_msg.throttle = msg.throttle_pct
            vehicle_state_msg.brake = msg.brake_pct
            # protobuf
            vehicle_state_data = vehicle_state_msg.SerializeToString()
            vehicle_state = BinaryData()
            vehicle_state.header.stamp = self.current_time
            vehicle_state.header.frame_id = "chassis_base"
            vehicle_state.name = "chassis.VehicleState"
            vehicle_state.size = len(vehicle_state_data)
            vehicle_state.data = vehicle_state_data
            # publish
            self.ego_state_pub.publish(vehicle_state)
        except Exception as e:
            logger.error(f"on_ego_state函数中出现错误: {e}", exc_info=True)


    def on_imu(self, msg):
        """更新IMU数据"""
        if self.stopped:
            return
        try:
            self.ego_acceleration_x = msg.linear_acceleration.x
            self.ego_angular_x = msg.angular_velocity.x
            self.ego_angular_y = msg.angular_velocity.y
            self.ego_angular_z = msg.angular_velocity.z
        except Exception as e:
            logger.error(f"on_imu函数中出现错误: {e}", exc_info=True)


    def on_gps_odom(self, msg):
        """处理/odom，发布/localization/global（依赖UTM区域和IMU数据）"""
        if self.stopped:
            return
        try:
            if self.utm_zone_code is None:
                return  # 必须等待/EgoState初始化UTM区域

            self.ego_odom_roll = msg.pose.pose.orientation.x
            self.ego_odom_pitch = msg.pose.pose.orientation.y
            self.ego_odom_yaw = msg.pose.pose.orientation.z

            # 构造并发布Localization消息
            loc_msg = Localization()

            # 正确设置header时间戳 (sec和nsec分开)
            loc_msg.header.stamp.sec = self.current_time.secs
            loc_msg.header.stamp.nsec = self.current_time.nsecs
            # 添加缺少的header字段
            loc_msg.header.frame_id = "utm"
            loc_msg.header.module_name = "localization.Localization"

            loc_msg.position.x = msg.pose.pose.position.x  # UTM X
            loc_msg.position.y = msg.pose.pose.position.y  # UTM Y
            loc_msg.position.z = msg.pose.pose.position.z  # UTM Z - 添加高度信息
            loc_msg.longitude = self.longitude
            loc_msg.latitude = self.latitude
            loc_msg.yaw = self.ego_odom_yaw % (2 * pi)
            loc_msg.roll = self.ego_odom_roll % (2 * pi)
            loc_msg.pitch = self.ego_odom_pitch % (2 * pi)
            loc_msg.longitudinal_v = self.longitude_speed
            loc_msg.longitudinal_a = self.ego_acceleration_x
            loc_msg.yaw_v = self.ego_angular_z

            # 添加缺少的角速度字段
            loc_msg.pitch_v = self.ego_angular_y
            loc_msg.roll_v = self.ego_angular_x

            # 添加缺少的速度和加速度字段（保持与sensor_bridge.py一致）
            loc_msg.horizontal_v = self.longitude_speed  # 水平速度设为纵向速度
            loc_msg.lateral_v = 0  # 横向速度默认为0
            loc_msg.vertical_a = self.ego_acceleration_x  # 垂直加速度使用X轴加速度

            # 添加缺少的状态字段
            loc_msg.gnss_status = 42  # GNSS状态标识
            loc_msg.loc_status = 0  # 定位状态标识

            loc_msg.utm_zone = self.utm_zone_code

            loc_data = loc_msg.SerializeToString()

            binary_msg = BinaryData()
            binary_msg.header.stamp = self.current_time
            binary_msg.header.frame_id = "utm"  # 保持与消息内容一致
            binary_msg.name = "localization.Localization"
            binary_msg.size = len(loc_data)  # 添加缺少的size字段
            binary_msg.data = loc_data

            self.ego_loc_pub.publish(binary_msg)
            logger.info(f"Localization: {loc_msg}")
        except Exception as e:
            logger.error(f"on_gps_odom函数中出现错误: {e}", exc_info=True)


    def publish_autopilot_cmd(self, event=None):
        """固定频率(10Hz)发布/autopilot/AutoPilotCmd，值固定为1"""
        if self.stopped:
            return
        try:
            auto_msg = Int32()
            auto_msg.data = 1
            self.autopilot_pub.publish(auto_msg)
        except Exception as e:
            logger.error(f"publish_autopilot_cmd函数中出现错误: {e}", exc_info=True)


    def start(self):
        """启动定位数据发布器"""
        try:
            # 创建发布器
            self.chassis25_pub = rospy.Publisher("/chassis/chassis2025", BinaryData, queue_size=1)
            self.ego_loc_pub = rospy.Publisher("/localization/global", BinaryData, queue_size=1)
            self.ego_state_pub = rospy.Publisher("/chassis/vehicle_state", BinaryData, queue_size=1)
            self.autopilot_pub = rospy.Publisher("/autopilot/AutoPilotCmd", Int32, queue_size=1)
            self.FSM_state_pub = rospy.Publisher("/fsm/fsm_state", BinaryData, queue_size=1)
            
            # 创建订阅者
            self.clock_sub = rospy.Subscriber("/clock", Clock, self.on_clock)
            self.ego_state_sub = rospy.Subscriber("/EgoState", CanBusData, self.on_ego_state)
            self.gps_odom_sub = rospy.Subscriber("/odom", Odometry, self.on_gps_odom, queue_size=1, tcp_nodelay=True)
            self.imu_sub = rospy.Subscriber("/imu", Imu, self.on_imu)

            # 创建一个10Hz的定时器来发布/autopilot/AutoPilotCmd
            self.autopilot_timer = rospy.Timer(rospy.Duration(0.1), self.publish_autopilot_cmd)

            logger.info("LocalizationPublisher initialized")
            return True
        except Exception as e:
            logger.error(f"LocalizationPublisher启动时发生错误: {e}", exc_info=True)
            return False

    def stop(self):
        """停止定位数据发布器"""
        try:
            self.stopped = True
            
            # 清理订阅者
            if self.clock_sub:
                self.clock_sub.unregister()
                self.clock_sub = None
                
            if self.ego_state_sub:
                self.ego_state_sub.unregister()
                self.ego_state_sub = None
                
            if self.gps_odom_sub:
                self.gps_odom_sub.unregister()
                self.gps_odom_sub = None
                
            if self.imu_sub:
                self.imu_sub.unregister()
                self.imu_sub = None
                
            # 清理定时器
            if self.autopilot_timer:
                self.autopilot_timer.shutdown()
                self.autopilot_timer = None
                
            # 清理发布者
            if self.chassis25_pub:
                self.chassis25_pub.unregister()
                self.chassis25_pub = None
                
            if self.ego_loc_pub:
                self.ego_loc_pub.unregister()
                self.ego_loc_pub = None
                
            if self.ego_state_pub:
                self.ego_state_pub.unregister()
                self.ego_state_pub = None
                
            if self.autopilot_pub:
                self.autopilot_pub.unregister()
                self.autopilot_pub = None
                
            if self.FSM_state_pub:
                self.FSM_state_pub.unregister()
                self.FSM_state_pub = None
                
            logger.info("LocalizationPublisher资源已清理完成")
            
        except Exception as e:
            logger.error(f"清理LocalizationPublisher资源时出错: {e}", exc_info=True)

    def set_stop(self, stop=True):
        """
        控制处理器的启停状态
        
        参数:
            stop (bool): True=停止, False=启动
        """
        self.stopped = stop
        status = "已停止" if stop else "已启动"
        logger.info(f"定位发布器{status}")


def create_localization_publisher():
    """
    工厂函数：创建定位数据发布器实例

    返回:
        LocalizationPublisher: 定位数据发布器实例
    """
    return LocalizationPublisher()


if __name__ == "__main__":
    try:
        rospy.init_node("sensor_bridge")
        publisher = create_localization_publisher()
        publisher.start()
        rospy.spin()
        publisher.stop()
    except rospy.ROSInterruptException:
        logger.info("LocalizationPublisher has been shutdown.")
    except Exception as e:
        logger.error(
            "Unhandled exception in LocalizationPublisher main: %s", e, exc_info=True
        )
