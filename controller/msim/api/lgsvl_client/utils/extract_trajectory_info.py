#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从bag文件中提取/planning/trajectory的前20条轨迹信息并打印

本脚本遵循SOLID原则设计：
- 单一职责：专门负责轨迹信息提取和打印
- 开闭原则：通过抽象基类支持扩展
- 依赖倒置：依赖抽象接口而非具体实现
"""
import argparse
import os
import sys
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
from dataclasses import dataclass

# 导入依赖包
try:
    import rosbag
    from google.protobuf.message import DecodeError
except ImportError as e:
    print(f"错误: 缺少必要的依赖包: {e}")
    print("请确保已安装 rosbag 和 protobuf 相关包")
    sys.exit(1)

# 导入protobuf消息类型
try:
    from common.trajectory_pb2 import Trajectory
except ImportError:
    try:
        sys.path.append("/home/<USER>/workspace/mount/dist-packages")
        sys.path.append("/home/<USER>/workspace/mount/dist-packages/common")
        from common.trajectory_pb2 import Trajectory
    except ImportError as e:
        print(f"错误: 无法导入轨迹protobuf定义: {e}")
        print("请确保protobuf文件路径正确")
        sys.exit(1)


@dataclass
class TrajectoryPoint:
    """轨迹点数据结构"""

    x: float
    y: float
    z: float = 0.0
    theta: float = 0.0  # yaw角度
    kappa: float = 0.0  # 曲率
    s: float = 0.0  # 累积弧长
    velocity: float = 0.0
    acceleration: float = 0.0
    timestamp: float = 0.0


class TrajectoryExtractor(ABC):
    """轨迹提取器抽象基类 - 遵循开闭原则"""

    @abstractmethod
    def extract_trajectories(
        self, bag_path: str, topic: str, max_count: int
    ) -> List[TrajectoryPoint]:
        """提取轨迹数据的抽象方法"""
        pass


class BagTrajectoryExtractor(TrajectoryExtractor):
    """基于ROS bag的轨迹提取器实现"""

    def __init__(self):
        self.trajectory_msg_count = 0  # 轨迹消息计数器
        self.total_points_count = 0  # 总轨迹点计数器

    def extract_trajectories(
        self, bag_path: str, topic: str, max_count: int
    ) -> List[TrajectoryPoint]:
        """
        从bag文件中提取轨迹数据

        参数:
            bag_path (str): bag文件路径
            topic (str): 轨迹topic名称
            max_count (int): 最大提取轨迹消息数量

        返回:
            List[TrajectoryPoint]: 轨迹点列表
        """
        trajectory_points = []

        try:
            with rosbag.Bag(bag_path, "r") as bag:
                print(f"开始从 {bag_path} 提取轨迹数据...")
                print(f"目标topic: {topic}")
                print(f"最大提取轨迹消息数量: {max_count}")
                print("-" * 50)

                for topic_name, msg, timestamp in bag.read_messages(topics=[topic]):
                    # 按轨迹消息数量限制，而不是轨迹点数量
                    if self.trajectory_msg_count >= max_count:
                        break

                    try:
                        trajectory_data = Trajectory()
                        trajectory_data.ParseFromString(msg.data)

                        current_msg_points = 0  # 当前轨迹消息中的点数

                        # 提取轨迹消息中的所有轨迹点
                        for point in trajectory_data.points:
                            traj_point = TrajectoryPoint(
                                x=point.x,
                                y=point.y,
                                z=getattr(point, "z", 0.0),
                                theta=getattr(point, "theta", 0.0),
                                kappa=getattr(point, "kappa", 0.0),
                                s=getattr(point, "s", 0.0),
                                velocity=getattr(point, "v", 0.0),
                                acceleration=getattr(point, "a", 0.0),
                                timestamp=timestamp.to_sec(),
                            )

                            trajectory_points.append(traj_point)
                            current_msg_points += 1
                            self.total_points_count += 1

                        # 轨迹消息处理完成，计数器+1
                        self.trajectory_msg_count += 1

                        print(
                            f"已处理第 {self.trajectory_msg_count} 个轨迹消息，包含 {current_msg_points} 个轨迹点"
                        )

                    except DecodeError as e:
                        print(f"警告: 解码轨迹消息失败: {e}")
                        continue
                    except Exception as e:
                        print(f"处理轨迹消息时发生错误: {e}")
                        continue

        except rosbag.bag.BagException as e:
            print(f"错误: 无法读取bag文件: {e}")
            return []
        except Exception as e:
            print(f"提取轨迹数据时发生未知错误: {e}")
            return []

        print(
            f"轨迹提取完成: 共处理 {self.trajectory_msg_count} 个轨迹消息，总计 {self.total_points_count} 个轨迹点"
        )
        return trajectory_points


class TrajectoryPrinter:
    """轨迹信息打印器 - 遵循单一职责原则"""

    @staticmethod
    def print_trajectory_summary(trajectories: List[TrajectoryPoint]) -> None:
        """打印轨迹摘要信息"""
        if not trajectories:
            print("未找到任何轨迹数据")
            return

        print(f"\n=== 轨迹摘要信息 ===")
        print(f"从多个轨迹消息中提取的总轨迹点数: {len(trajectories)}")
        print(f"第一个轨迹点坐标: ({trajectories[0].x:.3f}, {trajectories[0].y:.3f})")
        print(
            f"最后一个轨迹点坐标: ({trajectories[-1].x:.3f}, {trajectories[-1].y:.3f})"
        )
        if trajectories[-1].s > 0:
            print(f"最后轨迹点的弧长: {trajectories[-1].s:.3f} 米")
        else:
            print("弧长信息不可用")

    @staticmethod
    def print_detailed_trajectories(trajectories: List[TrajectoryPoint]) -> None:
        """打印详细轨迹信息"""
        if not trajectories:
            return

        print(f"\n=== 详细轨迹信息 ===")
        print(
            f"{'序号':<4} {'X坐标':<12} {'Y坐标':<12} {'Z坐标':<8} {'航向角':<10} {'曲率':<10} {'弧长':<10} {'速度':<8} {'加速度':<8}"
        )
        print("-" * 90)

        for i, point in enumerate(trajectories, 1):
            print(
                f"{i:<4} {point.x:<12.3f} {point.y:<12.3f} {point.z:<8.3f} "
                f"{point.theta:<10.3f} {point.kappa:<10.6f} {point.s:<10.3f} "
                f"{point.velocity:<8.3f} {point.acceleration:<8.3f}"
            )


class TrajectoryAnalysisService:
    """轨迹分析服务类 - 遵循依赖倒置原则"""

    def __init__(self, extractor: TrajectoryExtractor):
        self.extractor = extractor
        self.printer = TrajectoryPrinter()

    def analyze_bag_trajectories(
        self, bag_path: str, topic: str = "/planning/trajectory", max_count: int = 20
    ) -> None:
        """
        分析bag文件中的轨迹数据

        参数:
            bag_path (str): bag文件路径
            topic (str): 轨迹topic名称
            max_count (int): 最大分析数量
        """
        # 验证输入参数
        if not os.path.isfile(bag_path):
            print(f"错误: bag文件 '{bag_path}' 不存在")
            return

        # 提取轨迹数据
        trajectories = self.extractor.extract_trajectories(bag_path, topic, max_count)

        if not trajectories:
            print("未能从bag文件中提取到任何轨迹数据")
            return

        # 打印分析结果
        self.printer.print_trajectory_summary(trajectories)
        self.printer.print_detailed_trajectories(trajectories)

        print(
            f"\n轨迹数据提取完成，从前{max_count}个轨迹消息中共提取 {len(trajectories)} 个轨迹点"
        )


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="从bag文件中提取/planning/trajectory的前N个轨迹消息中的所有轨迹点并打印",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python extract_trajectory_info.py /path/to/your.bag
  python extract_trajectory_info.py /path/to/your.bag --topic /planning/trajectory --count 30
  
注意: count参数指定的是轨迹消息数量，每个轨迹消息包含多个轨迹点
        """,
    )

    parser.add_argument("bag_file", help="要分析的bag文件路径")
    parser.add_argument(
        "--topic",
        default="/planning/trajectory",
        help="轨迹topic名称 (默认: /planning/trajectory)",
    )
    parser.add_argument(
        "--count", type=int, default=20, help="要提取的轨迹消息数量 (默认: 20)"
    )

    args = parser.parse_args()

    # 创建轨迹分析服务
    extractor = BagTrajectoryExtractor()
    analysis_service = TrajectoryAnalysisService(extractor)

    # 执行轨迹分析
    analysis_service.analyze_bag_trajectories(args.bag_file, args.topic, args.count)


if __name__ == "__main__":
    main()

