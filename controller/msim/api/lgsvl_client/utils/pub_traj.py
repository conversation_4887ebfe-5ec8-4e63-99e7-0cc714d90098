#!/usr/bin/python3
# -*- coding: utf-8 -*-
import rospy
from rosgraph_msgs.msg import Clock

from utils.loggers import LoggerProxy

logger = LoggerProxy("pub_traj")

# LGSVL 消息类型
from lgsvl_msgs.msg import RefTrajectory, RefPoint, TrajectoryPoint, ObstacleContour

from common.trajectory_pb2 import Trajectory
from common.object_pb2 import TrackedObjects

from autopilot_msgs.msg import BinaryData

# 添加导入用于获取主车位置信息的ROS消息类型
from nav_msgs.msg import Odometry
from geometry_msgs.msg import Point, Quaternion
import math
from math import pi
import threading


def quaternion_to_euler(x: float, y: float, z: float, w: float) -> tuple:
    """
    四元数转欧拉角

    参数:
        x, y, z, w (float): 四元数分量

    返回:
        tuple[float, float, float]: 欧拉角 (roll, pitch, yaw) 单位：弧度
    """
    # Roll (x-axis rotation)
    sinr_cosp = 2 * (w * x + y * z)
    cosr_cosp = 1 - 2 * (x * x + y * y)
    roll = math.atan2(sinr_cosp, cosr_cosp)

    # Pitch (y-axis rotation)
    sinp = 2 * (w * y - z * x)
    if abs(sinp) >= 1:
        pitch = math.copysign(pi / 2, sinp)  # 使用90度
    else:
        pitch = math.asin(sinp)

    # Yaw (z-axis rotation)
    siny_cosp = 2 * (w * z + x * y)
    cosy_cosp = 1 - 2 * (y * y + z * z)
    yaw = math.atan2(siny_cosp, cosy_cosp)

    return roll, pitch, yaw


def real_pos2unity(x, y, z):
    return (-y, z, x)


def get_vehicle_type():
    return "bus"


class TrajectoryProcessor:
    def __init__(self, shield_time_ns=None):
        self.stopped = False
        self.planning_v = 0
        self.planning_a = 0
        self.shield_time_ns = shield_time_ns  # 存储屏蔽时间
        self.current_time_ns = 0  # 当前时间（纳秒）

        # 主车位置相关变量
        self.ego_utm_x = 0.0
        self.ego_utm_y = 0.0
        self.ego_odom_yaw = 0.0
        self.ego_longitude_speed = 0.0

        # 标志位：避免重复打印ego position未初始化警告
        self.ego_position_initialized = False
        self.ego_warning_printed = False

        # 创建轨迹发布器
        self.lgsvl_trajectory = rospy.Publisher(
            "/lgsvl_trajector", RefTrajectory, queue_size=1
        )

        # 创建bag轨迹发布器
        self.lgsvl_trajectory_bag = rospy.Publisher(
            "/lgsvl_trajector_bag", RefTrajectory, queue_size=1
        )

        # 创建障碍物轮廓发布器（用于发布主车位置）
        self.lgsvl_obstacle_pub = rospy.Publisher(
            "/lgsvl/obstacle_contour", ObstacleContour, queue_size=1
        )

        # 订阅规划轨迹主题（保存为实例变量以便清理）
        self.trajectory_bag_sub = rospy.Subscriber(
            "/planning/trajectory_bag", BinaryData, self.on_trajectory_bag
        )

        # 新增订阅：用于屏蔽时间后的轨迹（保存为实例变量以便清理）
        self.trajectory_sub = rospy.Subscriber(
            "/planning/trajectory", BinaryData, self.on_trajectory
        )

        # 订阅时钟主题（保存为实例变量以便清理）
        self.clock_sub = rospy.Subscriber("/clock", Clock, self.clock_callback)

        # 订阅里程计话题获取主车位置信息（保存为实例变量以便清理）
        self.odom_sub = rospy.Subscriber(
            "/odom", Odometry, self.on_odom_received, queue_size=1
        )

        # 订阅障碍物话题（在shield_time_ns后生效）（保存为实例变量以便清理）
        self.obstacles_sub = rospy.Subscriber(
            "/perception/fusion/obstacles",
            BinaryData,
            self.publish_ego_obstacle,
            queue_size=1,
        )

        # 创建定时器定期发布主车位置信息
        self.ego_position_timer = rospy.Timer(
            rospy.Duration(0.1), self.publish_ego_position
        )

        logger.info("Trajectory processor initialized with obstacle contour publisher")

    def on_odom_received(self, msg):
        """
        处理/odom话题消息的回调函数

        参数:
            msg (Odometry): 里程计消息
        """
        try:
            # 提取位置信息
            position = msg.pose.pose.position
            self.ego_utm_x = position.x
            self.ego_utm_y = position.y

            # 提取姿态信息并转换为欧拉角
            orientation = msg.pose.pose.orientation
            ego_roll, ego_pitch, self.ego_odom_yaw = quaternion_to_euler(
                orientation.x, orientation.y, orientation.z, orientation.w
            )

            # 提取速度信息（纵向速度）
            if hasattr(msg.twist, "twist") and hasattr(msg.twist.twist, "linear"):
                self.ego_longitude_speed = msg.twist.twist.linear.x
            else:
                self.ego_longitude_speed = 0.0

            # 标记ego position已初始化
            if not self.ego_position_initialized:
                self.ego_position_initialized = True
                logger.info(
                    f"Ego position initialized: x={self.ego_utm_x:.3f}, y={self.ego_utm_y:.3f}"
                )

            logger.debug(
                f"Updated ego position: x={self.ego_utm_x:.3f}, y={self.ego_utm_y:.3f}, "
                f"yaw={self.ego_odom_yaw:.3f}, speed={self.ego_longitude_speed:.3f}"
            )

        except Exception as e:
            logger.error(f"Failed to process odom message: {e}")

    def update_ego_position(self):
        """更新主车位置信息（现在通过odom话题回调更新，此方法用于数据有效性检查）"""
        # 检查数据是否已经初始化
        if not self.ego_position_initialized:
            if not self.ego_warning_printed:
                logger.warning("Ego position not yet initialized from odometry")
                self.ego_warning_printed = True

        # 可以在这里添加其他数据处理逻辑

    def publish_ego_position(self, event):
        """定期发布主车位置信息到/lgsvl/obstacle_contour话题"""
        if self.stopped:
            return

        # 更新主车位置信息
        self.update_ego_position()

        # 创建障碍物轮廓消息
        obstacle_contour = ObstacleContour()
        now = rospy.Time.now()
        obstacle_contour.header.stamp = now
        obstacle_contour.header.frame_id = "base_link"

        # 添加主车位置信息
        ego_utm_pt = RefPoint()
        ego_utm_pt.x = self.ego_utm_x
        ego_utm_pt.y = self.ego_utm_y
        ego_utm_pt.z = 3.0  # 固定高度
        obstacle_contour.utms.append(ego_utm_pt)

        # 发布消息
        self.lgsvl_obstacle_pub.publish(obstacle_contour)

        logger.info(
            f"Published ego position: x={self.ego_utm_x:.2f}, y={self.ego_utm_y:.2f}, yaw={self.ego_odom_yaw:.2f}"
        )

    def publish_ego_obstacle(self, msg):
        """
        处理障碍物数据的回调函数，仅在屏蔽时间之后生效

        参数:
            msg (BinaryData): 包含 TrackedObjects 数据的二进制消息
        """
        logger.info(f"障碍物回调函数被调用，消息大小: {len(msg.data)} bytes")

        if self.stopped:
            logger.info("处理器已停止，跳过障碍物处理")
            return

        # 检查屏蔽时间：如果当前时间小于屏蔽时间，则不处理此主题的轨迹
        if (
            self.shield_time_ns is not None
            and self.current_time_ns < self.shield_time_ns
        ):
            logger.debug(
                f"尚未到达屏蔽时间: {self.shield_time_ns}ns, 当前时间: {self.current_time_ns}ns, 忽略障碍物数据"
            )
            return

        # 解析二进制消息
        try:
            tracked_objects = TrackedObjects()
            tracked_objects.ParseFromString(msg.data)
            logger.info(
                f"障碍物消息解析成功，包含 {len(tracked_objects.objs)} 个障碍物"
            )
        except Exception as e:
            logger.error(f"障碍物消息解析失败: {e}")
            return

            # 创建障碍物轮廓消息
        obstacle_contour = ObstacleContour()
        now = rospy.Time.now()
        obstacle_contour.header.stamp = now
        obstacle_contour.header.frame_id = "base_link"

        # 处理障碍物数据，提取障碍物中心位置到contours
        for index, obj_tmp in enumerate(tracked_objects.objs):
            try:
                # 获取障碍物对象
                obj = obj_tmp.obj if hasattr(obj_tmp, "obj") else obj_tmp

                # 检查是否有center字段
                if hasattr(obj, "center"):
                    # 创建RefPoint并添加到contours
                    obstacle_pt = RefPoint()
                    obstacle_pt.x = getattr(obj.center, "x", 0.0)
                    obstacle_pt.y = getattr(obj.center, "y", 0.0)
                    obstacle_pt.z = 3
                    obstacle_contour.contours.append(obstacle_pt)

                    logger.debug(
                        f"障碍物[{index}]: center.x={obstacle_pt.x:.3f}, center.y={obstacle_pt.y:.3f}, center.z={obstacle_pt.z:.3f}"
                    )
                else:
                    logger.warning(f"障碍物[{index}] 缺少center字段")

            except Exception as e:
                logger.error(f"处理障碍物[{index}]时出错: {e}")
                continue

        # 添加主车位置信息到utms
        ego_utm_pt = RefPoint()
        ego_utm_pt.x = self.ego_utm_x
        ego_utm_pt.y = self.ego_utm_y
        ego_utm_pt.z = 3.0  # 固定高度
        obstacle_contour.utms.append(ego_utm_pt)

        # 发布消息
        self.lgsvl_obstacle_pub.publish(obstacle_contour)

        logger.info(
            f"✓ 障碍物轮廓发布完成: obstacles={len(obstacle_contour.contours)}, "
            f"ego_pos=({self.ego_utm_x:.2f}, {self.ego_utm_y:.2f})"
        )

    def control_lon_data(self, msg):
        """更新公交车的规划速度和加速度"""

        self.planning_a = msg.ref_point_accel
        self.planning_v = msg.ref_point_speed
        logger.debug(
            "Updated bus planning state: v={}, a={}", self.planning_v, self.planning_a
        )

    def clock_callback(self, msg):
        """处理时钟回调，更新当前时间"""
        # 将时间转换为纳秒 (秒部分 * 1e9 + 纳秒部分)
        self.current_time_ns = msg.clock.secs * int(1e9) + msg.clock.nsecs

    def on_trajectory_bag(self, msg):
        """处理 /planning/trajectory_bag 的回调，始终发布到 /lgsvl_trajector_bag"""
        logger.info(
            f"轨迹回调函数被调用 (from /planning/trajectory_bag), 消息大小: {len(msg.data)} bytes"
        )

        if self.stopped:
            logger.info("处理器已停止，跳过轨迹处理")
            return

        # 直接发布到 /lgsvl_trajector_bag
        logger.info(f"将 /planning/trajectory_bag 发布到 /lgsvl_trajector_bag")
        self._process_and_publish_trajectory(
            msg, "/planning/trajectory_bag", self.lgsvl_trajectory_bag
        )

    def on_trajectory(self, msg):
        """处理 /planning/trajectory 的回调，始终发布到 /lgsvl_trajector"""
        logger.info(
            f"轨迹回调函数被调用 (from /planning/trajectory), 消息大小: {len(msg.data)} bytes"
        )

        if self.stopped:
            logger.info("处理器已停止，跳过轨迹处理")
            return

        # 直接发布到 /lgsvl_trajector
        logger.info(f"将 /planning/trajectory 发布到 /lgsvl_trajector")
        self._process_and_publish_trajectory(msg, "/planning/trajectory")

    def _process_and_publish_trajectory(self, msg, source_topic: str, publisher=None):
        """
        通用的轨迹处理与发布函数

        参数:
            msg (BinaryData): 接收到的轨迹消息
            source_topic (str): 消息来源的话题名称，用于日志记录
            publisher: 指定的发布器，如果为None则使用默认的lgsvl_trajectory发布器
        """
        # 确定目标发布话题名称
        if publisher is None:
            publisher = self.lgsvl_trajectory
            target_topic = "/lgsvl_trajector"
        elif publisher == self.lgsvl_trajectory_bag:
            target_topic = "/lgsvl_trajector_bag"
        else:
            target_topic = "/lgsvl_trajector"
        logger.info(f"开始处理来自 {source_topic} 的轨迹")

        # 解析二进制消息
        try:
            trajectory = Trajectory()
            trajectory.ParseFromString(msg.data)
            logger.info(f"轨迹消息解析成功，包含 {len(trajectory.points)} 个点")
        except Exception as e:
            logger.error(f"轨迹消息解析失败: {e}")
            return

        # 【1】打印订阅得到的/planning/trajectory中所有的数据
        logger.info("=" * 60)
        logger.info(f"【原始轨迹数据】{source_topic} 接收到的数据:")
        logger.info(f"轨迹点总数: {len(trajectory.points)}")
        logger.info(
            f"轨迹头信息: frame_id={getattr(trajectory.header, 'frame_id', 'N/A')}, "
            f"timestamp_sec={getattr(trajectory.header, 'timestamp_sec', 'N/A')}"
        )

        # 只打印来自 /planning/trajectory 话题的轨迹点详情
        if source_topic == "/planning/trajectory":
            logger.info("轨迹点详情 (planning/trajectory):")
            try:
                for i, pt in enumerate(trajectory.points):
                    try:
                        # 安全获取轨迹点属性
                        x_val = getattr(pt, "x", 0.0)
                        y_val = getattr(pt, "y", 0.0)
                        theta_val = getattr(pt, "theta", 0.0)
                        v_val = getattr(pt, "v", 0.0)
                        a_val = getattr(pt, "a", 0.0)
                        t_val = getattr(pt, "t", 0.0)

                        logger.info(
                            f"  [{source_topic}] 原始轨迹点[{i:3d}]: x={x_val:8.3f}, y={y_val:8.3f}, "
                            f"theta={theta_val:6.3f}, v={v_val:6.3f}, a={a_val:6.3f}, t={t_val:6.3f}"
                        )
                    except Exception as e:
                        logger.error(f"打印原始轨迹点[{i}]时出错: {e}")
                        continue
            except Exception as e:
                logger.error(f"遍历原始轨迹点时出错: {e}")
        else:
            logger.debug(f"跨过打印 {source_topic} 话题的轨迹点详情")

        logger.info(f"原始二进制数据长度: {len(msg.data)} bytes")
        logger.info("=" * 60)

        # 创建LGSVL兼容的轨迹消息
        ref_traj = RefTrajectory()
        ref_traj.header.stamp = rospy.Time.now()
        ref_traj.header.frame_id = "base_link"

        ref_traj.horn = 0

        # 处理轨迹点
        for pt in trajectory.points:
            # 处理所有轨迹点，不进行采样
            traj_pt = TrajectoryPoint()
            # 直接映射坐标：x->east, y->north, theta->yaw, v->speed, a->acc, t->time
            traj_pt.east = pt.x
            traj_pt.north = pt.y
            traj_pt.yaw = getattr(pt, "theta", 0.0)
            traj_pt.speed = getattr(pt, "v", 0.0)
            traj_pt.acc = getattr(pt, "a", 0.0)
            traj_pt.time = getattr(pt, "t", 0.0)
            ref_traj.trajectory.append(traj_pt)

            # 注意：新的RefTrajectory消息不再包含planningVel和planningAcc字段
            # 速度和加速度信息现在包含在TrajectoryPoint中

        # 【2】打印将要发布的轨迹数据，区分发布话题
        logger.info("=" * 60)
        logger.info(f"【转换后轨迹数据】{target_topic} 即将发布的数据:")
        logger.info(
            f"消息头信息: frame_id={ref_traj.header.frame_id}, timestamp={ref_traj.header.stamp}"
        )
        logger.info(f"喇叭状态: {ref_traj.horn}")
        logger.info(f"数据流向: {source_topic} → {target_topic}")

        # 安全获取轨迹点列表，避免None值错误
        trajectory_points = ref_traj.trajectory or []
        logger.info(f"转换后轨迹点总数: {len(trajectory_points)}")

        # 打印转换后的TrajectoryPoint轨迹点详情（所有话题都打印）
        current_thread = threading.current_thread().name
        logger.info(
            f"TrajectoryPoint轨迹点详情 (发布到 {target_topic}) [线程: {current_thread}]:"
        )
        try:
            for i, traj_pt in enumerate(trajectory_points):
                try:
                    # 安全获取TrajectoryPoint属性
                    east_val = getattr(traj_pt, "east", 0.0)
                    north_val = getattr(traj_pt, "north", 0.0)
                    yaw_val = getattr(traj_pt, "yaw", 0.0)
                    speed_val = getattr(traj_pt, "speed", 0.0)
                    acc_val = getattr(traj_pt, "acc", 0.0)
                    time_val = getattr(traj_pt, "time", 0.0)

                    logger.info(
                        f"  [{current_thread}][{target_topic}] 轨迹点[{i:3d}]: east={east_val:8.3f}, north={north_val:8.3f}, "
                        f"yaw={yaw_val:6.3f}, speed={speed_val:6.3f}, acc={acc_val:6.3f}, time={time_val:6.3f}"
                    )
                except Exception as e:
                    logger.error(f"打印轨迹点[{i}]时出错: {e}")
                    continue

        except Exception as e:
            logger.error(f"遍历轨迹点时出错: {e}")

        # 如果是公交车，显示额外的状态信息
        if get_vehicle_type() == "bus":
            logger.info(
                f"公交车状态: planning_v={self.planning_v:.3f}, planning_a={self.planning_a:.3f}"
            )

        logger.info("=" * 60)

        # 发布处理后的轨迹
        publisher.publish(ref_traj)

        # 限流日志输出
        safe_trajectory_len = len(ref_traj.trajectory) if ref_traj.trajectory else 0
        current_thread = threading.current_thread().name
        logger.info(
            f"[线程: {current_thread}] ✓ 轨迹发布完成 ({source_topic} → {target_topic}): points={safe_trajectory_len}, "
            f"horn={ref_traj.horn}"
        )

    def set_stop(self, stop=True):
        """控制处理器的启停状态，包含完整的ROS资源清理"""
        self.stopped = stop

        if stop:
            # 清理定时器
            if hasattr(self, "ego_position_timer") and self.ego_position_timer:
                self.ego_position_timer.shutdown()
                self.ego_position_timer = None

            # 清理所有发布器
            if hasattr(self, "lgsvl_trajectory") and self.lgsvl_trajectory:
                self.lgsvl_trajectory.unregister()
                self.lgsvl_trajectory = None

            if hasattr(self, "lgsvl_trajectory_bag") and self.lgsvl_trajectory_bag:
                self.lgsvl_trajectory_bag.unregister()
                self.lgsvl_trajectory_bag = None

            if hasattr(self, "lgsvl_obstacle_pub") and self.lgsvl_obstacle_pub:
                self.lgsvl_obstacle_pub.unregister()
                self.lgsvl_obstacle_pub = None

            # 清理所有订阅器
            if hasattr(self, "trajectory_bag_sub") and self.trajectory_bag_sub:
                self.trajectory_bag_sub.unregister()
                self.trajectory_bag_sub = None

            if hasattr(self, "trajectory_sub") and self.trajectory_sub:
                self.trajectory_sub.unregister()
                self.trajectory_sub = None

            if hasattr(self, "clock_sub") and self.clock_sub:
                self.clock_sub.unregister()
                self.clock_sub = None

            if hasattr(self, "odom_sub") and self.odom_sub:
                self.odom_sub.unregister()
                self.odom_sub = None

            if hasattr(self, "obstacles_sub") and self.obstacles_sub:
                self.obstacles_sub.unregister()
                self.obstacles_sub = None

            logger.info(
                "✓ 轨迹处理器已停止，所有ROS资源已清理：3个发布器、5个订阅器、1个定时器"
            )
        else:
            # 重新创建定时器（重启时）
            if (
                not hasattr(self, "ego_position_timer")
                or self.ego_position_timer is None
            ):
                self.ego_position_timer = rospy.Timer(
                    rospy.Duration(0.1), self.publish_ego_position
                )
            logger.info("轨迹处理器已重启")


if __name__ == "__main__":
    processor = TrajectoryProcessor()
    logger.info("Trajectory processor created, ready for use")
