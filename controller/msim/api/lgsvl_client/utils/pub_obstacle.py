#!/usr/bin/python3
# -*- coding: utf-8 -*-
"""
障碍物处理器模块

该模块负责：
1. 订阅 /perception/fusion/obstacles 话题（BinaryData格式的TrackedObjects）
2. 解析 Protobuf 格式的障碍物数据
3. 转换为 LGSVL 兼容的 PDPObstacleArray 格式
4. 发布到 /lgsvl_pdp_obstacle 话题

"""

import rospy
import sys
import os
import math
import numpy as np

from utils.loggers import LoggerProxy

logger = LoggerProxy("pub_obstacle")

# LGSVL 消息类型导入
from lgsvl_msgs.msg import PDPObstacleArray, PDPObstacle

# ROS 消息类型导入
from autopilot_msgs.msg import BinaryData

try:
    from common.object_pb2 import TrackedObjects

    logger.info("成功导入 TrackedObjects from common.object_pb2")
except ImportError as e:
    logger.error(f"无法导入 TrackedObjects: {e}")
    logger.error("关键依赖缺失，程序无法正常运行")
    raise ImportError(f"Failed to import TrackedObjects: {e}") from e


class ObstacleProcessor:
    """
    障碍物处理器类

    功能：
    1. 订阅 /perception/fusion/obstacles 话题
    2. 解析 TrackedObjects 数据
    3. 转换为 PDPObstacleArray 格式
    4. 发布到 /lgsvl_pdp_obstacle 话题
    """

    def __init__(self):
        """
        初始化障碍物处理器
        """
        self.stopped = False

        # 统计信息
        self.processed_count = 0
        self.published_count = 0

        # 创建 PDPObstacleArray 发布器
        self.pdp_obstacle_pub = rospy.Publisher(
            "/lgsvl_pdp_obstacle", PDPObstacleArray, queue_size=1
        )

        # 订阅感知融合障碍物话题
        rospy.Subscriber(
            "/perception/fusion/obstacles_bag", BinaryData, self.on_obstacles_received
        )

        logger.info("障碍物处理器初始化完成，准备接收障碍物数据")

    def on_obstacles_received(self, msg):
        """
        处理障碍物数据的主回调函数

        参数:
            msg (BinaryData): 包含 TrackedObjects 数据的二进制消息
        """
        logger.info(f"障碍物回调函数被调用，消息大小: {len(msg.data)} bytes")

        if self.stopped:
            logger.info("处理器已停止，跳过障碍物处理")
            return

        # 解析二进制消息
        try:
            tracked_objects = TrackedObjects()
            tracked_objects.ParseFromString(msg.data)
            logger.info(
                f"障碍物消息解析成功，包含 {len(tracked_objects.objs)} 个障碍物"
            )
        except Exception as e:
            logger.error(f"障碍物消息解析失败: {e}")
            return

        self.processed_count += 1

        # 【1】打印订阅得到的 /perception/fusion/obstacles 中所有的数据
        logger.info("=" * 60)
        logger.info("【原始障碍物数据】/perception/fusion/obstacles 接收到的数据:")
        logger.info(f"障碍物总数: {len(tracked_objects.objs)}")

        # 打印头部信息
        if hasattr(tracked_objects, "header") and tracked_objects.header:
            logger.info(
                f"消息头信息: frame_id={getattr(tracked_objects.header, 'frame_id', 'N/A')}, "
                f"timestamp_sec={getattr(tracked_objects.header, 'timestamp_sec', 'N/A')}"
            )

        logger.info("=" * 60)

        # 创建 LGSVL 兼容的 PDPObstacleArray 消息
        pdp_obstacle_array = PDPObstacleArray()
        pdp_obstacle_array.obstacles = []

        # 处理每个障碍物
        for index, obj_tmp in enumerate(tracked_objects.objs):
            try:
                # 获取障碍物对象
                obj = obj_tmp.obj if hasattr(obj_tmp, "obj") else obj_tmp

                # 创建 PDPObstacle 实例
                pdp_obstacle = PDPObstacle()

                # 设置基础属性
                pdp_obstacle.id = getattr(
                    obj, "id", index
                )  # 使用原始ID，或索引作为备用
                pdp_obstacle.type = getattr(
                    obj, "type", 0
                )  # 直接使用原始类型，不进行映射

                # 检查并提取坐标信息
                trace_list = getattr(obj_tmp, "trace", [])
                if not trace_list:
                    logger.error(
                        f"障碍物[{index}] ID={getattr(obj, 'id', 'N/A')} 缺少 trace 数据，已跳过"
                    )
                    continue

                last_trace = trace_list[-1]
                last_position = getattr(last_trace, "position", None)
                if not last_position:
                    logger.error(
                        f"障碍物[{index}] ID={getattr(obj, 'id', 'N/A')} 最后一个 trace 缺少 position 数据，已跳过"
                    )
                    continue

                # 设置位置信息
                pdp_obstacle.x = getattr(last_position, "x", 0.0)
                pdp_obstacle.y = getattr(last_position, "y", 0.0)
                pdp_obstacle.z = getattr(last_position, "z", 0.0)

                # 设置姿态信息 - 使用obj_tmp.yaw
                pdp_obstacle.yaw = getattr(obj_tmp, "yaw", 0.0)

                # 设置尺寸信息
                size = getattr(obj, "size", None)
                if size:
                    pdp_obstacle.length = getattr(size, "x", 1.0)  # 长度，默认1.0
                    pdp_obstacle.width = getattr(size, "y", 1.0)  # 宽度，默认1.0
                    pdp_obstacle.height = getattr(size, "z", 1.0)  # 高度，默认1.0
                else:
                    pdp_obstacle.length = pdp_obstacle.width = pdp_obstacle.height = 1.0

                # 添加到障碍物数组
                pdp_obstacle_array.obstacles.append(pdp_obstacle)

            except Exception as e:
                logger.error(f"处理障碍物[{index}]时发生错误: {e}")
                continue

        # 【2】打印将要发布的 /lgsvl_pdp_obstacle 的所有数据
        logger.info("=" * 60)
        logger.info("【转换后障碍物数据】/lgsvl_pdp_obstacle 即将发布的数据:")
        logger.info(f"PDPObstacle 数组长度: {len(pdp_obstacle_array.obstacles)}")

        logger.info("开始打印 PDPObstacle 详情:")
        try:
            for i, pdp_obj in enumerate(pdp_obstacle_array.obstacles):
                logger.info(
                    f"  PDPObstacle[{i:3d}]: ID={pdp_obj.id:4d}, type={pdp_obj.type:2d}, "
                    f"pos=({pdp_obj.x:8.3f}, {pdp_obj.y:8.3f}, {pdp_obj.z:8.3f}), "
                    f"yaw={pdp_obj.yaw:6.3f}, "
                    f"size=({pdp_obj.length:6.3f}, {pdp_obj.width:6.3f}, {pdp_obj.height:6.3f})"
                )
        except Exception as e:
            logger.error(f"遍历 PDPObstacle 时出错: {e}")

        logger.info("=" * 60)

        # 发布处理后的障碍物数组
        self.pdp_obstacle_pub.publish(pdp_obstacle_array)
        self.published_count += 1

        # 统计日志
        logger.info(
            f"✓ 障碍物发布完成: 处理={len(pdp_obstacle_array.obstacles)}个, "
            f"总处理次数={self.processed_count}, 总发布次数={self.published_count}"
        )

    def set_stop(self, stop=True):
        """
        控制处理器的启停状态

        参数:
            stop (bool): True 停止处理器, False 启动处理器
        """
        self.stopped = stop
        logger.info(f"障碍物处理器 {'stopped' if stop else 'started'}")

    def get_statistics(self):
        """
        获取处理器统计信息

        返回:
            dict: 包含处理和发布统计的字典
        """
        return {
            "processed_count": self.processed_count,
            "published_count": self.published_count,
            "stopped": self.stopped,
        }


if __name__ == "__main__":
    # 示例用法
    rospy.init_node("obstacle_processor_node", anonymous=True)
    processor = ObstacleProcessor()
    logger.info("障碍物处理器创建完成，准备使用")

    try:
        rospy.spin()
    except KeyboardInterrupt:
        logger.info("接收到键盘中断，关闭障碍物处理器")
        processor.set_stop(True)
