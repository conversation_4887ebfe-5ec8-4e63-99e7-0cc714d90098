import math

# helper functions
def unity2real_pos(x, y, z):
    return (z, -x, y)

def real_pos2unity(x, y, z):
    return (-y, z, x)

def unity2read_rot(x, y, z):
    return (-z, x, -y)

def unity2real_qut(x, y, z, w):
    return (y, -z, -x, w)

def quaternion2rpy(x, y, z, w):
    r = math.atan2(2 * (w * x + y * z), 1 - 2 * (x * x + y * y))
    p = math.asin(2 * (w * y - z * x))
    y = math.atan2(2 * (w * z + x * y), 1 - 2 * (y * y + z * z))
    return r, p, y

class frameTransform(object):
    
    def __init__(self, utm_zone: int):
        self._zone = utm_zone
        # Ellipsoid model constants (actual values here are for WGS84)
        self.kSmA = 6378137.0
        self.kSmB = 6356752.31425
        self.kUtmScaleFactor = 0.9996
        self.kSinsRadToDeg = 57.295779513
        self.kSinsDegToRad = 0.01745329252

        self.kSinsR0 = 6378137.0
        self.kSinsE  = 0.08181919108425
        self.kSinsE2 = 0.00669437999013


    def __get_Footpointlatitude(self, utm_y: float):
        n = (self.kSmA - self.kSmB) / (self.kSmA + self.kSmB)
        alpha = (self.kSmA + self.kSmB)*(1 + n**2/4.0 + n**4/64.0)/2
        yy = utm_y / alpha
        beta = 1.5*n - 27*n**3/32 + 269*n**5/512
        gamma = 21*n**2/16 - 55*n**4/32
        delta = 151*n**3/96 - 417*n**5/128
        epsilon = 1097*n**4 / 512
        result = yy + (beta*math.sin(2*yy)) + (gamma*math.sin(4*yy)) + (delta*math.sin(6*yy)) + (epsilon*math.sin(8*yy))
        return result

    def setZone(self, utm_zone: int):
        self._zone = utm_zone

    def UtmCentralMeridian(self, utm_zone: int):
        return (-183.0 + (utm_zone*6.0))*self.kSinsDegToRad

    def UtmXYToLatlon(self, utm_x: float, utm_y: float):
        xx = utm_x
        xx -= 500000.0
        xx /= self.kUtmScaleFactor

        yy = utm_y
        yy /= self.kUtmScaleFactor
        cmeridian = self.UtmCentralMeridian(self._zone)
        return self.mapXYToLatLlon(xx, yy, cmeridian)
    
    def mapXYToLatLlon(self, utm_x: float, utm_y: float, cmeridian: float):
        phif = self.__get_Footpointlatitude(utm_y)
        ep2 = (self.kSmA**2 - self.kSmB**2) / (self.kSmB**2)
        cf = math.cos(phif)
        nuf2 = ep2 * cf**2
        nf = self.kSmA**2 / (self.kSmB * math.sqrt(1 + nuf2))
        nfpow = nf
        tf = math.tan(phif)
        tf2 = tf**2
        tf4 = tf2**2

        x1frac = 1.0 / (nfpow * cf)
        nfpow *= nf
        x2frac = tf / (2.0 * nfpow)
        nfpow *= nf
        x3frac = 1.0 / (6.0 * nfpow * cf)
        nfpow *= nf
        x4frac = 1.0 / (24.0 * nfpow)
        nfpow *= nf
        x5frac = 1.0 / (120.0 * nfpow * cf)
        nfpow *= nf
        x6frac = tf / (720.0 * nfpow)
        nfpow *= nf
        x7frac = 1.0 / (5040.0 * nfpow * cf)
        nfpow *= nf
        x8frac = tf / (40320.0 * nfpow)

        x2poly = -1.0 - nuf2
        x3poly = -1.0 - 2*tf2 - nuf2
        x4poly = 5.0 + 3.0*tf2 + 6.0*nuf2 -6.0*tf2*nuf2 - 3.0*nuf2**2 - 9.0*tf2*nuf2**2
        x5poly = 5.0 + 28.0*tf2 + 24.0*tf4 + 6.0*nuf2 + 8.0*tf2*nuf2
        x6poly = -61.0 - 90.0*tf2 - 45.0*tf4 - 107.0*nuf2 + 162.0*tf2*nuf2
        x7poly = -61.0 - 662.0*tf2 - 1320.0*tf4 - 720.0*(tf4*tf2)
        x8poly = 1385.0 + 3633.0*tf2 + 4095.0*tf4 + 1575.0*(tf4*tf2)

        lat = phif + x2frac*x2poly*utm_x**2 + x4frac*x4poly*utm_x**4 + x6frac*x6poly*utm_x**6 + x8frac*x8poly*utm_x**8
        lon = cmeridian + x1frac*utm_x + x3frac*x3poly*utm_x**3 + x5frac*x5poly*utm_x**5 + x7frac*x7poly*utm_x**7
        return (lon*self.kSinsRadToDeg, lat*self.kSinsRadToDeg)

def is_ipv4(ip: str) -> bool:
    return ([True] * 4 == [(True) if (x.isdigit() and 0 <= int(x) <= 255) else (False) for x in ip.split('.')])