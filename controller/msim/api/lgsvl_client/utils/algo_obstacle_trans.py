#!/usr/bin/python3
# -*- coding: utf-8 -*-

import rospy
import math
import sys
import os

from autopilot_msgs.msg import BinaryData
from rosgraph_msgs.msg import Clock
from visualization_msgs.msg import Marker, MarkerArray
from geometry_msgs.msg import Point

from common.localization_pb2 import Localization
from common.object_pb2 import TrackedObjects

from utils.loggers import LoggerProxy

logger = LoggerProxy("obstacle_coordinate_transformer")


class ObstacleCoordinateTransformer:
    """障碍物坐标变换器 - 将bag中的障碍物从bag定位坐标系转换到当前定位坐标系"""

    def __init__(self, shield_time_ns=None):
        """初始化坐标变换器"""
        # 屏蔽时间
        self.shield_time_ns = shield_time_ns
        self.current_time_ns = 0

        # 停止状态
        self.stopped = False

        # 当前定位和bag定位数据
        self.current_localization = None
        self.bag_localization = None
        self.last_bag_obstacles = None

        # 订阅话题
        self.current_loc_sub = rospy.Subscriber(
            "/localization/global",
            BinaryData,
            self.on_current_localization,
            queue_size=1,
        )
        self.bag_loc_sub = rospy.Subscriber(
            "/localization/global_bag",
            BinaryData,
            self.on_bag_localization,
            queue_size=1,
        )
        self.bag_obstacles_sub = rospy.Subscriber(
            "/perception/fusion/obstacles_bag",
            BinaryData,
            self.on_bag_obstacles,
            queue_size=1,
        )
        self.clock_sub = rospy.Subscriber("/clock", Clock, self.clock_callback, queue_size=1)

        # 发布转换后的障碍物
        self.obstacles_pub = rospy.Publisher(
            "/perception/fusion/obstacles", BinaryData, queue_size=1
        )

        # 发布可视化信息
        self.fusion_viz_pub = rospy.Publisher(
            "/perception/fusion/fusion_viz", MarkerArray, queue_size=1
        )

        logger.info("障碍物坐标变换器已启动")
        if self.shield_time_ns:
            logger.info(f"设置了屏蔽时间: {self.shield_time_ns} ns")

    def clock_callback(self, msg):
        """处理时钟回调，更新当前时间"""
        if self.stopped:
            return
        # 将时间转换为纳秒 (秒部分 * 1e9 + 纳秒部分)
        self.current_time_ns = msg.clock.secs * int(1e9) + msg.clock.nsecs

    def on_current_localization(self, msg):
        """接收当前定位数据"""
        if self.stopped:
            return
        try:
            loc = Localization()
            loc.ParseFromString(msg.data)
            self.current_localization = loc
            logger.info(
                f"当前定位: UTM({loc.position.x:.2f}, {loc.position.y:.2f}), yaw={loc.yaw:.3f}"
            )
        except Exception as e:
            logger.error(f"解析当前定位数据失败: {e}")

    def on_bag_localization(self, msg):
        """接收bag定位数据"""
        if self.stopped:
            return
        try:
            loc = Localization()
            loc.ParseFromString(msg.data)
            self.bag_localization = loc
            logger.info(
                f"Bag定位: UTM({loc.position.x:.2f}, {loc.position.y:.2f}), yaw={loc.yaw:.3f}"
            )
        except Exception as e:
            logger.error(f"解析bag定位数据失败: {e}")

    def on_bag_obstacles(self, msg):
        """接收bag障碍物数据并进行坐标变换"""
        if self.stopped:
            return
        
        # 检查屏蔽时间：如果当前时间小于屏蔽时间，则不处理此主题的轨迹
        if (
            self.shield_time_ns is not None
            and self.current_time_ns < self.shield_time_ns
        ):
            logger.debug(
                f"尚未到达屏蔽时间: {self.shield_time_ns}ns, 当前时间: {self.current_time_ns}ns, 忽略障碍物处理"
            )
            return

        try:
            obstacles = TrackedObjects()
            obstacles.ParseFromString(msg.data)
            self.last_bag_obstacles = obstacles

            # 如果定位数据齐全，进行坐标变换
            if self.current_localization and self.bag_localization:
                transformed_obstacles = self.transform_obstacles(obstacles)
                self.publish_transformed_obstacles(transformed_obstacles)

        except Exception as e:
            logger.error(f"处理bag障碍物数据失败: {e}")

    def transform_obstacles(self, bag_obstacles):
        """
        坐标变换：将bag坐标系下的障碍物转换到当前坐标系

        变换步骤：
        1. 计算两个定位间的平移和旋转差异
        2. 对每个障碍物应用逆变换和正变换
        """
        # 获取坐标变换参数
        current_loc = self.current_localization
        bag_loc = self.bag_localization

        # 计算定位差异
        dx = current_loc.position.x - bag_loc.position.x
        dy = current_loc.position.y - bag_loc.position.y
        dyaw = current_loc.yaw - bag_loc.yaw

        # 创建转换后的障碍物对象
        transformed_obstacles = TrackedObjects()
        transformed_obstacles.header.CopyFrom(bag_obstacles.header)
        transformed_obstacles.header.stamp.CopyFrom(current_loc.header.stamp)
        transformed_obstacles.sensor_name = bag_obstacles.sensor_name

        logger.info(f"坐标变换参数: dx={dx:.2f}, dy={dy:.2f}, dyaw={dyaw:.3f}")

        # 对每个障碍物进行坐标变换
        for bag_obj in bag_obstacles.objs:
            transformed_obj = transformed_obstacles.objs.add()
            self.transform_single_obstacle(
                bag_obj, transformed_obj, dx, dy, dyaw, current_loc, bag_loc
            )

        logger.info(f"转换了 {len(transformed_obstacles.objs)} 个障碍物")
        return transformed_obstacles

    def transform_single_obstacle(
        self, bag_obj, transformed_obj, dx, dy, dyaw, current_loc, bag_loc
    ):
        """转换单个障碍物的坐标"""
        # 复制基本属性
        transformed_obj.CopyFrom(bag_obj)

        # 1. 转换障碍物中心位置 (obj.center)
        if bag_obj.obj.HasField("center"):
            # 获取相对于bag车辆的位置
            rel_x = bag_obj.obj.center.x
            rel_y = bag_obj.obj.center.y

            # 转换到UTM坐标系（bag）
            cos_bag_yaw = math.cos(bag_loc.yaw)
            sin_bag_yaw = math.sin(bag_loc.yaw)
            utm_x_global = (
                bag_loc.position.x + rel_x * cos_bag_yaw - rel_y * sin_bag_yaw
            )
            utm_y_global = (
                bag_loc.position.y + rel_x * sin_bag_yaw + rel_y * cos_bag_yaw
            )

            # 使用绝对坐标计算在当前车辆坐标系下的相对位置
            cos_current_yaw = math.cos(current_loc.yaw)
            sin_current_yaw = math.sin(current_loc.yaw)
            rel_utm_x = utm_x_global - current_loc.position.x
            rel_utm_y = utm_y_global - current_loc.position.y
            new_rel_x = rel_utm_x * cos_current_yaw + rel_utm_y * sin_current_yaw
            new_rel_y = -rel_utm_x * sin_current_yaw + rel_utm_y * cos_current_yaw

            transformed_obj.obj.center.x = new_rel_x
            transformed_obj.obj.center.y = new_rel_y
            transformed_obj.obj.center.z = bag_obj.obj.center.z

            # 同步更新x_distance和y_distance
            transformed_obj.obj.x_distance = new_rel_x
            transformed_obj.obj.y_distance = new_rel_y

        # 2. 转换障碍物轮廓点 (obj.contour)
        del transformed_obj.obj.contour[:]
        for contour_pt in bag_obj.obj.contour:
            new_pt = transformed_obj.obj.contour.add()
            new_pt.x, new_pt.y = self.transform_point(
                contour_pt.x, contour_pt.y, dx, dy, dyaw, current_loc, bag_loc
            )
            new_pt.z = contour_pt.z

        # 3. 转换UTM轮廓点 (obj.polygon)
        del transformed_obj.obj.polygon[:]
        for polygon_pt in bag_obj.obj.polygon:
            new_pt = transformed_obj.obj.polygon.add()
            # 使用与 contour 相同的坐标变换逻辑（polygon 实际上也是相对坐标）
            new_pt.x, new_pt.y = self.transform_point(
                polygon_pt.x, polygon_pt.y, dx, dy, dyaw, current_loc, bag_loc
            )
            new_pt.z = polygon_pt.z

        # 4. 处理航向角 (绝对) 与相对角度
        if bag_obj.HasField("yaw"):
            # yaw 为绝对航向角，直接复制
            transformed_obj.yaw = bag_obj.yaw

        if bag_obj.obj.HasField("angle"):
            # angle 为相对角度，需要扣除两帧车辆航向差
            transformed_obj.obj.angle = (bag_obj.obj.angle - dyaw) % (2 * math.pi)

        # 5. 速度向量旋转到当前车辆坐标系
        if bag_obj.obj.HasField("velocity"):
            vx, vy = self.rotate_vector(
                bag_obj.obj.velocity.x, bag_obj.obj.velocity.y, -dyaw
            )
            transformed_obj.obj.velocity.x = vx
            transformed_obj.obj.velocity.y = vy
            transformed_obj.obj.velocity.z = bag_obj.obj.velocity.z

        # 6. 绝对 UTM 坐标保持不变
        if bag_obj.HasField("longitude_p") and bag_obj.HasField("latitude_p"):
            transformed_obj.longitude_p = bag_obj.longitude_p
            transformed_obj.latitude_p = bag_obj.latitude_p

    def transform_point(self, x, y, dx, dy, dyaw, current_loc, bag_loc):
        """将 bag 坐标系下 (x, y) 相对坐标转换到 current 坐标系下的相对坐标。
        说明：
        1. 该函数首先利用 bag 车辆的定位信息和朝向，将相对坐标转换为唯一的世界(UTM)坐标；
        2. 再结合 current 车辆的定位信息与朝向，计算点在 current 车辆坐标系下的相对坐标。
        3. 与旧实现相比，去除了对 (dx, dy) 的二次平移，避免了“双重平移”错误。
        """
        # 步骤 1: bag 车体坐标系 -> 世界 UTM
        cos_bag = math.cos(bag_loc.yaw)
        sin_bag = math.sin(bag_loc.yaw)
        utm_x = bag_loc.position.x + x * cos_bag - y * sin_bag
        utm_y = bag_loc.position.y + x * sin_bag + y * cos_bag

        # 步骤 2: 世界 UTM -> current 车体坐标系
        cos_current = math.cos(current_loc.yaw)
        sin_current = math.sin(current_loc.yaw)
        rel_utm_x = utm_x - current_loc.position.x
        rel_utm_y = utm_y - current_loc.position.y
        new_x = rel_utm_x * cos_current + rel_utm_y * sin_current
        new_y = -rel_utm_x * sin_current + rel_utm_y * cos_current

        return new_x, new_y

    def rotate_vector(self, vx, vy, angle):
        """旋转速度向量"""
        cos_a = math.cos(angle)
        sin_a = math.sin(angle)
        new_vx = vx * cos_a - vy * sin_a
        new_vy = vx * sin_a + vy * cos_a
        return new_vx, new_vy

    def publish_transformed_obstacles(self, obstacles):
        """发布转换后的障碍物数据"""
        try:
            # 序列化并发布障碍物数据
            obstacles_data = obstacles.SerializeToString()
            binary_msg = BinaryData()
            binary_msg.name = "perception.TrackedObjects"
            binary_msg.data = obstacles_data
            # 使用障碍物消息自身的时间戳，确保与数据一致，避免 0 时间导致负 /clock
            stamp = rospy.Time(
                secs=obstacles.header.stamp.sec,
                nsecs=obstacles.header.stamp.nsec,
            )
            binary_msg.header.stamp = stamp
            binary_msg.header.frame_id = "base_link"

            self.obstacles_pub.publish(binary_msg)

            # 创建并发布可视化信息
            marker_array = MarkerArray()
            marker_id = 0

            for obstacle in obstacles.objs:
                # 创建文本标记
                text_marker = self._get_marker(stamp)
                text_marker.id = marker_id
                marker_id += 1

                # 设置位置为障碍物中心
                if obstacle.obj.HasField("center"):
                    text_marker.pose.position.x = obstacle.obj.center.x
                    text_marker.pose.position.y = obstacle.obj.center.y
                    text_marker.pose.position.z = obstacle.obj.center.z

                # 构造文本信息，可按需展示障碍物ID或其他属性
                obstacle_id = self._extract_obstacle_id(obstacle)
                text_marker.text = f"ID: {obstacle_id}"

                marker_array.markers.append(text_marker)

                # 创建轮廓标记
                contour_marker = self._get_contour_marker(stamp)
                contour_marker.id = marker_id
                marker_id += 1

                # 添加转换后的轮廓点
                for contour_pt in obstacle.obj.contour:
                    point = Point()
                    point.x = contour_pt.x
                    point.y = contour_pt.y
                    point.z = contour_pt.z
                    contour_marker.points.append(point)

                # 闭合轮廓线：添加第一个点
                if len(obstacle.obj.contour) > 0:
                    point = Point()
                    point.x = obstacle.obj.contour[0].x
                    point.y = obstacle.obj.contour[0].y
                    point.z = obstacle.obj.contour[0].z
                    contour_marker.points.append(point)

                marker_array.markers.append(contour_marker)

            # 发布可视化信息
            self.fusion_viz_pub.publish(marker_array)

            logger.info(
                f"发布了转换后的障碍物数据，包含 {len(obstacles.objs)} 个障碍物，以及相应的可视化信息"
            )

        except Exception as e:
            logger.error(f"发布障碍物数据失败: {e}")

    def _get_marker(self, stamp):
        """创建用于可视化的Marker对象"""
        contour_marker = Marker()
        contour_marker.header.frame_id = "base_link"
        contour_marker.header.stamp = stamp
        contour_marker.ns = "track_info"
        contour_marker.action = Marker.ADD
        contour_marker.type = Marker.TEXT_VIEW_FACING
        # 对于 TEXT_VIEW_FACING，通常使用 scale.z 表示文字高度，但保持三个轴一致更直观
        contour_marker.scale.x = 0.7
        contour_marker.scale.y = 0.7
        contour_marker.scale.z = 0.7
        contour_marker.color.g = 1.0
        contour_marker.color.a = 1.0
        contour_marker.pose.orientation.w = 1.0
        contour_marker.points.clear()
        return contour_marker

    def _get_contour_marker(self, stamp):
        """创建用于轮廓可视化的Marker对象"""
        contour_marker = Marker()
        contour_marker.header.frame_id = "base_link"
        contour_marker.header.stamp = stamp
        contour_marker.ns = "track_contour"
        contour_marker.action = Marker.ADD
        contour_marker.type = Marker.LINE_STRIP
        contour_marker.scale.x = 0.1
        contour_marker.color.g = 1.0
        contour_marker.color.a = 1.0
        contour_marker.pose.orientation.w = 1.0
        contour_marker.points.clear()
        return contour_marker

    def _extract_obstacle_id(self, obstacle):
        """安全获取障碍物ID，兼容不同消息结构"""
        # 1. 直接属性
        if hasattr(obstacle, "id"):
            return obstacle.id
        # 2. 嵌套 obj.id
        if hasattr(obstacle, "obj") and hasattr(obstacle.obj, "id"):
            return obstacle.obj.id
        # 3. 找不到则返回 -1
        return -1

    def run(self):
        """运行主循环"""
        logger.info("障碍物坐标变换器开始运行...")
        rospy.spin()

    def set_stop(self, stop=True):
        """
        控制坐标变换器的启停状态
        
        参数:
            stop (bool): True停止，False启动
        """
        self.stopped = stop
        
        if stop:
            # 停止所有订阅器
            try:
                if hasattr(self, 'current_loc_sub') and self.current_loc_sub:
                    self.current_loc_sub.unregister()
                    self.current_loc_sub = None
                    
                if hasattr(self, 'bag_loc_sub') and self.bag_loc_sub:
                    self.bag_loc_sub.unregister()
                    self.bag_loc_sub = None
                    
                if hasattr(self, 'bag_obstacles_sub') and self.bag_obstacles_sub:
                    self.bag_obstacles_sub.unregister()
                    self.bag_obstacles_sub = None
                    
                if hasattr(self, 'clock_sub') and self.clock_sub:
                    self.clock_sub.unregister()
                    self.clock_sub = None
                    
                # 停止发布器
                if hasattr(self, 'obstacles_pub') and self.obstacles_pub:
                    self.obstacles_pub.unregister()
                    self.obstacles_pub = None
                    
                if hasattr(self, 'fusion_viz_pub') and self.fusion_viz_pub:
                    self.fusion_viz_pub.unregister()
                    self.fusion_viz_pub = None
                    
                logger.info("障碍物坐标变换器已停止，所有发布器和订阅器已取消注册")
                
            except Exception as e:
                logger.error(f"停止障碍物坐标变换器时出错: {e}")
        else:
            logger.info("障碍物坐标变换器已重新启动")


if __name__ == "__main__":
    try:
        # 只有在直接运行时才初始化ROS节点
        rospy.init_node("obstacle_coordinate_transformer", anonymous=False)

        # 示例：可以从rosparam或命令行获取shield_time_ns
        # shield_time = rospy.get_param("~shield_time_ns", None)
        transformer = ObstacleCoordinateTransformer()
        transformer.run()
    except rospy.ROSInterruptException:
        logger.info("障碍物坐标变换器关闭")
    except Exception as e:
        logger.error(f"障碍物坐标变换器运行失败: {e}")
