#!/usr/bin/python3
# -*- coding: utf-8 -*-
import rospy
from std_msgs.msg import String
from typing import Optional

from utils.loggers import LoggerProxy

logger = LoggerProxy("pub_traffic_light")

# LGSVL 消息类型
from lgsvl_msgs.msg import PlanningDecisionData

# 交通灯 protobuf 消息
from common.traffic_light_pb2 import TrafficLights


class TrafficLightProcessor:
    """
    交通灯处理器，负责接收感知模块的交通灯状态并转换为LGSVL格式发布

    功能：
    1. 订阅 /perception/camera/trfclts_state 话题
    2. 解析交通灯状态信息
    3. 转换为 lgsvl_msgs/PlanningDecisionData 格式
    4. 发布到 /lgsvl_pdp_signal 话题
    """

    def __init__(self):
        """初始化交通灯处理器"""
        # 停止状态
        self.stopped = False
        
        # 创建交通灯信号发布器
        self.lgsvl_signal_pub = rospy.Publisher(
            "/lgsvl_pdp_signal", PlanningDecisionData, queue_size=1
        )

        # 订阅感知模块的交通灯状态话题
        self.traffic_light_sub = rospy.Subscriber(
            "/perception/camera/trfclts_state",
            String,
            self.on_traffic_light_state,
            queue_size=1,
        )

        # 交通灯状态映射 (protobuf状态值到颜色名称)
        self.state_to_color = {1: "RED", 2: "YELLOW", 3: "GREEN"}

        # 交通灯方向映射
        self.direction_mapping = {
            "straight": "Straight",
            "left": "Left",
            "right": "Right",
            "u_turn": "U-Turn",
        }

        logger.info("交通灯处理器初始化完成")

    def on_traffic_light_state(self, msg):
        """
        处理交通灯状态消息的回调函数

        参数:
            msg (String): 包含交通灯状态的字符串消息
        """
        if self.stopped:
            return
            
        logger.info(f"接收到交通灯状态消息，数据长度: {len(msg.data)} bytes")
        self._process_and_publish_traffic_lights(msg)

    def _process_and_publish_traffic_lights(self, msg):
        """
        处理并发布交通灯状态

        参数:
            msg (String): 原始交通灯状态消息
        """
        logger.info("开始处理交通灯状态数据")

        # 解析 protobuf 消息
        try:
            traffic_lights = TrafficLights()
            traffic_lights.ParseFromString(msg.data)
            logger.info("交通灯消息解析成功")
        except Exception as e:
            logger.error(f"交通灯消息解析失败: {e}")
            return

        # 【1】打印接收到的原始交通灯数据
        logger.info("=" * 60)
        logger.info("【原始交通灯数据】/perception/camera/trfclts_state 接收到的数据:")
        logger.info(
            f"时间戳: {traffic_lights.header.stamp.sec}.{traffic_lights.header.stamp.nsec}"
        )
        logger.info(f"Frame ID: {traffic_lights.header.frame_id}")

        # 打印各方向的交通灯状态
        self._log_traffic_light_detail("直行 Straight", traffic_lights.straight)
        self._log_traffic_light_detail("左转 Left", traffic_lights.left)
        self._log_traffic_light_detail("右转 Right", traffic_lights.right)
        self._log_traffic_light_detail("掉头 U-Turn", traffic_lights.u_turn)
        logger.info("=" * 60)

        # 提取有效的交通灯状态
        active_signal = self._extract_active_signal(traffic_lights)

        # 创建 LGSVL PlanningDecisionData 消息
        planning_msg = PlanningDecisionData()
        planning_msg.header.stamp = rospy.Time.now()
        planning_msg.header.frame_id = "base_link"

        if active_signal:
            # 设置规划决策消息内容
            planning_msg.planningDecisionMsg = active_signal
            logger.info(f"检测到活动交通灯: {active_signal}")
        else:
            # 设置空字符串表示未检测到活动交通灯
            planning_msg.planningDecisionMsg = ""
            logger.info("未检测到活动的交通灯状态")

        # 【2】打印将要发布的 LGSVL 交通灯数据
        logger.info("=" * 60)
        logger.info("【转换后交通灯数据】/lgsvl_pdp_signal 即将发布的数据:")
        logger.info(
            f"消息头信息: frame_id={planning_msg.header.frame_id}, "
            f"timestamp={planning_msg.header.stamp}"
        )
        logger.info(f"规划决策消息: {planning_msg.planningDecisionMsg}")
        logger.info("=" * 60)

        # 发布处理后的交通灯信号
        self.lgsvl_signal_pub.publish(planning_msg)

        logger.info(f"✓ 交通灯信号发布完成: planningDecisionMsg='{planning_msg.planningDecisionMsg}'")

    def _log_traffic_light_detail(self, direction_name: str, light_info):
        """
        记录交通灯详细信息

        参数:
            direction_name (str): 方向名称
            light_info: 交通灯信息对象
        """
        try:
            if hasattr(light_info, "id") and light_info.id > 0:
                state_name = self.state_to_color.get(light_info.state, "UNKNOWN")
                logger.info(f"  [{direction_name}]:")
                logger.info(f"    状态 (State): {light_info.state} ({state_name})")
                logger.info(f"    持续时间 (Duration): {light_info.duration} s")
            else:
                logger.info(f"  [{direction_name}]: 未设置 (Not Set)")
        except Exception as e:
            logger.error(f"记录交通灯信息时出错 [{direction_name}]: {e}")

    def _extract_active_signal(self, traffic_lights) -> Optional[str]:
        """
        提取活动的交通灯信号

        参数:
            traffic_lights: TrafficLights protobuf 消息

        返回:
            str: 格式为 "方向:颜色" 的字符串，如 "Straight:GREEN"，如果没有活动信号则返回 None
        """
        # 检查各个方向的交通灯状态
        directions = [
            ("straight", traffic_lights.straight),
            ("left", traffic_lights.left),
            ("right", traffic_lights.right),
            ("u_turn", traffic_lights.u_turn),
        ]

        for direction_key, light_info in directions:
            try:
                # 检查是否有有效的交通灯状态
                if (
                    hasattr(light_info, "id")
                    and light_info.id > 0
                    and hasattr(light_info, "state")
                    and light_info.state > 0
                ):

                    # 获取方向名称
                    direction_name = self.direction_mapping.get(
                        direction_key, direction_key
                    )

                    # 获取颜色名称
                    color_name = self.state_to_color.get(light_info.state, "UNKNOWN")

                    # 组合为标签格式
                    signal_label = f"{direction_name}:{color_name}"

                    logger.info(
                        f"找到活动交通灯: 方向={direction_name}, "
                        f"状态={light_info.state}({color_name}), "
                        f"标签={signal_label}"
                    )

                    return signal_label

            except Exception as e:
                logger.error(f"处理交通灯方向 {direction_key} 时出错: {e}")
                continue

        return None

    def set_stop(self, stop=True):
        """
        控制交通灯处理器的启停状态
        
        参数:
            stop (bool): True停止，False启动
        """
        self.stopped = stop
        
        if stop:
            # 停止所有订阅器和发布器
            try:
                if hasattr(self, 'traffic_light_sub') and self.traffic_light_sub:
                    self.traffic_light_sub.unregister()
                    self.traffic_light_sub = None
                    
                if hasattr(self, 'lgsvl_signal_pub') and self.lgsvl_signal_pub:
                    self.lgsvl_signal_pub.unregister()
                    self.lgsvl_signal_pub = None
                    
                logger.info("交通灯处理器已停止，所有发布器和订阅器已取消注册")
                
            except Exception as e:
                logger.error(f"停止交通灯处理器时出错: {e}")
        else:
            logger.info("交通灯处理器已重新启动")


if __name__ == "__main__":
    processor = TrafficLightProcessor()
    logger.info("交通灯处理器创建完成，准备使用")
