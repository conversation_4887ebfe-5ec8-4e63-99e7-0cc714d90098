#!/usr/bin/python3
# -*- coding: utf-8 -*-
"""
定位数据处理器模块

功能:
1. 订阅 /localization/global_bag 话题接收定位数据
2. 解析 protobuf 格式的定位消息获取位置信息 (x, y, z, yaw)
3. 直接使用原始坐标，不进行转换
4. 根据屏蔽时间动态设置状态: 屏蔽时间前 state=0, 之后 state=1
5. 始终通过定位数据回调发布 PDPControl 消息到 /lgsvl_pdp_control 话题

遵循SOLID原则:
- 单一职责原则: 专门处理定位数据转换和发布
- 开闭原则: 通过继承可扩展功能
- 依赖倒置原则: 依赖抽象接口而非具体实现
"""

import rospy
from rosgraph_msgs.msg import Clock
from autopilot_msgs.msg import BinaryData
from lgsvl_msgs.msg import PDPControl
from google.protobuf.message import DecodeError

from utils.loggers import LoggerProxy

# 导入定位数据的protobuf定义
try:
    from common.localization_pb2 import Localization
except ImportError:
    import sys

    sys.path.append("/home/<USER>/workspace/mount/dist-packages")
    sys.path.append("/home/<USER>/workspace/mount/dist-packages/common")
    from common.localization_pb2 import Localization

logger = LoggerProxy("pub_loc")


class LocalizationProcessor:
    """
    定位数据处理器类

    职责:
    - 接收和解析定位数据
    - 执行坐标转换
    - 管理屏蔽时间逻辑
    - 发布PDPControl消息
    """

    def __init__(self, shield_time_ns=None):
        """
        初始化定位数据处理器

        参数:
            shield_time_ns (int, optional): 屏蔽时间（纳秒）
                如果为None则不使用屏蔽时间逻辑
        """
        # 状态管理
        self.stopped = False
        self.shield_time_ns = shield_time_ns  # 屏蔽时间（纳秒）
        self.current_time_ns = 0  # 当前时间（纳秒）
        self.shield_time_passed = False  # 屏蔽时间是否已过

        # 定位数据状态
        self.localization_initialized = False
        self.warning_printed = False

        # 最新的定位数据
        self.latest_x = 0.0
        self.latest_y = 0.0
        self.latest_z = 0.0
        self.latest_yaw = 0.0

        # 创建PDP控制消息发布器
        self.pdp_control_pub = rospy.Publisher(
            "/lgsvl_pdp_control", PDPControl, queue_size=1
        )

        # 订阅定位数据话题
        rospy.Subscriber(
            "/localization/global_bag",
            BinaryData,
            self.on_localization_received,
            queue_size=1,
        )

        # 订阅时钟话题以获取当前时间
        rospy.Subscriber("/clock", Clock, self.clock_callback)

        logger.info(
            f"定位处理器初始化完成, 屏蔽时间: "
            f"{'None' if shield_time_ns is None else f'{shield_time_ns}ns'}"
        )

    def clock_callback(self, msg):
        """
        处理时钟回调，更新当前时间，管理屏蔽时间状态

        参数:
            msg (Clock): 时钟消息
        """
        # 将时间转换为纳秒 (秒部分 * 1e9 + 纳秒部分)
        self.current_time_ns = msg.clock.secs * int(1e9) + msg.clock.nsecs
        logger.debug(f"时钟更新: {self.current_time_ns}ns")

        # 检查屏蔽时间状态
        if (
            self.shield_time_ns is not None
            and not self.shield_time_passed
            and self.current_time_ns >= self.shield_time_ns
        ):
            # 屏蔽时间刚刚到达
            self.shield_time_passed = True
            logger.info(f"屏蔽时间已过({self.shield_time_ns}ns)，状态将从1变为0")

    def coordinate_transform(self, parsed_x, parsed_y):
        """
        坐标处理（不进行转换，直接使用原始坐标）

        参数:
            parsed_x (float): 解析得到的x坐标
            parsed_y (float): 解析得到的y坐标

        返回:
            tuple[float, float]: 处理后的(x, y)坐标
        """
        # 直接使用原始坐标，不进行转换
        output_x = parsed_x
        output_y = parsed_y

        logger.debug(
            f"坐标处理: 原始({parsed_x:.3f}, {parsed_y:.3f}) -> "
            f"输出({output_x:.3f}, {output_y:.3f}) (无转换)"
        )

        return output_x, output_y

    def determine_state(self):
        """
        根据屏蔽时间确定状态值

        返回:
            int: 状态值 (0=正常, 1=屏蔽中)
        """
        if self.shield_time_ns is None:
            # 没有设置屏蔽时间，直接返回0 (正常状态)
            return 0

        if self.current_time_ns < self.shield_time_ns:
            # 当前时间小于屏蔽时间，返回1 (屏蔽状态)
            return 1
        else:
            # 屏蔽时间已过，返回0 (正常状态)
            return 0

    def on_localization_received(self, msg):
        """
        处理定位数据的主回调函数

        参数:
            msg (BinaryData): 包含protobuf序列化定位数据的消息
        """
        if self.stopped:
            logger.debug("处理器已停止，跳过定位数据处理")
            return

        logger.info(f"定位回调函数被调用, 消息大小: {len(msg.data)} bytes")

        # 解析protobuf消息
        try:
            localization_data = Localization()
            localization_data.ParseFromString(msg.data)
            logger.info("定位消息解析成功")

            # 【1】打印订阅得到的/localization/global_bag中所有的数据
            logger.info("=" * 60)
            logger.info("【原始定位数据】/localization/global_bag 接收到的数据:")

            # 提取原始位置数据
            raw_x = localization_data.position.x
            raw_y = localization_data.position.y
            raw_z = localization_data.position.z
            raw_yaw = localization_data.yaw
            raw_pitch = getattr(localization_data, "pitch", 0.0)
            raw_roll = getattr(localization_data, "roll", 0.0)

            # 提取时间戳信息
            timestamp_sec = (
                getattr(localization_data.header.stamp, "sec", 0)
                if hasattr(localization_data, "header")
                else 0
            )
            timestamp_nsec = (
                getattr(localization_data.header.stamp, "nsec", 0)
                if hasattr(localization_data, "header")
                else 0
            )
            frame_id = (
                getattr(localization_data.header, "frame_id", "N/A")
                if hasattr(localization_data, "header")
                else "N/A"
            )

            logger.info(
                f"定位头信息: frame_id={frame_id}, timestamp_sec={timestamp_sec}, timestamp_nsec={timestamp_nsec}"
            )
            logger.info(f"原始位置: x={raw_x:.6f}, y={raw_y:.6f}, z={raw_z:.6f}")
            logger.info(
                f"原始姿态: yaw={raw_yaw:.6f}rad({raw_yaw*180/3.14159:.2f}°), pitch={raw_pitch:.6f}rad, roll={raw_roll:.6f}rad"
            )

            # 如果有速度信息也打印
            if hasattr(localization_data, "velocity"):
                vel_x = getattr(localization_data.velocity, "x", 0.0)
                vel_y = getattr(localization_data.velocity, "y", 0.0)
                vel_z = getattr(localization_data.velocity, "z", 0.0)
                logger.info(f"原始速度: vx={vel_x:.3f}, vy={vel_y:.3f}, vz={vel_z:.3f}")

            logger.info(f"原始二进制数据长度: {len(msg.data)} bytes")
            logger.info("=" * 60)

            # 处理坐标（不进行转换）
            output_x, output_y = self.coordinate_transform(raw_x, raw_y)

            # 更新最新的定位数据
            self.latest_x = output_x
            self.latest_y = output_y
            self.latest_z = raw_z  # z坐标不进行转换
            self.latest_yaw = raw_yaw

            # 标记定位数据已初始化
            if not self.localization_initialized:
                self.localization_initialized = True
                logger.info(
                    f"定位数据初始化完成: x={self.latest_x:.3f}, "
                    f"y={self.latest_y:.3f}, z={self.latest_z:.3f}"
                )

            # 始终基于定位数据发布PDPControl消息
            # 状态值(state)会根据屏蔽时间动态调整
            self.publish_pdp_control_from_localization()

        except DecodeError as e:
            logger.error(f"解码 /localization/global_bag 消息失败: {e}")
        except Exception as e:
            logger.error(f"处理定位消息时发生错误: {e}")

    def publish_pdp_control_from_localization(self):
        """
        基于定位数据发布PDPControl消息到/lgsvl_pdp_control话题

        根据屏蔽时间设置状态值:
        - 屏蔽时间前: state=1 (屏蔽状态)
        - 屏蔽时间后: state=0 (正常状态)
        """
        try:
            # 创建PDPControl消息
            pdp_msg = PDPControl()

            # 设置位置和朝向
            pdp_msg.x = self.latest_x
            pdp_msg.y = self.latest_y
            pdp_msg.yaw = self.latest_yaw

            # 根据屏蔽时间确定状态
            pdp_msg.state = self.determine_state()

            # 【2】打印将要发布的/lgsvl_pdp_control的所有数据
            logger.info("=" * 60)
            logger.info("【处理后定位数据】/lgsvl_pdp_control 即将发布的数据:")
            logger.info(f"输出位置: x={pdp_msg.x:.6f}, y={pdp_msg.y:.6f}")
            logger.info(
                f"输出姿态: yaw={pdp_msg.yaw:.6f}rad({pdp_msg.yaw*180/3.14159:.2f}°)"
            )
            logger.info(
                f"状态: state={pdp_msg.state} ({'正常' if pdp_msg.state == 0 else '屏蔽中'})"
            )

            # 显示坐标处理信息
            logger.info(f"坐标处理详情: 直接使用原始坐标，无转换")
            logger.info(f"  处理方式: 实际x = 解析x, 实际y = 解析y")

            # 如果有屏蔽时间，显示时间状态
            if self.shield_time_ns is not None:
                logger.info(
                    f"时间状态: 当前={self.current_time_ns}ns, "
                    f"屏蔽时间={self.shield_time_ns}ns"
                )

            logger.info("=" * 60)

            # 发布消息
            self.pdp_control_pub.publish(pdp_msg)

            # 记录发布完成信息
            state_desc = "正常" if pdp_msg.state == 0 else "屏蔽中"
            logger.info(
                f"✓ 定位数据发布完成: x={pdp_msg.x:.3f}, y={pdp_msg.y:.3f}, "
                f"yaw={pdp_msg.yaw:.3f}, state={pdp_msg.state}({state_desc})"
            )

        except Exception as e:
            logger.error(f"发布PDPControl消息失败: {e}")

    def set_shield_time(self, shield_time_ns):
        """
        设置屏蔽时间

        参数:
            shield_time_ns (int): 新的屏蔽时间（纳秒）
        """
        old_shield_time = self.shield_time_ns
        self.shield_time_ns = shield_time_ns

        logger.info(f"屏蔽时间更新: {old_shield_time}ns -> {shield_time_ns}ns")

    def set_stop(self, stop=True):
        """
        控制处理器的启停状态

        参数:
            stop (bool): True=停止, False=启动
        """
        self.stopped = stop
        status = "已停止" if stop else "已启动"
        logger.info(f"定位处理器{status}")

    def get_latest_position(self):
        """
        获取最新的位置信息

        返回:
            dict: 包含最新位置信息的字典
        """
        return {
            "x": self.latest_x,
            "y": self.latest_y,
            "z": self.latest_z,
            "yaw": self.latest_yaw,
            "initialized": self.localization_initialized,
        }


def create_localization_processor(shield_time_ns=None):
    """
    工厂函数：创建定位处理器实例

    参数:
        shield_time_ns (int, optional): 屏蔽时间（纳秒）

    返回:
        LocalizationProcessor: 定位处理器实例
    """
    return LocalizationProcessor(shield_time_ns)


if __name__ == "__main__":
    """
    主函数：用于测试和独立运行
    """
    rospy.init_node("localization_processor_node", anonymous=True)

    # 创建定位处理器
    processor = create_localization_processor()

    logger.info("定位处理器节点启动，等待定位数据...")

    try:
        # 保持节点运行
        rospy.spin()
    except KeyboardInterrupt:
        logger.info("接收到中断信号，正在关闭...")
    except Exception as e:
        logger.error(f"节点运行时发生错误: {e}")
    finally:
        processor.set_stop(True)
        logger.info("定位处理器节点已关闭")
