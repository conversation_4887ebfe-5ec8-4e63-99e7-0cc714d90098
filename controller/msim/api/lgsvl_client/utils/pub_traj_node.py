#!/usr/bin/python3
# -*- coding: utf-8 -*-
import rospy
import time
import multiprocessing
from rosgraph_msgs.msg import Clock  # 新增时钟消息导入


# LGSVL 消息类型
from lgsvl_msgs.msg import RefTrajectory, RefPoint

from common.trajectory_pb2 import Trajectory

from autopilot_msgs.msg import BinaryData


def real_pos2unity(x, y, z):
    return (-y, z, x)


def get_vehicle_type():
    return "bus"


class TrajectoryProcessor:
    def __init__(self, shield_time_ns=None):
        # 初始化 ROS 节点
        rospy.init_node("trajectory_processor", anonymous=False)

        # 状态跟踪字典
        self.static_dict = {"last_on_trajectory_tm": 0}
        self.stopped = False
        self.planning_v = 0
        self.planning_a = 0
        self.shield_time_ns = shield_time_ns  # 存储屏蔽时间
        self.current_time_ns = 0  # 当前时间（纳秒）

        # 创建轨迹发布器
        self.lgsvl_trajectory = rospy.Publisher(
            "/lgsvl_trajector", RefTrajectory, queue_size=1
        )

        # 订阅规划轨迹主题
        rospy.Subscriber("/planning/trajectory", BinaryData, self.on_trajectory)

        # 订阅时钟主题
        rospy.Subscriber("/clock", Clock, self.clock_callback)

        print("Trajectory processor initialized")
        self.process = None  # 新增：用于保存进程对象

    def control_lon_data(self, msg):
        """更新公交车的规划速度和加速度"""

        self.planning_a = msg.ref_point_accel
        self.planning_v = msg.ref_point_speed
        print(
            "Updated bus planning state: v={}, a={}", self.planning_v, self.planning_a
        )

    def clock_callback(self, msg):
        """处理时钟回调，更新当前时间"""
        # 将时间转换为纳秒 (秒部分 * 1e9 + 纳秒部分)
        self.current_time_ns = msg.clock.secs * int(1e9) + msg.clock.nsecs

    def on_trajectory(self, msg):
        """处理规划轨迹的主回调函数"""
        if self.stopped:
            return

        # 检查屏蔽时间：如果当前时间小于屏蔽时间，则不发布
        if (
            self.shield_time_ns is not None
            and self.current_time_ns < self.shield_time_ns
        ):
            print(
                f"等待屏蔽时间: {self.shield_time_ns}ns, 当前时间: {self.current_time_ns}ns"
            )
            return

        # 频率控制：每0.3秒最多处理一次
        last_tm = self.static_dict.get("last_on_trajectory_tm", 0)
        if time.time() - last_tm <= 0.3:
            return

        # 解析二进制消息
        trajectory = Trajectory()
        trajectory.ParseFromString(msg.data)

        # 创建LGSVL兼容的轨迹消息
        ref_traj = RefTrajectory()
        ref_traj.header.stamp = rospy.Time.now()
        ref_traj.header.frame_id = "base_link"

        ref_traj.horn = 0

        # 处理轨迹点 - 移除采样，处理所有轨迹点
        b_get_planning = False
        print(f"\n=== 开始处理轨迹，共 {len(trajectory.points)} 个轨迹点 ===")

        for index, pt in enumerate(trajectory.points):
            # 处理所有轨迹点，不进行采样
            traj_pt = RefPoint()
            # 坐标转换：真实坐标 → Unity坐标
            original_coords = (pt.x, pt.y, pt.z)
            traj_pt.x, traj_pt.y, traj_pt.z = real_pos2unity(pt.x, pt.y, pt.z)
            ref_traj.trajectory.append(traj_pt)

            # 打印详细的轨迹点信息
            print(
                f"轨迹点 {index+1:3d}: "
                f"原始坐标({pt.x:8.3f}, {pt.y:8.3f}, {pt.z:6.3f}) → "
                f"Unity坐标({traj_pt.x:8.3f}, {traj_pt.y:8.3f}, {traj_pt.z:6.3f}) | "
                f"速度:{getattr(pt, 'v', 0.0):6.2f} | 加速度:{getattr(pt, 'a', 0.0):6.2f} | "
                f"航向角:{getattr(pt, 'theta', 0.0):6.3f} | 曲率:{getattr(pt, 'kappa', 0.0):8.5f} | "
                f"弧长:{getattr(pt, 's', 0.0):7.2f}"
            )

            # 提取速度和加速度（非公交车）
            if (
                get_vehicle_type() != "bus"
                and not b_get_planning
                and getattr(pt, "a", 0.0) > 0
            ):
                ref_traj.planningVel = getattr(pt, "v", 0.0)
                ref_traj.planningAcc = getattr(pt, "a", 0.0)
                b_get_planning = True
                print(
                    f"    └── 提取到规划速度: {ref_traj.planningVel:.2f}, 规划加速度: {ref_traj.planningAcc:.2f}"
                )

        print(f"=== 轨迹处理完成，共处理 {len(ref_traj.trajectory)} 个轨迹点 ===")

        # 公交车使用独立的速度/加速度源
        if get_vehicle_type() == "bus":
            ref_traj.planningVel = self.planning_v
            ref_traj.planningAcc = self.planning_a

        # 发布处理后的轨迹
        self.lgsvl_trajectory.publish(ref_traj)
        self.static_dict["last_on_trajectory_tm"] = time.time()

        # 详细的轨迹发布信息
        print(f"\n🚀 轨迹发布完成:")
        print(f"   ├── 发布的轨迹点数量: {len(ref_traj.trajectory)}")
        print(f"   ├── 规划速度: {ref_traj.planningVel:.2f} m/s")
        print(f"   ├── 规划加速度: {ref_traj.planningAcc:.2f} m/s²")
        print(f"   ├── 喇叭状态: {ref_traj.horn}")
        print(f"   ├── 车辆类型: {get_vehicle_type()}")
        print(f"   └── 发布话题: /lgsvl_trajector")
        print("-" * 60)

    def set_stop(self, stop=True):
        """控制处理器的启停状态"""
        self.stopped = stop
        print(f"Trajectory processor {'stopped' if stop else 'started'}")

    def run(self):
        """启动处理器"""
        rospy.spin()

    def start(self):
        """
        启动轨迹处理进程
        """
        if self.process is not None and self.process.is_alive():
            print("TrajectoryProcessor 进程已在运行。")
            return
        self.process = multiprocessing.Process(target=self.run)
        self.process.start()
        print(f"TrajectoryProcessor 进程已启动，PID={self.process.pid}")

    def stop(self):
        """
        停止轨迹处理进程
        """
        if self.process is not None and self.process.is_alive():
            print(f"尝试终止 TrajectoryProcessor 进程，PID={self.process.pid}")
            self.process.terminate()
            self.process.join(timeout=5)
            if self.process.is_alive():
                print("TrajectoryProcessor 进程强制终止失败！")
            else:
                print("TrajectoryProcessor 进程已被终止。")
            self.process = None
        else:
            print("TrajectoryProcessor 进程未在运行，无需终止。")


if __name__ == "__main__":
    processor = TrajectoryProcessor()
    try:
        processor.start()
    except rospy.ROSInterruptException:
        processor.stop()
        print("Trajectory processor shutdown")
