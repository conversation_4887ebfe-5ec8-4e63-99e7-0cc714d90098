# -*- coding: UTF-8 -*-
#  Copyright (C) Mogo.ai Information and Technology Co.Ltd 
#
#  All rights reserved.
#
#  This document contains proprietary information belonging to Mogo.ai Co.Ltd.
#  Passing on and copying of this document, and communication of its contents
#  is not permitted without prior written authorization.
#
#
#  <AUTHOR>
#  @date 10/11/23, 3:52 PM

from api.reader.reader_base import ReaderBase
from utils.mcap_tools.reader import read_mcap_messages, get_summary


class McapReader(ReaderBase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def get_summary(self, file_path):
        return get_summary(file_path)



