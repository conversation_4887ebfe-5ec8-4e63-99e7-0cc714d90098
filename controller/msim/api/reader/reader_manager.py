#  Copyright (C) Mogo.ai Information and Technology Co.Ltd 
#
#  All rights reserved.
#
#  This document contains proprietary information belonging to Mogo.ai Co.Ltd.
#  Passing on and copying of this document, and communication of its contents
#  is not permitted without prior written authorization.
#
#
#  <AUTHOR>
#  @date 10/11/23, 3:51 PM
import json
from os import PathLike
from pathlib import Path
from typing import Tuple, Optional, Type, Iterator, Any, Iterable, Union, IO

from api.reader.reader_base import ReaderBase
from models.task_data import TaskData

from utils.path import get_record_path
from mcap.reader import Summary


class ReaderManager:
    def __init__(self, task_data: TaskData):
        self._task_data = task_data
        self._reader_instance: Optional[ReaderBase] = None

    @staticmethod
    def get_reader_class():
        try:
            from api.reader.impl.mcap_reader import Mcap<PERSON>eader
            return McapReader
        except ImportError:
            return None

    @property
    def reader_instance(self):
        if self._reader_instance is None:
            reader_class = self.get_reader_class()
            if reader_class is None:
                raise ImportError("reader class not supported.")
            self._reader_instance = reader_class()

        return self._reader_instance

    def get_summary(self, file_path: str) -> Summary:
        return self.reader_instance.get_summary(file_path)

    def iter_messages(
        self,
        source: Union[str, bytes, "PathLike[str]", IO[bytes]],
        topics: Optional[Iterable[str]] = None,
        start_time: Optional[int] = None,
        end_time: Optional[int] = None,
        log_time_order: bool = True,
        reverse: bool = False,
    ) -> Iterator[Tuple[str, Any, int]]:
        return self.reader_instance.iter_messages(source, topics, start_time, end_time, log_time_order, reverse)

    def get_record_file(self) -> str:
        record_path = Path(get_record_path(self._task_data.task_id))
        mcap_files = list(record_path.rglob("*.mcap"))
        if len(mcap_files) != 1:
            raise FileNotFoundError("No record file or more than one file found.")
        return str(mcap_files[0])

    def get_record_summary(self) -> Summary:
        return self.reader_instance.get_summary(self.get_record_file())

    def iter_recorder_messages(
        self,
        topics: Optional[Iterable[str]] = None,
        start_time: Optional[int] = None,
        end_time: Optional[int] = None,
        log_time_order: bool = True,
        reverse: bool = False,
    ) -> Iterator[Tuple[str, Any, int]]:

        return self.reader_instance.iter_messages(self.get_record_file(), topics, start_time, end_time, log_time_order, reverse)
