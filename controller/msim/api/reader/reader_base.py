#  Copyright (C) Mogo.ai Information and Technology Co.Ltd 
#
#  All rights reserved.
#
#  This document contains proprietary information belonging to Mogo.ai Co.Ltd.
#  Passing on and copying of this document, and communication of its contents
#  is not permitted without prior written authorization.
#
#
from abc import abstractmethod
from os import PathLike
from typing import Optional, Iterable, Tuple, Iterator, Any, Union, IO

from mcap.reader import Summary


class ReaderBase:
    def __init__(self, *args, **kwargs):
        ...

    @abstractmethod
    def get_summary(self, file_path) -> Summary:
        raise NotImplementedError

    @abstractmethod
    def iter_messages(
        self,
        source: Union[str, bytes, "PathLike[str]", IO[bytes]],
        topics: Optional[Iterable[str]] = None,
        start_time: Optional[int] = None,
        end_time: Optional[int] = None,
        log_time_order: bool = True,
        reverse: bool = False,
    ) -> Iterator[Tuple[str, Any, int]]:
        """iterates through the messages in an MCAP.
        :param source: the source of the MCAP file. This can be a file path, a file-like object, or
            a McapReader instance.
        :param topics: if not None, only messages from these topics will be returned.
        :param start_time: an integer nanosecond timestamp. if provided, messages logged before this
            timestamp are not included.
        :param end_time: an integer nanosecond timestamp. if provided, messages logged after this
            timestamp are not included.
        :param log_time_order: if True, messages will be yielded in ascending log time order. If
            False, messages will be yielded in the order they appear in the MCAP file.
        :param reverse: if both ``log_time_order`` and ``reverse`` are True, messages will be
            yielded in descending log time order.
        """
        raise NotImplementedError()

    def __del__(self):
        pass
