#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
"""ROS1 数据流控制播放器

提供基于流程控制的 ROS 数据包回放功能，支持时间控制、话题重映射和数据记录等特性。
"""

import os
import argparse
import time
from api.player.tools.flow_control_player import FlowControlPlayer
from utils.loggers import LoggerProxy

logger = LoggerProxy("player")


def main():
    """ROS1 数据流控制播放器的主入口函数

    功能特性：
    - 支持配置文件自定义播放行为
    - 可控制播放时间段和时钟频率
    - 支持话题重映射
    - 可选择性记录输出数据
    """
    parser = argparse.ArgumentParser(description="基于流程控制的 ROS 数据播放器")
    parser.add_argument("--config", type=str, required=True, help="配置文件路径")
    parser.add_argument("--bag", type=str, required=True, help="ROS bag 文件路径")
    parser.add_argument("--clock_freq", type=float, default=0.0, help="时钟频率(Hz)")
    parser.add_argument("--start_time", type=int, default=0, help="起始时间偏移(秒)")
    parser.add_argument("--end_time", type=int, default=0, help="结束时间偏移(秒)")
    parser.add_argument(
        "--remap",
        type=str,
        nargs="*",
        default=[],
        help="话题重映射，格式: 原话题:=新话题",
    )
    parser.add_argument("--record_path", type=str, help="数据记录输出路径(可选)")
    parser.add_argument("--start-roscore", action="store_true", help="按需启动 roscore")

    args = parser.parse_args()

    roscore_mgr = None

    try:
        # 文件存在性校验
        if not os.path.exists(args.config):
            raise FileNotFoundError(f"未找到配置文件: {args.config}")
        if not os.path.exists(args.bag):
            raise FileNotFoundError(f"未找到数据包文件: {args.bag}")

        logger.info(f"正在加载配置文件: {args.config}")
        logger.info(f"正在加载Bag: {args.bag}")

        # 按需初始化 roscore
        if args.start_roscore:
            logger.info("正在初始化 roscore...")
            from utils.compatibility.ros1.roscore_manager import RoscoreManager

            roscore_mgr = RoscoreManager()
            if not roscore_mgr.start():
                raise RuntimeError("roscore 初始化失败")

        # 初始化并运行播放器
        with FlowControlPlayer(
            config_path=args.config,
            bag_path=args.bag,
            clock_freq=args.clock_freq,
            remap=args.remap,
            start_time=args.start_time,
            end_time=args.end_time,
            record_path=args.record_path,
        ) as player:
            # 上下文管理器负责播放器的启动和关闭
            # 等待播放完成
            try:
                while not player._shutdown.is_set():
                    time.sleep(0.1)
            except KeyboardInterrupt:
                logger.info("用户终止播放")

        logger.info("播放已完成")

    except Exception as e:
        logger.error(f"播放器运行异常: {e}", exc_info=True)
        return 1
    finally:
        # 确保 roscore 正确清理
        if roscore_mgr is not None:
            logger.info("正在关闭 roscore...")
            roscore_mgr.stop()

    return 0


if __name__ == "__main__":
    exit(main())
