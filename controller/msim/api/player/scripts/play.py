#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
#  Copyright (C) Mogo.ai Information and Technology Co.Ltd 
#
#  All rights reserved.
#
#  This document contains proprietary information belonging to Mogo.ai Co.Ltd.
#  Passing on and copying of this document, and communication of its contents
#  is not permitted without prior written authorization.
#

import argparse
import os
import signal
from argparse import ArgumentTypeError
from pathlib import Path
from typing import Any

from api.player.tools.player import McapPlayer


def check_mcap_exists(value: Any) -> str:
    """Argparse validator to verify a path exists."""
    try:
        if os.path.exists(value):
            if Path(value).suffix == '.mcap':
                return value
            raise ArgumentTypeError("File '{}' is not an mcap file!".format(value))
        raise ArgumentTypeError("Bag file '{}' does not exist!".format(value))
    except ValueError:
        raise ArgumentTypeError('{} is not the valid type (string)'.format(value))


def positive_float(arg: str) -> float:
    value = float(arg)
    if value <= 0:
        raise ValueError(f'Value {value} is less than or equal to zero.')
    return value


parser = argparse.ArgumentParser(description='mcap player.')

parser.add_argument(
    'bag_file', type=check_mcap_exists, help='bag file to replay')
parser.add_argument(
    '--topics', type=str, default=[], nargs='+',
    help='topics to replay, separated by space. If none specified, all topics will be '
         'replayed.')
parser.add_argument(
    '--remap', '-m', default='', nargs='+',
    help='list of topics to be remapped: in the form '
         '"old_topic1:=new_topic1 old_topic2:=new_topic2 etc." ')
parser.add_argument(
    '--clock', type=positive_float, nargs='?', const=40, default=0,
    help='Publish to /clock at a specific frequency in Hz, to act as a ROS Time Source. '
         'Value must be positive. Defaults to not publishing.')

parser.add_argument(
    '-r', '--rate', type=positive_float, default=1.0,
    help='rate at which to play back messages. Valid range > 0.0.')

parser.add_argument(
    '--start-offset', type=positive_float, default=0.0,
    help='Start the playback player this many seconds into the bag file.')

parser.add_argument(
            '--qos-profile-overrides-path', type=argparse.FileType('r'),
            help='Path to a yaml file defining overrides of the QoS profile for specific topics.')

parser.add_argument(
            '--flow-control-config-path', type=argparse.FileType('r'),
            help='Path to a json file defining flow control items.')

parser.add_argument(
            '--heartbeat-nodes', type=str, default=[], nargs='+',
            help='a list for node names that need to be monitored for heartbeats.')

parser.add_argument(
    '--shm-topics', type=str, default=[], nargs='+',
    help='a list for node names that need to pub shm messages.')


def main(args=None):
    player = McapPlayer(args.bag_file, args.topics, args.clock, args.remap,
                        args.rate, args.start_offset, args.qos_profile_overrides_path, args.flow_control_config_path,
                        args.heartbeat_nodes, args.shm_topics)

    def _signal_handler(sig, frame):
        try:
            print(f"Signal: {sig}, stop player")
            player.stop()
        except Exception as e:
            print(f"signal handle error: {str(e)}")

    signal.signal(signal.SIGINT, _signal_handler)
    signal.signal(signal.SIGTERM, _signal_handler)

    play_only(player)


def play_only(player: McapPlayer):
    try:
        player.start(block=True)
    except KeyboardInterrupt:
        print("KeyboardInterrupt")
        player.stop()


if __name__ == '__main__':
    args = parser.parse_args()
    main(args)

