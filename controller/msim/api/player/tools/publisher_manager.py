#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
"""ROS 发布者管理器模块，同时支持 ROS1 和 ROS2"""

from typing import Dict, List, Optional, Union, Type, Any
import traceback
import importlib
import copy

from utils.compatibility.node_factory import (
    NodeFactory,
    RunEnvType,
    WriterBase,
    TimeWriter,
)
from utils.loggers import LoggerProxy

logger = LoggerProxy("player")
from utils.compatibility.ros_env_detector import (
    is_ros1_available,
    is_ros2_available,
    get_ros_version,
    get_message_class_ros2,
)

CLOCK_TOPIC = "/clock"


class PublisherManager:
    """ROS 发布者管理器，同时支持 ROS1 和 ROS2。

    负责ROS节点生命周期管理、发布者的创建和管理、实际消息发布操作。

    参数:
        pub_topics: 要发布的话题列表
        remap: 话题重映射列表
        clock_freq: 时钟频率
        ros_version: 指定使用的ROS版本，可选值为 1 或 2，默认自动检测
    """

    def __init__(
        self,
        pub_topics: Optional[List[dict]] = None,
        remap: Optional[List[str]] = None,
        clock_freq: float = 0.0,
        ros_version: Optional[str] = None,
    ):
        self._pub_topics = pub_topics or []
        self._remap = remap or []
        self._clock_freq = clock_freq
        self._shielded_topics: set[str] = set()

        # 确定 ROS 版本
        self._ros_version = ros_version
        if self._ros_version is None:
            ros_version_str = get_ros_version()
            # 将字符串版本转换为整数
            if ros_version_str == "ros1":
                self._ros_version = "ros1"
            elif ros_version_str == "ros2":
                self._ros_version = "ros2"
            elif ros_version_str == "both":
                # 当两个版本都可用时，优先使用 ROS2
                self._ros_version = "ros2"
                logger.info("检测到 ROS1 和 ROS2 都可用，优先使用 ROS2")
            else:
                # 如果无法自动检测，默认使用 ROS1
                logger.warning("无法自动检测 ROS 版本，默认使用 ROS1")
                self._ros_version = "ros1"

        self._remap_topics = {}
        if self._remap:
            self._remap_topics = dict(map(lambda x: x.split(":="), self._remap))

        self._writer_map: Dict[str, List[Union[WriterBase, TimeWriter]]] = {}
        self._node = None
        self._setup()

    def _setup(self):
        """初始化发布者管理器。"""
        self._init_node()
        self._create_publisher()

    def _init_node(self):
        """初始化ROS节点。"""
        if self._ros_version == "ros1":
            run_env = RunEnvType.ROS1
            node_name = "ros1_player"
        else:
            run_env = RunEnvType.ROS2
            node_name = "ros2_player"

        params = {
            "name": node_name,
            "remap": self._remap_topics,
        }

        # 为 ROS2 添加额外参数
        if self._ros_version == "ros2":
            try:
                # 导入 ROS2 的 Parameter 类
                from rclpy.parameter import Parameter

                params["parameter_overrides"] = [
                    Parameter(name="use_sim_time", value=True)
                ]
            except ImportError:
                logger.warning("无法导入 rclpy.parameter.Parameter，跳过参数设置")

        self._node = NodeFactory().get_node(run_env=run_env, params=params)

    def _create_publisher(self):
        """创建所有话题的发布者。"""
        self._writer_map.clear()

        for pub_topic in self._pub_topics:
            topic_name = pub_topic.get("remap", pub_topic["name"])
            original_topic_name = pub_topic["name"]

            try:
                if self._ros_version == "ros1":
                    # ROS1 方式获取消息类型
                    import rospy
                    from roslib.message import get_message_class

                    msg_type = get_message_class(pub_topic["type"])
                else:
                    # ROS2 方式获取消息类型
                    msg_type = get_message_class_ros2(pub_topic["type"])

                if msg_type is None:
                    raise ImportError(f"未找到消息类型: {pub_topic['type']}")
            except Exception as e:
                logger.error(str(e))
                continue

            logger.info(f"创建发布者: {topic_name}, {msg_type}")
            writer = self._node.create_writer(topic_name, msg_type)
            writer.topic_name = topic_name  # 存储最终的话题名

            if original_topic_name not in self._writer_map:
                self._writer_map[original_topic_name] = []
            self._writer_map[original_topic_name].append(writer)

        if self._clock_freq:
            self._writer_map[CLOCK_TOPIC] = [self._node.create_time_writer(CLOCK_TOPIC)]

    def set_shielded_topics(self, topics: List[str]):
        """设置需要屏蔽的话题列表。"""
        self._shielded_topics = set(topics)
        logger.info(f"PublisherManager: 屏蔽列表已更新为: {self._shielded_topics}")

    def publish(self, topic: str, msg):
        """发布消息到指定话题。"""
        if topic not in self._writer_map:
            logger.error(f"未找到话题 {topic} 的发布者")
            return False

        all_published = True
        for writer in self._writer_map.get(topic, []):
            # 检查每个发布者的具体话题名是否被屏蔽
            if (
                hasattr(writer, "topic_name")
                and writer.topic_name in self._shielded_topics
            ):
                logger.debug(f"话题 {writer.topic_name} 当前被屏蔽，跳过发布。")
                continue

            try:
                # 为每个发布者创建消息的深拷贝，避免消息对象被修改影响其他发布者
                msg_copy = copy.deepcopy(msg)
                writer.write(msg_copy)
            except Exception as e:
                logger.error(
                    f"发布消息到话题 {getattr(writer, 'topic_name', topic)} 的某个发布者时失败: {e}"
                )
                logger.error(traceback.format_exc())
                all_published = False
        return all_published

    def publish_clock(self, secs: int, nsecs: int):
        """发布时钟消息。"""
        if CLOCK_TOPIC not in self._writer_map:
            return False

        try:
            # 时钟发布者是列表中的第一个也是唯一一个
            self._writer_map[CLOCK_TOPIC][0].write(secs, nsecs)
            return True
        except Exception as e:
            logger.error(f"发布时钟消息失败: {e}")
            return False

    def destroy(self):
        """清理资源。"""
        if self._node:
            self._node.destroy()
            self._node = None

    def stop(self):
        """清理资源（ROS2 兼容方法）。"""
        self.destroy()
