#!/usr/bin/env python3
"""
ROS1 bag 文件读取工具。

该模块提供了读取 ROS1 bag 文件的功能，支持按时间范围和话题过滤消息。
"""

from datetime import datetime
from io import BytesIO, StringIO
from os import PathLike
from typing import IO, Iterable, Iterator, Optional, Tuple, Union, Any, Dict, Type
import traceback
import rosbag
import rospy

import traceback

from utils.compatibility.ros_env_detector import get_message_class_ros2
from utils.loggers import LoggerProxy

logger = LoggerProxy("player")

# 定义常量，放在模块开头
CHUNK_THRESHOLD = 100 * 1024 * 1024  # 100MB

import sys

sys.path.append("/home/<USER>/simulation/mogosim_engine/controller/common")

# 话题到消息类型的映射配置
TOPIC_MESSAGE_MAPPING = {
    "/perception/camera/camera_obstacle_front60": "common.object_pb2/TrackedObjects",
    "/perception/fusion_mid_inno/lidar_obstacle": "common.object_pb2/TrackedObjects",
    "/perception/fusion/obstacles": "common.object_pb2/TrackedObjects",
}


def read_bag_messages(
    source: Union[str, PathLike],
    topics: Optional[Iterable[str]] = None,
    start_time: Optional[Union[int, datetime]] = None,
    end_time: Optional[Union[int, datetime]] = None,
) -> Iterator[Tuple[str, rospy.Time, Any]]:
    """
    读取 ROS bag 文件中的消息。

    参数:
        source: bag 文件路径
        topics: 要读取的话题列表，如果为 None 则读取所有话题
        start_time: 开始时间，可以是 datetime 对象或纳秒时间戳
        end_time: 结束时间，可以是 datetime 对象或纳秒时间戳

    返回:
        Iterator[Tuple[str, rospy.Time, Any]]: 返回一个迭代器，每个元素为 (话题名, 时间戳, 消息)

    异常:
        rosbag.ROSBagException: 当 bag 文件读取失败时抛出
        ValueError: 当时间参数格式不正确时抛出
    """
    # 转换时间格式
    if start_time is not None and isinstance(start_time, datetime):
        start_time = int(start_time.timestamp() * 1e9)
    if end_time is not None and isinstance(end_time, datetime):
        end_time = int(end_time.timestamp() * 1e9)

    # 如果传入 0 则视为未设置（与 ROS2 版本一致的行为）
    if start_time == 0:
        start_time = None
    if end_time == 0:
        end_time = None

    try:
        bag = rosbag.Bag(source, chunk_threshold=CHUNK_THRESHOLD)

        for topic, msg, t in bag.read_messages(topics=topics):
            try:
                msg_timestamp = _extract_message_timestamp(topic, msg)

                # 无法提取时间戳
                if msg_timestamp is None:
                    logger.warning(
                        f"无法从话题 '{topic}' 的消息中提取时间戳，跳过该消息"
                    )
                    continue

                # 过滤掉无效时间戳（<=0）
                if msg_timestamp.to_sec() <= 0:
                    logger.warning(
                        f"话题 '{topic}' 的消息时间戳无效（{msg_timestamp}），跳过该消息"
                    )
                    continue

                # 将 rospy.Time 转为纳秒整数，便于与 start/end_time 比较
                msg_timestamp_ns = (
                    msg_timestamp.secs * 1_000_000_000 + msg_timestamp.nsecs
                )

                # 开始时间过滤
                if start_time is not None and msg_timestamp_ns < start_time:
                    logger.info(
                        f"开始时间: {start_time}, 跳过在开始时间之前的消息: {topic}, {msg_timestamp_ns}"
                    )
                    continue

                # 结束时间过滤
                if end_time is not None and msg_timestamp_ns > end_time:
                    logger.info(
                        f"结束时间: {end_time}, 跳过在结束时间之后的消息: {topic}, {msg_timestamp_ns}"
                    )
                    continue

                # 通过过滤，返回消息
                yield topic, msg_timestamp, msg
            except Exception as e:
                logger.error(f"处理话题 '{topic}' 的消息时发生错误: {e}")
                logger.error(traceback.format_exc())
                continue
    except rosbag.ROSBagException as e:
        logger.error(f"读取 bag 文件失败: {e}")
        raise
    finally:
        bag.close()


def _extract_message_timestamp(topic: str, msg: Any) -> Optional[rospy.Time]:
    """
    从消息中提取时间戳。

    参数:
        topic: 话题名
        msg: ROS 消息对象

    返回:
        Optional[rospy.Time]: 提取的时间戳，如果无法提取则返回 None
    """
    # 首先检查是否是标准的header.stamp格式
    if hasattr(msg, "header") and hasattr(msg.header, "stamp"):
        # 检查时间戳是否有效（秒数必须大于0）
        if msg.header.stamp.secs > 0:
            return rospy.Time(secs=msg.header.stamp.secs, nsecs=msg.header.stamp.nsecs)
        else:
            logger.warning(
                f"话题 '{topic}' 的header.stamp时间戳无效（秒数: {msg.header.stamp.secs}）"
            )
            return None

    # 特殊处理/tf话题的tf2_msgs/TFMessage格式
    if topic == "/tf" and hasattr(msg, "transforms") and len(msg.transforms) > 0:
        # 使用第一个transform的header.stamp
        first_transform = msg.transforms[0]
        if hasattr(first_transform, "header") and hasattr(
            first_transform.header, "stamp"
        ):
            # 检查时间戳是否有效
            if first_transform.header.stamp.secs > 0:
                return rospy.Time(
                    secs=first_transform.header.stamp.secs,
                    nsecs=first_transform.header.stamp.nsecs,
                )
            else:
                logger.warning(
                    f"话题 '{topic}' 的transform时间戳无效（秒数: {first_transform.header.stamp.secs}）"
                )
                return None
        else:
            logger.warning(f"话题 '{topic}' 的第一个transform没有header.stamp字段")
            return None

    # 特殊处理MarkerArray类型的可视化消息
    if hasattr(msg, "markers") and len(msg.markers) > 0:
        # MarkerArray包含多个Marker，尝试从第一个Marker获取时间戳
        first_marker = msg.markers[0]
        if hasattr(first_marker, "header") and hasattr(first_marker.header, "stamp"):
            # 检查时间戳是否有效
            if first_marker.header.stamp.secs > 0:
                return rospy.Time(
                    secs=first_marker.header.stamp.secs,
                    nsecs=first_marker.header.stamp.nsecs,
                )
            else:
                logger.warning(
                    f"话题 '{topic}' 的MarkerArray时间戳无效（秒数: {first_marker.header.stamp.secs}）"
                )
                return None
        else:
            logger.warning(
                f"话题 '{topic}' 的MarkerArray中第一个Marker没有header.stamp字段"
            )
            return None

    # 检查是否是包含data字段的protobuf消息（如BinaryData）
    if not hasattr(msg, "data"):
        logger.warning(
            f"话题 '{topic}' 的消息类型 '{type(msg).__name__}' 不包含data字段，且不是已知的ROS原生消息类型"
        )
        return None

    # 对于包含protobuf数据的消息，尝试使用protobuf消息类型解析
    msg_class = get_message_class_ros2(
        TOPIC_MESSAGE_MAPPING.get(topic, "common.object_pb2/TrackedObjects")
    )
    if msg_class is None:
        logger.info(f"msg_class not exist: {msg_class}")
        return None

    try:
        # 创建消息实例
        msg_obj = msg_class()
        msg_obj.ParseFromString(msg.data)

        # 检查protobuf消息的时间戳是否有效
        if msg_obj.header.stamp.sec > 0:
            return rospy.Time(
                secs=msg_obj.header.stamp.sec, nsecs=msg_obj.header.stamp.nsec
            )
        else:
            logger.warning(
                f"话题 '{topic}' 的protobuf消息时间戳无效（秒数: {msg_obj.header.stamp.sec}）"
            )
            return None
    except Exception as e:
        logger.error(f"解析消息失败: {e}")
        logger.error(traceback.format_exc())
        return None


if __name__ == "__main__":
    for topic, msg_time, msg in read_bag_messages(
        "/home/<USER>/simulation/bag/20241105104612-3562118400-rosmaster-010621_2.bag",
        topics=[
            "/perception/fusion_mid_inno/lidar_obstacle",
            "/perception/camera/camera_obstacle_front60",
            "/perception/fusion/obstacles",
        ],
    ):
        print(f"Topic: {topic}, Time: {msg_time}")
        # break
