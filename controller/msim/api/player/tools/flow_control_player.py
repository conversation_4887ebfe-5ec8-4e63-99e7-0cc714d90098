#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
"""基于流程控制的ROS数据播放器模块，支持ROS1和ROS2环境"""

import os
import threading
import time
import yaml
import signal
from typing import List, Optional, Dict, Any, Type, Union
from abc import ABC, abstractmethod

from api.player.tools.bag_player import FramePlayer
from utils.loggers import LoggerProxy

logger = LoggerProxy("player")
from utils.compatibility.ros_env_detector import (
    get_ros_version,
    is_ros1_available,
    is_ros2_available,
)
from utils.compatibility.node_factory import NodeFactory, RunEnvType

# 根据ROS环境动态导入
ros_version = get_ros_version()
if ros_version == "ros1" or ros_version == "both":
    import rospy
    from roslib.message import get_message_class
    from api.recorder.impl.ros1_recorder import ROS1Recorder
elif ros_version == "ros2":
    from api.recorder.impl.ros2_recorder import ROS2Recorder, TopicConfig
else:
    logger.error("未检测到ROS环境，无法使用FlowControlPlayer")
    raise ImportError("未检测到ROS环境，请确保已安装ROS1或ROS2")


class FlowControlPlayerFactory:
    """流程控制播放器工厂类，根据ROS环境创建适当的播放器实例。"""

    @staticmethod
    def create_player(
        config_path: str,
        bag_path: str,
        clock_freq: float = 0.0,
        remap: Optional[List[str]] = None,
        start_time: float = 0.0,
        end_time: float = 0.0,
        record_path: Optional[str] = None,
        ros_version: Optional[str] = None,
    ) -> "FlowControlPlayer":
        """
        创建流程控制播放器实例。

        参数:
            config_path (str): 配置文件路径
            bag_path (str): 数据包路径
            clock_freq (float, optional): 时钟频率，默认为 0.0
            remap (Optional[List[str]], optional): 话题重映射列表
            start_time (float, optional): 开始时间偏移，默认为 0.0
            end_time (float, optional): 结束时间偏移，默认为 0.0
            record_path (Optional[str], optional): 录制数据包路径，默认为 None
            ros_version (Optional[str], optional): 指定ROS版本，如果为None则自动检测

        返回:
            FlowControlPlayer: 流程控制播放器实例
        """
        # 如果未指定ROS版本，则自动检测
        if ros_version is None:
            ros_version = get_ros_version()

        # 创建适当的播放器实例
        if ros_version == "ros1" or ros_version == "both":
            logger.info("检测到ROS1环境，创建ROS1播放器")
            return FlowControlPlayer(
                config_path=config_path,
                bag_path=bag_path,
                clock_freq=clock_freq,
                remap=remap,
                start_time=start_time,
                end_time=end_time,
                record_path=record_path,
                ros_version="ros1",
            )
        elif ros_version == "ros2":
            logger.info("检测到ROS2环境，创建ROS2播放器")
            return FlowControlPlayer(
                config_path=config_path,
                bag_path=bag_path,
                clock_freq=clock_freq,
                remap=remap,
                start_time=start_time,
                end_time=end_time,
                record_path=record_path,
                ros_version="ros2",
            )
        else:
            raise ValueError(f"不支持的ROS版本: {ros_version}")


class FlowControlPlayer:
    """基于流程控制的ROS数据播放器，支持ROS1和ROS2环境。

    根据配置文件控制数据播放流程，支持以下功能：
    1. 按照主话题分帧播放数据
    2. 等待反馈话题消息后再播放下一帧
    3. 录制指定话题的数据

    参数:
        config_path (str): 配置文件路径
        bag_path (str): 数据包路径
        clock_freq (float, optional): 时钟频率，默认为 0.0
        remap (Optional[List[str]], optional): 话题重映射列表
        start_time (float, optional): 开始时间偏移，默认为 0.0
        end_time (float, optional): 结束时间偏移，默认为 0.0
        record_path (Optional[str], optional): 录制数据包路径，默认为 None
        ros_version (str, optional): ROS版本，默认为自动检测
        block_on_enter (bool, optional): 是否在进入上下文时阻塞，默认为 False
    """

    def __init__(
        self,
        config_path: str,
        bag_path: str,
        clock_freq: float = 0.0,
        remap: Optional[List[str]] = None,
        start_time: float = 0.0,
        end_time: float = 0.0,
        record_path: Optional[str] = None,
        ros_version: str = "auto",
        block_on_enter: bool = False,
        shield_time_ns: Optional[int] = None,
    ):
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"配置文件不存在: {config_path}")

        if not os.path.exists(bag_path):
            raise FileNotFoundError(f"数据包文件不存在: {bag_path}")

        # 确定ROS版本
        if ros_version == "auto":
            self.ros_version = get_ros_version()
            if self.ros_version == "both":
                self.ros_version = "ros1"  # 默认使用ROS1
        else:
            self.ros_version = ros_version

        logger.info(f"使用ROS版本: {self.ros_version}")

        # 加载配置文件
        with open(config_path, "r") as f:
            self.config = yaml.safe_load(f)

        # 提取配置信息
        self.timeout = self.config.get("timeout", 30)
        self.timeout_cnt_threshold = self.config.get("timeout_cnt_threshold", None)
        # 超时次数阈值
        self.main_topic = self.config.get("main_topic", {}).get("name")
        self.main_topic_type = self.config.get("main_topic", {}).get("type")

        # 屏蔽配置：topics 从配置文件读取
        file_shield_config = self.config.get("shield_config", {})
        shield_topics = file_shield_config.get("topics", [])

        # time_point_ns 必须由调用方显式传入，否则报错
        if shield_time_ns is None:
            raise ValueError(
                "缺少必需的屏蔽时间参数(shield_time_ns)。请在创建 FlowControlPlayer 时显式传入。"
            )

        self.shield_config = {
            "time_point_ns": shield_time_ns,
            "topics": shield_topics,
        }
        logger.info(
            f"屏蔽配置已生成: time_point_ns={shield_time_ns}ns, topics={shield_topics}"
        )

        self._shielding_active = False  # 标记屏蔽是否已激活

        if not self.main_topic:
            raise ValueError("配置文件中必须指定main_topic")

        # 发布话题列表
        self.pub_topics = self.config.get("pub_topics", [])
        # 要订阅的反馈话题
        self.feedback_topics = self.config.get("feedback_topics", [])

        # 要录制的话题
        self.record_topics = self.config.get("record_topics", [])

        # 验证配置有效性
        self._validate_config()

        # 路径和参数
        self.bag_path = bag_path
        self._record_path = record_path
        self.clock_freq = clock_freq
        self.remap = remap or []
        self.start_time = start_time
        self.end_time = end_time

        # 初始化播放器
        self._init_player()

        # 反馈控制
        self._feedback_events = {
            topic["name"]: threading.Event() for topic in self.feedback_topics
        }
        self._subscribers = []

        # 控制状态
        self._shutdown = threading.Event()
        self._frame_count = 0
        self._timeout_count = 0  # 超时计数器

        # 节点实例
        self.node = None
        self.block_on_enter = block_on_enter  # 存储参数值

    def _validate_config(self):
        """验证配置有效性"""
        if not self.main_topic:
            raise ValueError("配置文件必须包含main_topic设置")

        # 确保main_topic存在于pub_topics中
        main_in_pub = False
        for topic in self.pub_topics:
            if topic["name"] == self.main_topic:
                main_in_pub = True
                break

        if not main_in_pub:
            logger.warning(f"主话题 {self.main_topic} 未在pub_topics中找到，将自动添加")
            self.pub_topics.append(
                {"name": self.main_topic, "type": self.main_topic_type}
            )

    def _init_player(self):
        """初始化帧播放器，根据ROS版本选择适当的实现"""
        # 将秒时间戳转换为纳秒时间戳
        start_time_ns = int(self.start_time * 1e9) if self.start_time > 0 else 0
        end_time_ns = int(self.end_time * 1e9) if self.end_time > 0 else 0

        logger.info(f"FlowControlPlayer时间参数转换:")
        logger.info(f"  start_time: {self.start_time} 秒 -> {start_time_ns} 纳秒")
        logger.info(f"  end_time: {self.end_time} 秒 -> {end_time_ns} 纳秒")

        # FramePlayer 已经支持 ROS1 和 ROS2，只需传入相同的参数
        self.player = FramePlayer(
            source=self.bag_path,
            pub_topics=self.pub_topics,
            main_topic=self.main_topic,
            clock_freq=self.clock_freq,
            remap=self.remap,
            start_time=start_time_ns,  # 传递纳秒时间戳
            end_time=end_time_ns,  # 传递纳秒时间戳
            use_frame_play=False,  # 我们将手动控制帧播放
            ros_version=(
                self.ros_version if self.ros_version != "auto" else None
            ),  # 传入ROS版本
        )

    def _init_record(self):
        """初始化录制功能，根据ROS版本选择适当的实现"""
        if not self.record_topics:
            return

        if self.ros_version == "ros1":
            # 使用 ROS1Recorder 进行录制
            self.recorder = ROS1Recorder(
                topics=self.record_topics, record_path=self._record_path
            )
            self.recorder.init()
            logger.info(
                f"已初始化ROS1录制器，将记录以下话题: {[topic['name'] for topic in self.record_topics]}"
            )
        elif self.ros_version == "ros2":
            # 使用 ROS2Recorder 进行录制
            # 将话题配置转换为 TopicConfig 对象
            topic_configs = []
            for topic in self.record_topics:
                topic_configs.append(
                    TopicConfig(
                        name=topic["name"],
                        type=topic["type"],
                        remap=topic.get("remap"),
                        qos_depth=topic.get("qos_depth", 10),
                    )
                )

            self.recorder = ROS2Recorder(
                topics=topic_configs, record_path=self._record_path
            )
            self.recorder.init()
            logger.info(
                f"已初始化ROS2录制器，将记录以下话题: {[topic.name for topic in topic_configs]}"
            )
        else:
            logger.error(f"不支持的ROS版本: {self.ros_version}，无法初始化录制器")

    def _init_feedback_subscribers(self):
        """初始化反馈话题订阅，根据ROS版本选择适当的实现"""
        if self.ros_version == "ros1":
            self._init_feedback_subscribers_ros1()
        elif self.ros_version == "ros2":
            self._init_feedback_subscribers_ros2()
        else:
            logger.error(f"不支持的ROS版本: {self.ros_version}，无法初始化反馈订阅")

    def _init_feedback_subscribers_ros1(self):
        """初始化ROS1反馈话题订阅"""
        for topic in self.feedback_topics:
            try:
                msg_type = get_message_class(topic["type"])
                logger.info(
                    f"正在订阅ROS1反馈主题 {topic['name']} (类型: {topic['type']})"
                )
                if msg_type is None:
                    raise ImportError(f"未找到消息类型: {topic['type']}")

                callback = self._create_feedback_callback(topic["name"])
                sub = rospy.Subscriber(
                    topic.get("remap", topic["name"]), msg_type, callback
                )
                self._subscribers.append(sub)
                logger.info(f"已订阅ROS1反馈话题: {topic.get('remap', topic['name'])}")
            except Exception as e:
                logger.error(f"订阅ROS1主题 {topic['name']} 失败: {e}", exc_info=True)

    def _init_feedback_subscribers_ros2(self):
        """初始化ROS2反馈话题订阅"""
        for topic in self.feedback_topics:
            try:
                # 导入消息类型
                from utils.compatibility.ros_env_detector import get_message_class_ros2

                msg_type = get_message_class_ros2(topic["type"])
                logger.info(
                    f"正在订阅ROS2反馈主题 {topic['name']} (类型: {topic['type']})"
                )
                if msg_type is None:
                    raise ImportError(f"未找到ROS2消息类型: {topic['type']}")

                callback = self._create_feedback_callback(topic["name"])

                # 使用节点工厂创建的节点订阅主题
                sub = self.node.create_reader(
                    topic.get("remap", topic["name"]), callback, msg_type=msg_type
                )

                self._subscribers.append(sub)
                logger.info(f"已订阅ROS2反馈话题: {topic.get('remap', topic['name'])}")
            except Exception as e:
                logger.error(f"订阅ROS2主题 {topic['name']} 失败: {e}", exc_info=True)

    def _create_feedback_callback(self, topic_name):
        """创建反馈话题回调函数，对ROS1和ROS2通用

        参数:
            topic_name (str): 反馈话题名称

        返回:
            Callable: 回调函数，用于处理接收到的消息
        """

        def callback(msg):
            try:
                if topic_name in self._feedback_events:
                    logger.debug(f"收到反馈话题消息: {topic_name}")
                    self._feedback_events[topic_name].set()
                else:
                    logger.warning(f"收到未注册的反馈话题消息: {topic_name}")
            except Exception as e:
                logger.error(
                    f"处理反馈话题 {topic_name} 时发生错误: {e}", exc_info=True
                )

        return callback

    def _wait_for_all_feedback(self):
        """等待所有反馈话题接收到消息，支持ROS1和ROS2环境"""
        if not self.feedback_topics:
            return True

        # 设置超时时间
        start_time = time.time()
        all_received = False

        # 循环检查所有反馈是否收到
        while time.time() - start_time < self.timeout:
            time.sleep(0.01)

            # 检查是否所有反馈都已收到
            all_received = all(
                event.is_set() for event in self._feedback_events.values()
            )
            if all_received:
                break

            # 检查是否要关闭
            if self._shutdown.is_set():
                logger.info("收到关闭信号，停止等待反馈")
                return False

        # 检查是否所有反馈都已收到
        if not all_received:
            missing = [
                topic
                for topic, event in self._feedback_events.items()
                if not event.is_set()
            ]
            logger.warning(f"等待反馈超时，未收到以下话题的反馈: {missing}")
            # 增加超时计数
            self._timeout_count += 1
            logger.warning(f"当前超时次数: {self._timeout_count}")

            # 检查是否超过阈值
            if (
                self.timeout_cnt_threshold is not None
                and self._timeout_count >= self.timeout_cnt_threshold
            ):
                logger.error(
                    f"超时次数 ({self._timeout_count}) 已超过阈值 ({self.timeout_cnt_threshold})，退出播放"
                )
                self.stop()
        else:
            # 如果成功接收所有反馈，重置超时计数
            self._timeout_count = 0
            logger.info("成功收到所有反馈消息")

        # 重置所有事件，为下一帧做准备
        for event in self._feedback_events.values():
            event.clear()

        return all_received

    def _check_and_apply_shielding(self):
        """检查是否达到屏蔽时间点，并应用屏蔽规则。"""
        if not self.shield_config or self._shielding_active:
            return  # 没有配置或已经激活，则无需操作

        shield_time_ns = self.shield_config.get("time_point_ns")
        topics_to_shield = self.shield_config.get("topics")

        if shield_time_ns is None or not topics_to_shield:
            logger.warning("屏蔽配置无效 (缺少 time_point_ns 或 topics)，跳过屏蔽。")
            self.shield_config = None  # 避免重复检查无效配置
            return

        current_msg_time_obj = self.player.current_message_time_ns
        if current_msg_time_obj is None:
            return  # 还没有消息时间

        # 确保 current_msg_time_obj 是 Time 对象并转换为纳秒
        if hasattr(current_msg_time_obj, "to_nsec"):
            current_msg_time = current_msg_time_obj.to_nsec()
        else:
            # 如果已经是数值类型（例如，在非ROS环境或测试中），直接使用
            # 或者记录一个警告，如果预期总是Time对象
            logger.warning(
                "current_message_time_ns 不是预期的Time对象，将直接使用其值。"
            )
            current_msg_time = current_msg_time_obj

        if current_msg_time >= shield_time_ns:
            logger.info(
                f"达到屏蔽时间点 (当前: {current_msg_time} ns, 阈值: {shield_time_ns} ns)。"
                f"开始屏蔽话题: {topics_to_shield}"
            )
            self.player.set_shielded_topics(topics_to_shield)
            self._shielding_active = True
            # 可选: 将 shield_config 置为 None 或修改 _shielding_active 逻辑，以避免不必要的重复调用 set_shielded_topics
            # self.shield_config = None # 如果只想应用一次

    def _play_loop(self):
        """播放循环，控制帧播放节奏，支持ROS1和ROS2环境"""
        try:
            while not self._shutdown.is_set() and not self.player.done():

                # 播放一帧
                self._frame_count += 1
                frame_played = self.player.play_next_frame()

                if frame_played:
                    # 检查并应用屏蔽规则
                    self._check_and_apply_shielding()

                    # 等待反馈
                    logger.info("等待反馈...")
                    feedback_received = self._wait_for_all_feedback()

                    if not feedback_received:
                        logger.warning(
                            f"第 {self._frame_count} 帧未收到完整反馈，继续播放"
                        )

                        # 如果设置了超时阈值且已达到，则退出循环
                        if (
                            self.timeout_cnt_threshold is not None
                            and self._timeout_count >= self.timeout_cnt_threshold
                        ):
                            logger.error(
                                f"已达到超时阈值 ({self.timeout_cnt_threshold})，停止播放"
                            )
                            # 确保底层播放器也停止
                            self.player._shutdown.set()
                            break
                else:
                    # 如果没有成功播放帧，可能是到达了文件末尾
                    if self.player.done():
                        logger.info("播放完成")
                        break

                    logger.warning(f"第 {self._frame_count} 帧播放失败，尝试继续")
                    # 短暂休眠以避免CPU占用过高
                    time.sleep(0.1)
        except Exception as e:
            logger.error(f"播放过程中发生错误: {e}", exc_info=True)
        finally:
            self.stop()

    def stop(self):
        """停止播放并清理资源，支持ROS1和ROS2环境"""
        if not self._shutdown.is_set():
            self._shutdown.set()
        # 停止播放器
        if hasattr(self, "player"):
            self.player.stop()

        # 重置屏蔽状态，以便下次播放时重新应用
        self._shielding_active = False
        if (
            hasattr(self, "player")
            and self.player
            and hasattr(self.player, "set_shielded_topics")
        ):
            # 清除屏蔽列表，确保下次播放时是干净的状态
            self.player.set_shielded_topics([])

        # 根据ROS版本处理订阅和清理
        if self.ros_version == "ros1":
            # 取消所有ROS1订阅
            for sub in self._subscribers:
                sub.unregister()

            # # 通知ROS系统需要关闭
            # if not rospy.is_shutdown():
            #     rospy.signal_shutdown("超时阈值达到，强制关闭")
            # # 确保rospy完全退出
            # while not rospy.is_shutdown():
            #     logger.info("等待rospy完全退出...")
            #     time.sleep(0.1)

        elif self.ros_version == "ros2":
            # 使用节点工厂管理的节点，清理订阅者
            self._subscribers = []

            # 销毁节点 - 使用节点工厂的方式
            if self.node is not None:
                try:
                    self.node.destroy()
                    self.node = None
                    logger.info("ROS2节点已销毁")
                except Exception as e:
                    logger.error(f"销毁ROS2节点时发生错误: {e}")
                    self.node = None

        # 关闭录制器
        if hasattr(self, "recorder"):
            self.recorder.stop()

        logger.info("播放已停止，资源已清理")

    def start(self, block=True):
        """启动播放器，支持ROS1和ROS2环境"""
        logger.info("开始播放并等待反馈...")

        # 根据ROS版本初始化节点
        if self.ros_version == "ros1":
            # 初始化ROS1节点
            if not rospy.core.is_initialized():
                # rospy.init_node("flow_control_player", anonymous=True)
                pass
        elif self.ros_version == "ros2":
            # 使用NodeFactory初始化ROS2节点
            if self.node is None:
                params = {"name": "flow_control_player", "remap": {}}
                self.node = NodeFactory().get_node(
                    run_env=RunEnvType.ROS2, params=params
                )
                logger.info("ROS2节点初始化成功")
        else:
            logger.error(f"不支持的ROS版本: {self.ros_version}")
            raise ValueError(f"不支持的ROS版本: {self.ros_version}")

        # 初始化记录和订阅
        self._init_record()
        self._init_feedback_subscribers()

        # 启动播放器的消息加载线程
        self.player.start(block=False)

        # 根据阻塞模式决定是直接运行还是启动新线程
        if block:
            self._play_loop()
            return None
        else:
            # 非阻塞模式，启动一个新线程运行播放循环
            self._play_thread = threading.Thread(target=self._play_loop)
            self._play_thread.daemon = (
                True  # 设置为守护线程，这样主线程退出时会自动终止
            )
            self._play_thread.start()
            return self._play_thread

    def __enter__(self):
        """上下文管理器入口"""
        self.start(block=self.block_on_enter)  # 使用存储的参数值
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器退出"""
        self.stop()
        return False
