#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
"""ROS 消息加载器模块，支持 ROS1 和 ROS2"""

import traceback
import os
import threading
from queue import Queue, Empty
from typing import Optional, List, Any, Tuple, Dict, Callable, Union
import time
from abc import ABC, abstractmethod

from utils.loggers import LoggerProxy

logger = LoggerProxy("player")
from utils.compatibility.node_factory import RunEnvType

# 检测 ROS 环境可用性
from utils.compatibility.ros_env_detector import (
    is_ros1_available,
    is_ros2_available,
    get_ros_version,
    detect_bag_file_type,
)

# 导入 ROS1 相关模块
ROS1_AVAILABLE = is_ros1_available()
if ROS1_AVAILABLE:
    try:
        from api.player.tools.bag_reader_ros1 import (
            read_bag_messages as read_bag_messages_ros1,
        )

        logger.info("ROS1 环境可用，已加载 ROS1 消息读取器")
    except ImportError:
        logger.warning("ROS1 环境可用，但无法导入 bag_reader_ros1 模块")
        logger.error(traceback.format_exc())
        ROS1_AVAILABLE = False
else:
    logger.debug("ROS1 环境不可用")
    read_bag_messages_ros1 = None

# 导入 ROS2 相关模块
ROS2_AVAILABLE = is_ros2_available()
if ROS2_AVAILABLE:
    try:
        from api.player.tools.bag_reader_ros2 import (
            read_bag_messages as read_bag_messages_ros2,
        )

        logger.info("ROS2 环境可用，已加载 ROS2 消息读取器")
    except ImportError:
        logger.warning("ROS2 环境可用，但无法导入 bag_reader_ros2 模块")
        ROS2_AVAILABLE = False
else:
    logger.debug("ROS2 环境不可用")
    read_bag_messages_ros2 = None

# 记录当前环境中的 ROS 版本
ros_version = get_ros_version()
logger.info(f"当前 ROS 环境: {ros_version}")

CLOCK_TOPIC = "/clock"
TIME_CONSTANT = 1_000_000_000


class TimeStampExtractor(ABC):
    """时间戳提取器抽象基类"""

    @abstractmethod
    def extract_timestamp(self, timestamp: Any) -> int:
        """从时间戳对象中提取纳秒时间

        参数:
            timestamp: 时间戳对象

        返回:
            纳秒时间整数
        """
        pass


class ROS1TimeStampExtractor(TimeStampExtractor):
    """ROS1 时间戳提取器"""

    def extract_timestamp(self, timestamp: Any) -> int:
        """从 ROS1 时间戳中提取纳秒时间"""
        if timestamp is None:
            return 0
        return timestamp.secs * TIME_CONSTANT + timestamp.nsecs


class ROS2TimeStampExtractor(TimeStampExtractor):
    """ROS2 时间戳提取器"""

    def extract_timestamp(self, timestamp: Any) -> int:
        """从 ROS2 时间戳中提取纳秒时间"""
        if timestamp is None:
            return 0
        return timestamp.nanoseconds


class MessageLoaderFactory:
    """消息加载器工厂类"""

    @staticmethod
    def create_message_loader(
        source: str,
        pub_topics: Optional[List[Any]] = None,
        clock_freq: float = 0.0,
        start_time: int = 0,
        end_time: int = 0,
        ros_version: str = "auto",
    ) -> "MessageLoader":
        """创建消息加载器

        参数:
            source: 数据源路径
            pub_topics: 要发布的话题列表
            clock_freq: 时钟频率
            start_time: 开始时间偏移
            end_time: 结束时间偏移
            ros_version: ROS 版本，可选值为 'ros1', 'ros2', 'auto'

        返回:
            消息加载器实例
        """
        # 确定 ROS 版本
        if ros_version != "auto":
            if ros_version == "ros1":
                if not ROS1_AVAILABLE:
                    raise ImportError("未找到 ROS1 依赖")
                version = RunEnvType.ROS1
            elif ros_version == "ros2":
                if not ROS2_AVAILABLE:
                    raise ImportError("未找到 ROS2 依赖")
                version = RunEnvType.ROS2
            else:
                raise ValueError(
                    f"不支持的 ROS 版本: {ros_version}，可选值为 'ros1', 'ros2', 'auto'"
                )
        else:
            # 使用新的 detect_bag_file_type 函数自动检测文件类型
            bag_type = detect_bag_file_type(source)
            logger.info(f"检测到数据包类型: {bag_type}")

            if bag_type == "ros1":
                if not ROS1_AVAILABLE:
                    raise ImportError("未找到 ROS1 依赖，但数据包是 ROS1 格式")
                version = RunEnvType.ROS1
            elif bag_type == "ros2":
                if not ROS2_AVAILABLE:
                    raise ImportError("未找到 ROS2 依赖，但数据包是 ROS2 格式")
                version = RunEnvType.ROS2
            elif bag_type == "unknown":
                # 如果无法确定文件类型，则基于环境可用性选择
                logger.warning(f"无法确定数据包类型: {source}，将基于环境可用性选择")
                if ROS1_AVAILABLE:
                    logger.info("选择使用 ROS1 加载器")
                    version = RunEnvType.ROS1
                elif ROS2_AVAILABLE:
                    logger.info("选择使用 ROS2 加载器")
                    version = RunEnvType.ROS2
                else:
                    raise ImportError("未找到任何 ROS 依赖")

        # 创建消息加载器
        return MessageLoader(
            source=source,
            pub_topics=pub_topics,
            clock_freq=clock_freq,
            start_time=start_time,
            end_time=end_time,
            ros_version=version,
        )


class MessageLoader:
    """ROS 消息加载器。

    负责数据源验证、消息读取与队列管理、后台加载线程管理。
    支持 ROS1 和 ROS2 格式的数据包。

    参数:
        source: 数据源路径
        pub_topics: 要发布的话题列表
        clock_freq: 时钟频率
        start_time: 开始时间偏移
        end_time: 结束时间偏移
        ros_version: ROS 版本，默认自动检测
    """

    def __init__(
        self,
        source: str,
        pub_topics: Optional[List[Any]] = None,
        clock_freq: float = 0.0,
        start_time: int = 0,
        end_time: int = 0,
        ros_version: str = RunEnvType.ROS1,
    ):
        self._validate_source(source)
        self._source = source
        self._clock_freq = clock_freq
        self._start_msg_time = int(start_time)
        self._end_msg_time = int(end_time)
        self._ros_version = ros_version

        # 设置时间戳提取器
        if self._ros_version == RunEnvType.ROS1:
            self._timestamp_extractor = ROS1TimeStampExtractor()
            self._read_bag_messages = read_bag_messages_ros1
            logger.info("使用 ROS1 消息加载器")
        elif self._ros_version == RunEnvType.ROS2:
            self._timestamp_extractor = ROS2TimeStampExtractor()
            self._read_bag_messages = read_bag_messages_ros2
            logger.info("使用 ROS2 消息加载器")
        else:
            raise ValueError(f"不支持的 ROS 版本: {ros_version}")

        self._filter_topics = []
        if pub_topics is not None:
            self._filter_topics = [topic["name"] for topic in pub_topics]

        self._fixed_delta_nanoseconds = (
            int(1 / clock_freq * TIME_CONSTANT) if clock_freq else 0
        )
        self._msg_queue = Queue(maxsize=50)
        self._shutdown = threading.Event()
        self._add_done = threading.Event()
        self._add_thread = threading.Thread(target=self._add_message)

    def _validate_source(self, source: str):
        """验证数据源文件是否存在"""
        if not os.path.exists(source):
            raise FileNotFoundError(f"数据源文件不存在: {source}")

    def _add_message(self):
        """消息加载线程主函数"""
        last_msg_time = 0

        for topic, msg_tsp, msg in self._read_bag_messages(
            self._source,
            topics=self._filter_topics,
            start_time=self._start_msg_time,
            end_time=self._end_msg_time,
        ):
            if self._clock_freq:
                last_msg_time = self._add_clock_time(last_msg_time, msg_tsp)

            self._msg_queue.put((topic, msg_tsp, msg))
            if self._shutdown.is_set():
                break
        # else:
        #     if self._clock_freq:
        #         self._add_clock_time(self._end_msg_time)
        self._add_done.set()

    def _add_clock_time(self, last_time: int, next_time: Optional[Any] = None) -> int:
        """添加时钟消息到队列"""

        def _convert_time(nano_sec):
            return nano_sec // TIME_CONSTANT, nano_sec % TIME_CONSTANT

        if next_time is None:
            secs, nsecs = _convert_time(last_time)
            self._msg_queue.put((CLOCK_TOPIC, last_time, (secs, nsecs)))
            return last_time

        if last_time == 0:
            # 根据 ROS 版本提取时间戳
            last_time = (
                self._timestamp_extractor.extract_timestamp(next_time)
                - 2 * self._fixed_delta_nanoseconds
            )

        # 根据 ROS 版本计算下一个时间戳
        next_time_ns = self._timestamp_extractor.extract_timestamp(next_time)

        while last_time + self._fixed_delta_nanoseconds < next_time_ns:
            last_time += self._fixed_delta_nanoseconds
            secs, nsecs = _convert_time(last_time)
            self._msg_queue.put((CLOCK_TOPIC, last_time, (secs, nsecs)))
        return last_time

    def start(self):
        """启动消息加载线程"""
        self._add_thread.start()

    def stop(self):
        """停止消息加载"""
        if not self._shutdown.is_set():
            self._shutdown.set()
            self.empty_queue()
            if self._add_thread.is_alive():
                self._add_thread.join(timeout=1)

    def empty_queue(self):
        """清空消息队列"""
        while not self._msg_queue.empty():
            self._msg_queue.get()

    def get_message(self, timeout: float = 10) -> Optional[Tuple[str, float, Any]]:
        """获取下一条消息

        参数:
            timeout: 超时时间，单位秒

        返回:
            元组 (话题名, 时间戳, 消息) 或 None（如果队列为空且加载完成）
        """
        try:
            if self._add_done.is_set() and self._msg_queue.empty():
                raise Empty
            return self._msg_queue.get(timeout=timeout)
        except:
            raise Empty

    def is_done(self) -> bool:
        """检查是否加载完成"""
        return self._add_done.is_set() and self._msg_queue.empty()

    def __enter__(self):
        self.start()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.stop()


# 为了向后兼容，提供 MessageLoaderROS2 类
class MessageLoaderROS2(MessageLoader):
    """ROS2 消息加载器，继承自通用消息加载器。

    此类仅用于向后兼容，功能与 MessageLoader 相同。
    """

    def __init__(
        self,
        source: str,
        pub_topics: Optional[List[Any]] = None,
        clock_freq: float = 0.0,
        start_time: int = 0,
        end_time: int = 0,
    ):
        super().__init__(
            source=source,
            pub_topics=pub_topics,
            clock_freq=clock_freq,
            start_time=start_time,
            end_time=end_time,
            ros_version=RunEnvType.ROS2,
        )
