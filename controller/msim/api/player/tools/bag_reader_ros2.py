#!/usr/bin/env python3
"""
ROS2 bag 文件读取工具。

该模块提供了读取 ROS2 bag 文件的功能，支持按时间范围和话题过滤消息。
"""

from datetime import datetime
from os import PathLike
from typing import Iterable, Iterator, Optional, Tuple, Union, Any, Dict, Type
import traceback
from rclpy.time import Time
import time

try:
    from rosbag2_py import SequentialReader, StorageOptions, ConverterOptions
    from rclpy.serialization import deserialize_message
    from rosidl_runtime_py.utilities import get_message
except ImportError as e:
    raise ImportError(f"无法导入ROS2 bag相关模块，请确保已安装相应库: {e}")

from utils.loggers import LoggerProxy

logger = LoggerProxy("player")

# 话题到消息类型的映射配置
TOPIC_MESSAGE_MAPPING = {
    "/perception/camera/camera_obstacle_front60": "TrackedObjects",
    "/perception/fusion_mid_inno/lidar_obstacle": "TrackedObjects",
    "/perception/fusion/obstacles": "TrackedObjects",
}


def read_bag_messages(
    source: Union[str, PathLike],
    topics: Optional[Iterable[str]] = None,
    start_time: Optional[Union[int, datetime]] = None,
    end_time: Optional[Union[int, datetime]] = None,
) -> Iterator[Tuple[str, Time, Any]]:
    """
    读取 ROS2 bag 文件中的消息。

    参数:
        source: bag 文件路径
        topics: 要读取的话题列表，如果为 None 则读取所有话题
        start_time: 开始时间，可以是 datetime 对象或纳秒时间戳
        end_time: 结束时间，可以是 datetime 对象或纳秒时间戳

    返回:
        Iterator[Tuple[str, Time, Any]]: 返回一个迭代器，每个元素为 (话题名, 时间戳, 消息)

    异常:
        Exception: 当 bag 文件读取失败时抛出
        ValueError: 当时间参数格式不正确时抛出
    """
    logger.info(
        f"source:{source}, topics:{topics}, start_time:{start_time}, end_time:{end_time}"
    )

    # 转换时间格式
    if start_time is not None and isinstance(start_time, datetime):
        start_time = int(start_time.timestamp() * 1e9)
    if end_time is not None and isinstance(end_time, datetime):
        end_time = int(end_time.timestamp() * 1e9)
    # 如果开始时间和结束时间均为0，则设置为None

    if start_time == 0:
        start_time = None
    if end_time == 0:
        end_time = None

    try:
        # 配置存储选项
        storage_id = "mcap"  # 默认使用mcap

        # 根据文件扩展名选择适当的存储插件
        if str(source).endswith(".mcap"):
            storage_id = "mcap"

        storage_options = StorageOptions(uri=str(source), storage_id=storage_id)

        # 配置转换选项
        converter_options = ConverterOptions(
            input_serialization_format="cdr", output_serialization_format="cdr"
        )

        # 创建顺序读取器
        reader = SequentialReader()
        reader.open(storage_options, converter_options)

        # 获取所有话题和类型
        topic_types = reader.get_all_topics_and_types()

        # 创建话题名称到类型的映射
        topic_type_map = {
            topic_info.name: topic_info.type for topic_info in topic_types
        }

        # 筛选要读取的话题
        filter_topics = set(topics) if topics else set(topic_type_map.keys())

        # 循环读取消息
        has_next = True
        while has_next:
            try:
                (topic, data, timestamp) = reader.read_next()

                # 如果不在要读取的话题列表中，则跳过
                if topic not in filter_topics:
                    continue

                # 如果指定了开始时间，则跳过在开始时间之前的消息
                if start_time is not None and timestamp < start_time:
                    logger.info(
                        f"开始时间: {start_time}, 跳过在开始时间之前的消息: {topic}, {timestamp}"
                    )
                    continue

                # 如果指定了结束时间，则跳过在结束时间之后的消息
                if end_time is not None and timestamp > end_time:
                    logger.info(
                        f"结束时间: {end_time}, 跳过在结束时间之后的消息: {topic}, {timestamp}"
                    )
                    continue

                # 获取消息类型
                message_type = get_message(topic_type_map[topic])

                # 反序列化消息
                ros_message = deserialize_message(data, message_type)

                # 提取消息时间戳
                msg_timestamp = _extract_message_timestamp(
                    topic, ros_message, timestamp
                )
                if msg_timestamp is not None:
                    logger.debug(f"读取到消息: {topic}, {msg_timestamp}, {ros_message}")
                    yield topic, msg_timestamp, ros_message
                else:
                    logger.warning(
                        f"无法从话题 '{topic}' 的消息中提取时间戳，跳过该消息"
                    )
            except (StopIteration, RuntimeError):
                has_next = False
            except Exception as e:
                logger.error(f"处理消息时发生错误: {e}")
                logger.error(traceback.format_exc())
                continue
    except Exception as e:
        logger.error(f"读取 bag 文件失败: {e}")
        logger.error(traceback.format_exc())
        raise


def _extract_message_timestamp(topic: str, msg: Any, timestamp: int) -> Optional[Time]:
    """
    从消息中提取时间戳。

    参数:
        topic: 话题名
        msg: ROS 消息对象
        timestamp: 消息的存储时间戳

    返回:
        Optional[Time]: 提取的时间戳，如果无法提取则返回 None
    """
    # 首先尝试从消息头获取时间戳
    if hasattr(msg, "header") and hasattr(msg.header, "stamp"):
        return Time(seconds=msg.header.stamp.sec, nanoseconds=msg.header.stamp.nanosec)

    # 尝试从特定消息类型获取时间戳
    msg_class = TOPIC_MESSAGE_MAPPING.get(topic)
    if msg_class is None:
        # 如果没有指定的映射，则使用消息的存储时间戳
        seconds = timestamp // 1_000_000_000
        nanoseconds = timestamp % 1_000_000_000
        return Time(seconds=seconds, nanoseconds=nanoseconds)

    try:
        # 这里可以添加特定消息类型的时间戳提取逻辑
        # 如果没有特定逻辑，则使用消息的存储时间戳
        seconds = timestamp // 1_000_000_000
        nanoseconds = timestamp % 1_000_000_000
        return Time(seconds=seconds, nanoseconds=nanoseconds)
    except Exception as e:
        logger.error(f"从 {topic} 的消息中提取时间戳失败: {e}")
        logger.error(traceback.format_exc())
        return None


if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        bag_path = sys.argv[1]
    else:
        bag_path = "/home/<USER>/mogosim_engine/controller/test/test_data/test_bag_multi"

    print(f"读取 bag 文件: {bag_path}")
    for topic, msg_time, msg in read_bag_messages(bag_path):
        print(f"Topic: {topic}, Time: {msg_time}")
