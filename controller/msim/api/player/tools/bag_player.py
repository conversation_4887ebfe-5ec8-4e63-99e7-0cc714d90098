#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
"""ROS 仿真数据播放器模块，同时支持 ROS1 和 ROS2"""

import threading
import time
import signal
from queue import Queue, Empty
from typing import Any, Optional, Union, List, Dict
import traceback
import os

from utils.compatibility.node_factory import (
    NodeFactory,
    RunEnvType,
    WriterBase,
    TimeWriter,
)
from utils.loggers import LoggerProxy
logger = LoggerProxy()
from utils.compatibility.ros_env_detector import (
    is_ros1_available,
    is_ros2_available,
    get_ros_version,
)
from api.player.tools.publisher_manager import PublisherManager, CLOCK_TOPIC
from api.player.tools.message_loader import MessageLoader, MessageLoaderFactory

TIME_CONSTANT = 1_000_000_000


class BagPlayer:
    """ROS 仿真数据播放器，同时支持 ROS1 和 ROS2。

    该类实现了 ROS 环境下的仿真数据帧播放功能，支持时钟控制和话题重映射。
    根据环境和配置自动选择使用 ROS1 或 ROS2 的实现。

    参数:
        source (Union[str, bytes, PathLike[str]]): 数据源路径
        pub_topics (Optional[List[Any]]): 要发布的话题列表
        main_topic (str, optional): 主话题名称
        clock_freq (float, optional): 时钟频率，默认为 0.0
        remap (Optional[List[str]], optional): 话题重映射列表
        start_time (int, optional): 开始时间偏移，默认为 0
        end_time (int, optional): 结束时间偏移，默认为 0
        use_frame_play (bool, optional): 是否使用帧播放模式，默认为 True
        ros_version (Optional[int]): 指定使用的 ROS 版本，可选值为 1 或 2，默认自动检测
    """

    def __init__(
        self,
        source: str = "",
        pub_topics: Optional[List[Any]] = None,
        main_topic: str = None,
        clock_freq: float = 0.0,
        remap: Optional[List[str]] = None,
        start_time: int = 0,
        end_time: int = 0,
        use_frame_play: bool = True,
        ros_version: Optional[str] = None,
    ):
        self.pub_topics = pub_topics
        self.main_topic = main_topic
        self._clock_freq = clock_freq
        self.use_frame_play = use_frame_play

        self._current_msg_time = None
        self._current_real_time = None
        self._spec_msg_time = None

        self._remap = remap or []
        self._shutdown = threading.Event()
        self._pause = threading.Event()
        self._add_done = threading.Event()

        self._pub_cnt_map: Dict[str, int] = {}
        self._start_time: Optional[float] = None
        self.pub_frame_cnt: int = 0

        # 确定 ROS 版本
        self._ros_version = ros_version
        if self._ros_version is None:
            ros_version_str = get_ros_version()
            # 将字符串版本转换为整数
            if ros_version_str == "ros1":
                self._ros_version = "ros1"
            elif ros_version_str == "ros2":
                self._ros_version = "ros2"
            elif ros_version_str == "both":
                # 当两个版本都可用时，优先使用 ROS2
                self._ros_version = "ros2"
                logger.info("检测到 ROS1 和 ROS2 都可用，优先使用 ROS2")
            else:
                # 如果无法自动检测，默认使用 ROS1
                logger.warning("无法自动检测 ROS 版本，默认使用 ROS1")
                self._ros_version = "ros1"

        # 创建消息加载器
        ros_env_type = "ros1" if self._ros_version == "ros1" else "ros2"
        logger.info(f"使用的 ROS 版本: {self._ros_version}")
        self._message_loader = MessageLoaderFactory.create_message_loader(
            source=source,
            pub_topics=pub_topics,
            clock_freq=clock_freq,
            start_time=start_time,
            end_time=end_time,
            ros_version=ros_env_type,
        )

        # 创建发布者管理器
        self._publisher_manager = PublisherManager(
            pub_topics=pub_topics,
            remap=remap,
            clock_freq=clock_freq,
            ros_version=self._ros_version,
        )

        self._play_thread = None

    @property
    def current_message_time_ns(self) -> Optional[int]:
        """获取当前正在处理或最后处理的消息的原始时间戳 (纳秒)。"""
        return self._spec_msg_time  # _spec_msg_time 存储的是纳秒级时间戳

    def set_shielded_topics(self, topics: List[str]):
        """设置需要屏蔽的话题列表，并传递给 PublisherManager。"""
        if hasattr(self, "_publisher_manager") and self._publisher_manager:
            self._publisher_manager.set_shielded_topics(topics)
        else:
            logger.warning("BagPlayer: PublisherManager 未初始化，无法设置屏蔽列表。")

    def __enter__(self):
        """进入上下文时自动启动播放器。"""
        self.start(block=False)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """退出上下文时确保资源释放。"""
        if self.use_frame_play:
            self.wait_for_complete()
        self.stop()
        logger.info("资源已被清理")
        return False

    def start(self, block: bool = True):
        """启动播放器"""
        self._start_time = time.time()
        self._message_loader.start()

        if self.use_frame_play:
            self._play_thread = threading.Thread(target=self.play_by_frame)
            self._play_thread.start()
        if block:
            self.wait()

    def stop(self):
        """停止播放并清理资源"""
        if not self._shutdown.is_set():
            self._shutdown.set()
            logger.info("正在清理资源...")
            self.wait()
            logger.info("播放已停止，资源已清理")

    def wait(self):
        """等待播放完成"""
        self._shutdown.wait()
        self._publisher_manager.destroy()
        self._message_loader.stop()
        try:
            logger.info(
                f"播放完成，总耗时: {time.time() - self._start_time or 0:.2f} 秒"
            )
            logger.info(self._format_topic_stats(self._pub_cnt_map))
        except TypeError:
            logger.warning("未播放...")

        if self._play_thread and self._play_thread.is_alive():
            self._play_thread.join()

    def done(self):
        """检查播放是否完成"""
        return self._shutdown.is_set() or (self._add_done.is_set())

    def play_next_frame(self) -> bool:
        """播放下一帧数据"""
        try:
            frame_ended = False
            self.pub_frame_cnt += 1
            logger.info(f"正在播放第 {self.pub_frame_cnt} 帧")
            while not frame_ended and not self._shutdown.is_set():
                try:
                    topic, pub_t, raw_msg = self._message_loader.get_message(timeout=10)

                    # pub_t 就是原始消息时间戳 (纳秒)
                    self._spec_msg_time = pub_t  # 更新当前消息时间

                    if topic == CLOCK_TOPIC:
                        self._publisher_manager.publish_clock(*raw_msg)
                        # logger.info(f"发布时钟消息: {raw_msg}") # 可以减少日志量
                    else:
                        self._publisher_manager.publish(
                            topic, raw_msg
                        )  # publish 方法现在会检查屏蔽
                        # logger.info(f"发布消息: {topic}, {pub_t}") # 可以减少日志量

                    self._pub_cnt_map[topic] = self._pub_cnt_map.get(topic, 0) + 1

                    if topic == self.main_topic:
                        frame_ended = True

                except Empty:
                    logger.info("队列为空，结束当前帧")
                    self._add_done.set()
                    break
                except Exception as e:
                    logger.error(f"消息发布失败: {e}")
                    logger.error(traceback.format_exc())
                    self._add_done.set()
                    break
            return frame_ended
        except Exception as e:
            logger.error(f"帧播放失败: {e}", exc_info=True)
            return False

    def play_by_frame(self):
        """连续帧播放函数。"""
        self._current_real_time = int(time.time() * TIME_CONSTANT)
        while not self._shutdown.is_set():
            if not self._pause.is_set():
                self.play_next_frame()
            else:
                time.sleep(0.1)

            if self._add_done.is_set():
                self._shutdown.set()
                break

    def pause(self):
        """暂停播放"""
        if not self._pause.is_set():
            self._pause.set()

    def resume(self):
        """恢复播放"""
        self._current_real_time = int(time.time() * TIME_CONSTANT)
        self._current_msg_time = self._spec_msg_time
        if self._pause.is_set():
            self._pause.clear()

    def is_pause(self):
        """检查是否暂停"""
        return self._pause.is_set()

    def wait_for_complete(self):
        """等待播放完成。"""
        while not self._shutdown.is_set():
            if self._add_done.is_set():
                break
            time.sleep(0.1)

    def _sleep_until(self):
        """控制播放速度"""
        current_time_ns = int(time.time() * TIME_CONSTANT)
        sleep_gap = (
            current_time_ns
            - (self._spec_msg_time - self._current_msg_time + self._current_real_time)
        ) / TIME_CONSTANT
        if sleep_gap < 0:
            time.sleep(-(sleep_gap))
        else:
            self._current_msg_time = self._spec_msg_time
            self._current_real_time = current_time_ns

    def _format_topic_stats(self, topic_stats: Dict[str, int]) -> str:
        """格式化话题统计信息。

        参数:
            topic_stats: 话题消息计数字典

        返回:
            str: 格式化后的统计信息
        """
        if not topic_stats:
            return "未发布任何消息"

        result = ["话题发布统计:"]
        result.append("-" * 50)
        for topic, count in sorted(
            topic_stats.items(), key=lambda x: x[1], reverse=True
        ):
            result.append(f"{topic}: {count}")
        return "\n".join(result)


FramePlayer = BagPlayer


if __name__ == "__main__":
    # ROS2
    # test_bag_path = '/home/<USER>/mogosim_engine/controller/test/test_data/ros2_test'
    # # test_bag_path = '/home/<USER>/mogosim_engine/controller/test/test_data/test_bag_multi'

    # # 初始化播放器
    # player = BagPlayer(source=test_bag_path, use_frame_play=True, pub_topics=    [{
    #     'name': '/string_topic',
    #     'type': 'std_msgs/String'
    # }, {
    #     'name': '/int_topic',
    #     'type': 'std_msgs/Int32'
    # }], end_time=1743495327350003845)

    # # 开始播放
    # player.start(block=True)

    # # 验证播放是否正常完成
    # assert player.done(), "播放未正常完成"
    # assert player._start_time is not None, "播放未正常启动"

    # ROS1
    test_bag_path = (
        "/home/<USER>/simulation/bag/20230809135224-2390643200-rosmaster-JLBJA47708D.bag"
    )

    # 初始化播放器
    player = BagPlayer(
        source=test_bag_path,
        use_frame_play=True,
        pub_topics=[
            {
                "name": "/perception/camera/object_detection_front60_trfclts/nvjpeg",
                "type": "sensor_msgs/CompressedImage",
            },
            {
                "name": "/perception/fusion/obstacles",
                "type": "autopilot_msgs/BinaryData",
            },
        ],
    )

    # 开始播放
    player.start(block=True)

    # 验证播放是否正常完成
    assert player.done(), "播放未正常完成"
    assert player._start_time is not None, "播放未正常启动"
