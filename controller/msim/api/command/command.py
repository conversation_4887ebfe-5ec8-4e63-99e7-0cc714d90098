#  Copyright (C) Mogo.ai Information and Technology Co.Ltd 
#
#  All rights reserved.
#
#  This document contains proprietary information belonging to Mogo.ai Co.Ltd.
#  Passing on and copying of this document, and communication of its contents
#  is not permitted without prior written authorization.
#
#
#  <AUTHOR>
#  @date 10/10/23, 8:56 PM
from typing import Dict

from data.task_data import TaskData


class Command:
    def __init__(self, task_data: TaskData):
        self._task_data = task_data

    def get_runtime_info(self, module: str) -> Dict[str, Dict[str, str]]:
        return {}
