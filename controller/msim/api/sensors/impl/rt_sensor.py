#  Copyright (C) Mogo.ai Information and Technology Co.Ltd 
#
#  All rights reserved.
#
#  This document contains proprietary information belonging to Mogo.ai Co.Ltd.
#  Passing on and copying of this document, and communication of its contents
#  is not permitted without prior written authorization.
#
#
#  <AUTHOR>
#  @date 9/25/23, 7:37 PM
import json

from api.actor.actor_base import Actor
from api.sensors.sensor_base import Sensor, CollisionEvent
from utils.loggers import get_server_logger
logger = get_server_logger()


class RTSensor(Sensor):
    def __init__(self, name, model_type, parent: Actor):
        super().__init__(name, model_type, parent)

    def listen(self, callback):
        self._callback_list.append(callback)


class CollisionSensor(RTSensor):
    def __init__(self, name, model_type=None, parent=None):
        super().__init__(name, model_type, parent)
        from api.common.node import get_node
        from std_msgs.msg import String
        self._node = get_node()
        self._collision_reader = \
            self._node.create_reader('/simulation/collision_event', self._handle_msg, msg_type=String)
        # TODO: filter by type, need to remove when map sdk is online
        self._filter_type = ("static.road", )

    def _handle_msg(self, data):
        try:
            msg_dict = json.loads(data.data)
        except Exception as e:
            logger.warning(f"Failed to parse collision event message: {data.data}, error: {str(e)}")
            msg_dict = {}
        other_actor_json = msg_dict.get('other_actor', {})
        other_actor = Actor(
            other_actor_json.get('name'),
            actor_id=other_actor_json.get('id'),
            model_type=other_actor_json.get('type_id')
        )
        if other_actor_json.get('type_id') in self._filter_type:
            logger.info(f"skip the collision event with {str(other_actor_json.get('type_id'))}")
            return
        event = CollisionEvent(timestamp=msg_dict.get('timestamp'), actor=self.parent, other_actor=other_actor)
        for callback in self._callback_list:
            try:
                callback(event)
            except Exception as e:
                logger.warning(f"Failed to call collision event callback, error: {str(e)}")

    def destroy(self):
        try:
            # self._collision_subscriber.destroy()
            pass
        except Exception as e:
            logger.warning(f"Failed to destroy collision subscriber, error: {str(e)}")
