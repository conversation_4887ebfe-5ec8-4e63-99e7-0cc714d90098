#  Copyright (C) Mogo.ai Information and Technology Co.Ltd 
#
#  All rights reserved.
#
#  This document contains proprietary information belonging to Mogo.ai Co.Ltd.
#  Passing on and copying of this document, and communication of its contents
#  is not permitted without prior written authorization.
#
#
#  <AUTHOR>
#  @date 9/25/23, 3:43 PM


class SensorFactory:
    def __init__(self, task_data):
        self.task_data = task_data

    def create_sensor(self, actor_name, actor_type, parent=None):
        from api.sensors.impl.rt_sensor import CollisionSensor
        return CollisionSensor(actor_name, actor_type, parent)
