#  Copyright (C) Mogo.ai Information and Technology Co.Ltd 
#
#  All rights reserved.
#
#  This document contains proprietary information belonging to Mogo.ai Co.Ltd.
#  Passing on and copying of this document, and communication of its contents
#  is not permitted without prior written authorization.
#
#
#  <AUTHOR>
#  @date 9/25/23, 3:43 PM

from api.actor.actor_base import Actor


class SensorData:
    def __init__(self, frame=None, timestamp=None, transform=None):
        self.frame = frame
        self.timestamp = timestamp
        self.transform = transform


class Sensor:

    def __init__(self, name, model_type, parent: Actor = None):
        self.name = name
        self.id = None
        self.model_type = model_type
        self.parent = parent
        self._callback_list = []

    def listen(self, callback: callable):
        pass

    def destroy(self):
        pass


class CollisionEvent(SensorData):
    def __init__(self, timestamp, transform=None, actor: Actor = None, other_actor: Actor = None, frame=None):
        super().__init__(timestamp=timestamp, transform=transform, frame=frame)
        self.actor = actor
        self.other_actor = other_actor

