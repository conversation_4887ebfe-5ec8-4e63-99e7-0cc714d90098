#  Copyright (C) Mogo.ai Information and Technology Co.Ltd 
#
#  All rights reserved.
#
#  This document contains proprietary information belonging to Mogo.ai Co.Ltd.
#  Passing on and copying of this document, and communication of its contents
#  is not permitted without prior written authorization.
#
#
#  <AUTHOR>
#  @date 9/25/23, 8:13 PM
# !/usr/bin/env python

# Copyright (c) 2018-2020 Intel Corporation
#
# This work is licensed under the terms of the MIT license.
# For a copy, see <https://opensource.org/licenses/MIT>.

"""
This module provides all frequently used data from SIM via
local buffers to avoid blocking calls to SIM
"""

from __future__ import print_function

from api.actor.impl.carla_actor import CarlaActor
from api.dataprovider.data_provider_base import DataProvider
from utils.log import logger


class CarlaDataProvider(DataProvider):  # pylint: disable=too-many-public-methods

    """
    This class provides access to various data of all registered actors
    It buffers the data and updates it on every tick

    Currently available data:
    - Absolute velocity
    - Location
    - Transform

    Potential additions:
    - Acceleration

    In addition it provides access to the map and the transform of all traffic lights
    """

    _global_osc_parameters = {}
    _client = None
    _world = None
    _map = None
    _sync_flag = False

    @staticmethod
    def on_tick(timestamp=0.0):
        DataProvider.on_tick()
        CarlaDataProvider._time = timestamp

    @staticmethod
    def get_time():
        return CarlaDataProvider._time

    @staticmethod
    def get_all_actors():
        """
        @return all the world actors. This is an expensive call, hence why it is part of the CDP,
        but as this might not be used by everyone, only get the actors the first time someone
        calls asks for them. 'DataProvider._all_actors' is reset each tick to None.
        """
        if CarlaDataProvider._all_actors:
            return CarlaDataProvider._all_actors

        CarlaDataProvider._all_actors = CarlaDataProvider._world.get_actors()
        return CarlaDataProvider._all_actors

    @staticmethod
    def spawn_sensor(sensor_type, parent_actor: CarlaActor, relative_transform=None, sensor_params=None):
        carla_actor = CarlaDataProvider._actor_pool.get(parent_actor.id)
        if carla_actor is None:
            return None
        if relative_transform is None:
            import carla
            relative_transform = carla.Transform()
        bp_library = CarlaDataProvider._world.get_blueprint_library()
        bp = bp_library.find(sensor_type)
        if sensor_params is not None:
            for attr_name, attr_value in sensor_params.items():
                bp.set_attribute(attr_name, attr_value)
        sensor = CarlaDataProvider._world.spawn_actor(
            bp,
            relative_transform,
            attach_to=carla_actor)
        return sensor

    @staticmethod
    def cleanup():
        if CarlaDataProvider._world is not None:
            del CarlaDataProvider._world
        if CarlaDataProvider._client is not None:
            del CarlaDataProvider._client

        CarlaDataProvider._actor_velocity_map.clear()
        CarlaDataProvider._actor_location_map.clear()
        CarlaDataProvider._actor_transform_map.clear()
        CarlaDataProvider._traffic_light_map.clear()
        CarlaDataProvider._map = None
        CarlaDataProvider._world = None
        CarlaDataProvider._sync_flag = False
        CarlaDataProvider._ego_vehicle_route = None
        CarlaDataProvider._all_actors = None
        CarlaDataProvider._carla_actor_pool = {}
        CarlaDataProvider._client = None
        CarlaDataProvider._spawn_points = None
        CarlaDataProvider._spawn_index = 0
        CarlaDataProvider._grp = None
