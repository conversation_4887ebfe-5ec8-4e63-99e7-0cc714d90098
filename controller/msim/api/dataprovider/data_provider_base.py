#  Copyright (C) Mogo.ai Information and Technology Co.Ltd 
#
#  All rights reserved.
#
#  This document contains proprietary information belonging to Mogo.ai Co.Ltd.
#  Passing on and copying of this document, and communication of its contents
#  is not permitted without prior written authorization.
#
#
#  <AUTHOR>
#  @date 9/25/23, 8:13 PM


# !/usr/bin/env python


"""
This module provides all frequently used data from SIM via
local buffers to avoid blocking calls to SIM
"""

from __future__ import print_function

import math


def calculate_velocity(actor):
    """
    Method to calculate the velocity of a actor
    """
    velocity_squared = actor.get_velocity().x ** 2
    velocity_squared += actor.get_velocity().y ** 2
    return math.sqrt(velocity_squared)


class DataProvider(object):  # pylint: disable=too-many-public-methods

    """
    This class provides access to various data of all registered actors
    It buffers the data and updates it on every tick

    Currently available data:
    - Absolute velocity
    - Location
    - Transform

    Potential additions:
    - Acceleration

    In addition it provides access to the map and the transform of all traffic lights
    """

    _actor_velocity_map = {}
    _actor_location_map = {}
    _actor_transform_map = {}
    _actor_acceleration_map = {}
    _traffic_light_map = {}
    _actor_pool = {}
    _spawn_points = None
    _spawn_index = 0
    _blueprint_library = None
    _all_actors = None
    _ego_vehicle_route = None
    _traffic_manager_port = 8000
    _random_seed = 2000
    _grp = None
    _time = 0.0       # seconds

    @staticmethod
    def init():
        pass

    @staticmethod
    def register_actor(actor):
        """
        Add new actor to dictionaries
        If actor already exists, throw an exception
        """
        if actor in DataProvider._actor_velocity_map:
            raise KeyError(
                "Vehicle '{}' already registered. Cannot register twice!".format(actor.id))
        else:
            DataProvider._actor_velocity_map[actor] = 0.0

        if actor in DataProvider._actor_location_map:
            raise KeyError(
                "Vehicle '{}' already registered. Cannot register twice!".format(actor.id))
        else:
            DataProvider._actor_location_map[actor] = None

        if actor in DataProvider._actor_transform_map:
            raise KeyError(
                "Vehicle '{}' already registered. Cannot register twice!".format(actor.id))
        else:
            DataProvider._actor_transform_map[actor] = None

        if actor in DataProvider._actor_acceleration_map:
            raise KeyError(
                "Vehicle '{}' already registered. Cannot register twice!".format(actor.id))
        else:
            DataProvider._actor_acceleration_map[actor] = None

    @staticmethod
    def register_actors(actors):
        """
        Add new set of actors to dictionaries
        """
        for actor in actors:
            DataProvider.register_actor(actor)

    @staticmethod
    def on_tick(timestamp=0.0):
        DataProvider._time = timestamp

        for actor in DataProvider._actor_velocity_map:
            if actor is not None:
                DataProvider._actor_velocity_map[actor] = actor.get_current_toward_speed().x

        for actor in DataProvider._actor_location_map:
            if actor is not None:
                DataProvider._actor_location_map[actor] = actor.get_location()

        for actor in DataProvider._actor_transform_map:
            if actor is not None:
                DataProvider._actor_transform_map[actor] = actor.get_transform()

        for actor in DataProvider._actor_acceleration_map:
            if actor is not None:
                DataProvider._actor_acceleration_map[actor] = actor.get_toward_acceleration()

    @staticmethod
    def get_velocity(actor):
        """
        returns the absolute velocity for the given actor
        """
        for key in DataProvider._actor_velocity_map:
            if key.id == actor.id:
                return DataProvider._actor_velocity_map[key]

        # We are intentionally not throwing here
        # This may cause exception loops in py_trees
        return 0.0

    @staticmethod
    def get_location(actor):
        """
        returns the location for the given actor
        """
        for key in DataProvider._actor_location_map:
            if key.id == actor.id:
                return DataProvider._actor_location_map[key]

        # We are intentionally not throwing here
        # This may cause exception loops in py_trees
        return None

    @staticmethod
    def get_transform(actor):
        """
        returns the transform for the given actor
        """
        for key in DataProvider._actor_transform_map:
            if key.id == actor.id:
                return DataProvider._actor_transform_map[key]

        # We are intentionally not throwing here
        # This may cause exception loops in py_trees
        return None

    @staticmethod
    def get_acceleration(actor):
        """
        returns the transform for the given actor
        """
        for key in DataProvider._actor_acceleration_map:
            if key.id == actor.id:
                return DataProvider._actor_acceleration_map[key]

        # We are intentionally not throwing here
        # This may cause exception loops in py_trees
        return None

    @staticmethod
    def get_actor_by_id(actor_id):
        """
        Get an actor from the pool by using its ID. If the actor
        does not exist, None is returned.
        """
        if actor_id in DataProvider._actor_pool:
            return DataProvider._actor_pool[actor_id]

        return None

    @staticmethod
    def remove_actor_by_id(actor_id):
        """
        Remove an actor from the pool using its ID
        """
        if actor_id in DataProvider._actor_pool:
            DataProvider._actor_pool[actor_id].destroy()
            DataProvider._actor_pool[actor_id] = None
            DataProvider._actor_pool.pop(actor_id)

    @staticmethod
    def remove_actors_in_surrounding(location, distance):
        """
        Remove all actors from the pool that are closer than distance to the
        provided location
        """
        for actor_id in DataProvider._actor_pool.copy():
            if DataProvider._actor_pool[actor_id].get_location().distance(location) < distance:
                DataProvider._actor_pool[actor_id].destroy()
                DataProvider._actor_pool.pop(actor_id)

        # Remove all keys with None values
        DataProvider._actor_pool = dict({k: v for k, v in DataProvider._actor_pool.items() if v})

    @staticmethod
    def get_time():
        return DataProvider._time

    @staticmethod
    def cleanup():
        """
        Cleanup and remove all entries from all dictionaries
        """

        DataProvider._actor_velocity_map.clear()
        DataProvider._actor_location_map.clear()
        DataProvider._actor_transform_map.clear()
        DataProvider._traffic_light_map.clear()
        DataProvider._map = None
        DataProvider._world = None
        DataProvider._sync_flag = False
        DataProvider._ego_vehicle_route = None
        DataProvider._all_actors = None
        DataProvider._actor_pool = {}
        DataProvider._client = None
        DataProvider._spawn_points = None
        DataProvider._spawn_index = 0
        DataProvider._grp = None
