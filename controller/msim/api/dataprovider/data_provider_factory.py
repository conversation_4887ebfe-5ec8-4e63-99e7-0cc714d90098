#  Copyright (C) Mogo.ai Information and Technology Co.Ltd 
#
#  All rights reserved.
#
#  This document contains proprietary information belonging to Mogo.ai Co.Ltd.
#  Passing on and copying of this document, and communication of its contents
#  is not permitted without prior written authorization.
#
#
#  <AUTHOR>
#  @date 9/25/23, 3:43 PM
from data.task_data import TaskData


def get_data_provider(task_data: TaskData):
    """
    Get data provider
    :param task_info:
    :return:
    """
    from api.dataprovider.impl.carla_data_provider import CarlaDataProvider

    CarlaDataProvider.init()

    return CarlaDataProvider
