from api.actor.actor_base import Actor
from api.actor.actor_factory import ActorFactory
from api.dataprovider.data_provider_factory import get_data_provider
from api.param.param_manager import ParamManager
from api.process.process_manager import ProcessManager
from api.recorder.recorder_manager import RecorderManager
from api.sensors.sensor_factory import SensorFactory
from api.sync.sync_base import SyncBase
from api.sync.sync_factory import SyncFactory
from data.task_data import TaskData
from utils.log import logger


class ExecutorAPI:

    def __init__(self, task_data: TaskData):
        self._task_data = task_data

        # actor factory, use to create actor. for example, ego vehicle
        self._actor_factory = ActorFactory(task_data)
        # sensor factory. for example, collision sensor
        self._sensor_factory = SensorFactory(task_data)
        # data provider, use to get data from sim engine
        self._data_provider = get_data_provider(task_data)
        self._sync_factory = SyncFactory(task_data)
        # param manager, use to get param from task data
        self._param = ParamManager(task_data)
        # process manager, use to manage
        self._proc_manager = ProcessManager()

        # recorder manager, use to record data, lazy init
        self._recorder = None

        # reader manager, use to read data, lazy init
        self._reader = None

    def setup(self):
        """
        Setup 有些滞后的操作，比如初始化DataProvider，
        需要在仿真引擎启动之后才能进行，但有些静态参数在初始化的时候就已获取，用于区分不同模块所需的api能力
        :return:
        """
        logger.info('Start to init data provider')
        self._data_provider.init()

    @property
    def data_provider(self):
        """
        Get data provider
        :return:
        """
        return self._data_provider

    @property
    def recorder(self) -> RecorderManager:
        if self._recorder is None:
            self._recorder = RecorderManager(self._task_data)
        return self._recorder

    @property
    def reader(self):
        from api.reader.reader_manager import ReaderManager
        if self._reader is None:
            self._reader = ReaderManager(self._task_data)
        return self._reader

    def create_sensor(self, sensor_type, attach_to: Actor, **kwargs):
        """
        Create a sensor
        :param sensor_type:
        :param attach_to:
        :param kwargs:
        :return:
        """
        return self._sensor_factory.create_sensor(sensor_type, attach_to, **kwargs)

    def create_actor(self, actor_name, actor_type, parent: Actor = None):
        """
        Create an actor
        :param actor_name:
        :param actor_type:
        :param parent:
        :return:
        """
        actor = self._actor_factory.create_actor(actor_name, actor_type, parent)
        if actor:
            self._data_provider.register_actor(actor)
        return actor

    def create_sync(self) -> SyncBase:
        return self._sync_factory.create_sync()

    @property
    def param(self):
        return self._param

    @property
    def process_manager(self):
        return self._proc_manager

    def cleanup(self):
        """
        Clean up
        :return:
        """
        self._data_provider.cleanup()
        if self._recorder:
            self._recorder.cleanup()
            self._recorder = None
        self._proc_manager.destroy()
        from api.common.node import destroy_node
        destroy_node()

    def destroy(self):
        """
        Destroy
        :return:
        """
        self.cleanup()



