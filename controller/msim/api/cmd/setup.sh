#!/bin/bash
#
# Copyright (C) Mogo.ai Information and Technology Co.Ltd 
#
# All rights reserved.
#
# This document contains proprietary information belonging to Mogo.ai Co.Ltd.
# Passing on and copying of this document, and communication of its contents
# is not permitted without prior written authorization.
#
#

function setup_lib() {
  local -r ALGO_PATH=$1
  # Source setup.bash files from directories under ALGO_PATH
  while IFS= read -r dir; do
    if [ -f "$dir/setup.bash" ]; then
      source "$dir/setup.bash"
    else
      echo "File $dir/setup.bash does not exist."
    fi
  done < <(find "${ALGO_PATH}" -mindepth 1 -maxdepth 1 -type d)

  # Add paths to LD_LIBRARY_PATH
  export LD_LIBRARY_PATH="${LD_LIBRARY_PATH}:/usr/local/cuda/lib64:/opt/local/lib:${ALGO_PATH}/proto/lib:${ALGO_PATH}/pdc/pdc/third_party/lib/:${ALGO_PATH}/umap/lib:${ALGO_PATH}/udeer-common/lib"
  export PYTHONPATH=$PYTHONPATH:${ALGO_PATH}/proto/lib/python3.8.10/dist-packages
}


function setup_log() {
  # Set ROS log directory
  local -r LOG_PATH=$1

  export ROS_LOG_DIR="${LOG_PATH}"
  export GLOG_colorlogtostderr=1
  export GLOG_alsologtostderr=0
  export GLOG_logtostderr=1
  export GLOG_minloglevel=0
  export GLOG_log_dir="${LOG_PATH}"
}


function setup_env() {
  # Enable core dump
  ulimit -c 0
}

function setup() {
  setup_lib "$1"
  setup_log "$2"
  setup_env
}