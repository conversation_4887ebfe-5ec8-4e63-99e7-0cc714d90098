rosbag2_bagfile_information:
  version: 9
  storage_identifier: mcap
  duration:
    nanoseconds: 0
  starting_time:
    nanoseconds_since_epoch: 0
  message_count: 10
  topics_with_message_count:
    - topic_metadata:
        name: test_topic
        type: std_msgs/msg/String
        serialization_format: cdr
        offered_qos_profiles:
          []
        type_description_hash: ""
      message_count: 10
  compression_format: ""
  compression_mode: ""
  relative_file_paths:
    - test_recording_0.mcap
  files:
    - path: test_recording_0.mcap
      starting_time:
        nanoseconds_since_epoch: 0
      duration:
        nanoseconds: 0
      message_count: 10
  custom_data: ~
  ros_distro: jazzy