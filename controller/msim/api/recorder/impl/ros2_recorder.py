#!/usr/bin/env python3

"""
本模块提供 ROS2 消息记录功能，将 ROS2 消息记录到 rosbag 文件。
"""

import copy
import os
import threading
import time
from typing import List, Optional, Dict, Any, TypeVar, Type
from threading import RLock
from contextlib import contextmanager
from dataclasses import dataclass
from enum import Enum

from rclpy.serialization import serialize_message
from rclpy.time import Time
from rosgraph_msgs.msg import Clock

import rosbag2_py
from utils.loggers import LoggerProxy

logger = LoggerProxy("player")
from utils.compatibility.node_factory import (
    NodeFactory,
    RunEnvType,
    WriterBase,
    ReaderBase,
)
from utils.compatibility.ros_env_detector import get_message_class_ros2

T = TypeVar("T")


class StorageFormat(Enum):
    """存储格式枚举"""

    MCAP = "mcap"
    SQLITE3 = "sqlite3"


@dataclass
class TopicConfig:
    """话题配置数据类"""

    name: str
    type: str
    remap: Optional[str] = None
    qos_depth: int = 10


class ROS2Recorder:
    """
    ROS2Recorder 类用于记录 ROS2 消息到 rosbag 文件。

    参数:
        topics (Optional[List[TopicConfig]]): 要记录的主题列表
        record_path (str): rosbag 文件保存路径
        storage_format (StorageFormat): 存储格式，默认为 MCAP
    """

    def __init__(
        self,
        topics: Optional[List[TopicConfig]] = None,
        record_path: str = "",
        storage_format: StorageFormat = StorageFormat.MCAP,
        **kwargs,
    ):
        self.writer = None
        self.record_path = record_path
        self.topics = topics or []
        self.storage_format = storage_format
        self.node = None
        self.subscriptions = []
        self.clock_sub = None
        self._cur_clock = Time()
        self._write_lock = RLock()
        self._initialized = False
        self._stopped = False
        self._topic_types: Dict[str, Dict[str, Any]] = {}
        self._message_count: Dict[str, int] = {}  # 记录每个话题的消息数量

        self.__spin_thread = None

    def __enter__(self) -> "ROS2Recorder":
        """支持上下文管理器模式"""
        self.init()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb) -> None:
        """确保在退出时清理资源"""
        self.stop()

    @contextmanager
    def _write_lock_context(self):
        """提供线程安全的写入锁上下文"""
        try:
            self._write_lock.acquire()
            yield
        finally:
            self._write_lock.release()

    @property
    def cur_clock(self) -> Time:
        """线程安全的getter"""
        with self._write_lock_context():
            return self._cur_clock

    @cur_clock.setter
    def cur_clock(self, value: Time) -> None:
        """线程安全的setter"""
        with self._write_lock_context():
            self._cur_clock = value

    def init(self):
        """
        初始化 rosbag 文件和订阅者。
        """
        if self._initialized:
            logger.warning("记录器已经初始化")
            return

        if not self.record_path:
            raise ValueError("未指定bag文件路径")

        try:
            # 确保目录存在，但如果是bag目录本身，则先删除它
            bag_dir = os.path.abspath(self.record_path)
            if os.path.exists(bag_dir):
                import shutil

                logger.warning(f"删除已存在的bag目录: {bag_dir}")
                shutil.rmtree(bag_dir)

            # 创建父目录
            parent_dir = os.path.dirname(bag_dir)
            if parent_dir and not os.path.exists(parent_dir):
                os.makedirs(parent_dir, exist_ok=True)

            # 使用NodeFactory创建ROS2节点
            from rclpy.parameter import Parameter

            params = {
                "name": "ros2_recorder",
                "parameter_overrides": [Parameter(name="use_sim_time", value=True)],
            }
            self.node = NodeFactory().get_node(run_env=RunEnvType.ROS2, params=params)

            # 创建 rosbag 写入器
            self.writer = rosbag2_py.SequentialWriter()

            # 配置存储选项
            storage_options = rosbag2_py.StorageOptions(
                uri=self.record_path, storage_id=self.storage_format.value
            )

            # 配置转换选项
            converter_options = rosbag2_py.ConverterOptions("", "")

            # 打开 bag 文件
            self.writer.open(storage_options, converter_options)

            # 初始化订阅者
            self.clock_sub = self.node.create_reader(
                "/clock", self._clock_callback, msg_type=Clock
            )

            # 为每个话题创建订阅者
            for topic_config in self.topics:
                try:
                    self._init_topic_subscriber(topic_config)
                except Exception as e:
                    logger.error(f"初始化话题 {topic_config.name} 失败: {e}")
                    self._cleanup_resources()  # 清理已创建的资源
                    raise

            self._initialized = True
            self._stopped = False
            logger.info(f"记录器初始化成功，topics: {self.topics}")

        except Exception as e:
            self._cleanup_resources()
            raise RuntimeError(f"初始化失败: {e}") from e

    def _init_topic_subscriber(self, topic_config: TopicConfig) -> None:
        """
        为指定话题初始化订阅者。

        参数:
            topic_config (TopicConfig): 话题配置信息
        """
        try:
            # 获取话题类型
            topic_name = topic_config.name
            topic_type_str = topic_config.type

            # 导入消息类型
            msg_type = get_message_class_ros2(topic_type_str)
            if not msg_type:
                logger.error(f"无法导入消息类型: {topic_type_str}")
                return

            # 存储话题类型信息
            self._topic_types[topic_name] = {
                "type": topic_type_str,
                "msg_type": msg_type,
            }

            # 创建话题元数据
            topic_metadata = rosbag2_py.TopicMetadata(
                id=0, name=topic_name, type=topic_type_str, serialization_format="cdr"
            )

            # 在 bag 中创建话题
            self.writer.create_topic(topic_metadata)

            # 创建回调函数
            def callback(
                msg: Any, current_topic: TopicConfig = copy.deepcopy(topic_config)
            ) -> None:
                try:
                    logger.info(f"写入消息: {current_topic.name}, {msg}")
                    current_clock = self.cur_clock
                    with self._write_lock_context():
                        # 获取目标话题名（可能被重映射）
                        target_topic = current_topic.remap or current_topic.name
                        # 序列化消息
                        serialized_msg = serialize_message(msg)
                        # 获取时间戳
                        timestamp = 0
                        if hasattr(current_clock, "nanoseconds"):
                            timestamp = current_clock.nanoseconds
                        elif hasattr(current_clock, "to_nsec"):
                            timestamp = current_clock.to_nsec()
                        else:
                            # 尝试使用sec和nsec组合
                            timestamp = getattr(
                                current_clock, "sec", 0
                            ) * 1_000_000_000 + getattr(current_clock, "nanosec", 0)
                        logger.info(f"写入时间戳为：{timestamp}")
                        # 写入消息
                        self.writer.write(target_topic, serialized_msg, timestamp)
                        # 更新消息计数
                        self._message_count[target_topic] = (
                            self._message_count.get(target_topic, 0) + 1
                        )
                        if (
                            self._message_count[target_topic] % 1000 == 0
                        ):  # 每1000条消息记录一次
                            logger.info(
                                f"话题 {target_topic} 已记录 {self._message_count[target_topic]} 条消息"
                            )
                except Exception as e:
                    logger.error(f"写入消息失败: {str(e)}", exc_info=True)

            # 创建订阅者
            logger.info(f"为话题创建订阅者: {topic_name} 类型: {topic_type_str}")
            subscription = self.node.create_reader(
                topic_name, callback, msg_type=msg_type
            )

            # 保存订阅者引用
            self.subscriptions.append(subscription)

        except Exception as e:
            logger.error(f"创建订阅者失败 {topic_config.name}: {str(e)}", exc_info=True)
            raise

    def _clock_callback(self, msg: Clock) -> None:
        """
        处理 /clock 主题的回调函数。

        参数:
            msg (Clock): Clock 消息
        """
        # 直接使用消息中的时间戳，避免额外的获取操作
        self.cur_clock = msg.clock

    def check_bag_info(self, bag_path: Optional[str] = None) -> None:
        """
        检查并格式化输出 rosbag 文件的信息。

        参数:
            bag_path (Optional[str]): bag 文件路径，如果为 None 则使用当前实例的 record_path
        """
        path = bag_path or self.record_path
        try:
            # 使用 rosbag2_py 的 Reader 读取 bag 信息
            reader = rosbag2_py.SequentialReader()

            storage_options = rosbag2_py.StorageOptions(
                uri=path, storage_id=self.storage_format.value
            )

            converter_options = rosbag2_py.ConverterOptions("", "")

            reader.open(storage_options, converter_options)

            # 获取话题元数据
            topic_metadata = reader.get_all_topics_and_types()

            result = ["Rosbag 文件统计:"]
            result.append("=" * 50)

            # 输出话题信息
            result.append("\n话题详情:")
            result.append("-" * 50)

            for topic in topic_metadata:
                # 截断过长的话题名
                topic_display = (
                    topic.name if len(topic.name) < 40 else topic.name[:37] + "..."
                )
                result.append(f"话题: {topic_display}")
                result.append(f"  - 消息类型: {topic.type}")
                result.append(f"  - 序列化格式: {topic.serialization_format}")
                result.append(
                    f"  - 记录消息数: {self._message_count.get(topic.name, 0)}"
                )
                result.append("-" * 50)

            logger.info("\n".join(result))

        except Exception as e:
            logger.error(f"获取 bag 统计信息失败: {str(e)}", exc_info=True)

    def _cleanup_resources(self) -> None:
        """清理所有资源"""
        if not self._stopped:
            try:
                # 标记为已停止，防止重复调用
                self._stopped = True

                # 清理订阅者
                self.subscriptions.clear()
                self.clock_sub = None

                # 销毁节点
                if self.node:
                    try:
                        self.node.destroy()
                        self.node = None
                        logger.info("ROS2节点已销毁")
                    except Exception as e:
                        logger.error(f"销毁节点时发生错误: {e}")
                        self.node = None

                # 关闭bag文件
                if self.writer:
                    self.writer = None

                logger.info("recorder已退出")
            except Exception as e:
                logger.error(f"清理资源时发生错误: {e}", exc_info=True)

    def spin_once(self, timeout_sec: float = 0.1) -> None:
        """
        处理一次回调，用于非阻塞方式运行记录器。

        参数:
            timeout_sec (float): 超时时间（秒）
        """
        # 此方法不再需要，因为NodeFactory的节点会自动处理回调
        pass

    def stop(self) -> None:
        """
        停止记录并释放所有资源。
        """
        if not self._initialized:
            return

        try:
            self._cleanup_resources()
            # 在完全关闭后进行统计
            self.check_bag_info()
        except Exception as e:
            logger.error(f"停止记录失败: {e}", exc_info=True)
            raise  # 重新抛出异常，确保调用者知道发生了错误
        finally:
            self._initialized = False
            logger.info("记录器已停止并释放所有资源")


if __name__ == "__main__":
    topics = [
        TopicConfig(name="/clock", type="rosgraph_msgs/Clock"),
        TopicConfig(name="/string_topic", type="std_msgs/String"),
    ]
    record_path = "/home/<USER>/mogosim_engine/test_out2"
    recorder = ROS2Recorder(topics=topics, record_path=record_path)
    recorder.init()
    # 处理回调的循环
    end_time = time.time() + 15  # 运行5秒
    while time.time() < end_time:
        # 短暂休眠，避免CPU占用过高
        time.sleep(0.1)
        recorder.node.spin_once(timeout_sec=0.1)
    recorder.stop()
