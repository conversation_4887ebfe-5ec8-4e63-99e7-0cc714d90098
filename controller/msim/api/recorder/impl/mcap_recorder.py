# -*- coding: UTF-8 -*-
#  Copyright (C) Mogo.ai Information and Technology Co.Ltd 
#
#  All rights reserved.
#
#  This document contains proprietary information belonging to Mogo.ai Co.Ltd.
#  Passing on and copying of this document, and communication of its contents
#  is not permitted without prior written authorization.
#
#
#  <AUTHOR>
#  @date 10/11/23, 3:52 PM
import os
from typing import Optional, Iterable
from api.recorder.recorder_base import RecorderBase


class McapRecorder(RecorderBase):
    def __init__(self, output: str, topics: Optional[Iterable[str]] = None, **kwargs):
        super().__init__(output, topics, **kwargs)

    def init(self):
        pass

    def start(self):
        pass

    def stop(self):
        pass

    def __del__(self):
        pass
