import copy
import os
from typing import List, Optional
from threading import RLock

import rosbag
import rospy
from roslib.message import get_message_class
from rosgraph_msgs.msg import Clock

from utils.loggers import LoggerProxy

logger = LoggerProxy("player")


class ROS1Recorder:
    """
    ROS1Recorder 类用于记录 ROS1 消息到 rosbag 文件。

    参数:
        topics (Optional[List[dict]]): 要记录的主题列表
        record_path (str): rosbag 文件保存路径
        record_all (bool): 是否记录所有话题，如果为True，则忽略topics参数
    """

    def __init__(
        self,
        topics: Optional[List[dict]] = None,
        record_path: str = "",
        record_all: bool = False,
        **kwargs,
    ):
        self.bag = None
        self.record_path = record_path
        self.topics = topics
        self.record_all = record_all
        self.subscribers = []
        self.clock_sub = None
        self._cur_clock = rospy.Time.now()  # 使用下划线表示私有变量
        self._write_lock = RLock()
        self._initialized = False
        self._stopped = False
        self._topic_check_timer = None

    @property
    def cur_clock(self) -> rospy.Time:
        """线程安全的getter"""
        with self._write_lock:
            return self._cur_clock

    @cur_clock.setter
    def cur_clock(self, value: rospy.Time) -> None:
        """线程安全的setter"""
        with self._write_lock:
            self._cur_clock = value

    def init(self):
        """
        初始化 rosbag 文件和订阅者。
        """
        if self._initialized:
            logger.warning("记录器已经初始化")
            return

        if not self.record_path:
            raise ValueError("未指定bag文件路径")

        try:
            # 确保目录存在
            os.makedirs(
                os.path.dirname(os.path.abspath(self.record_path)), exist_ok=True
            )

            # 直接创建bag文件，不使用上下文管理器
            self.bag = rosbag.Bag(self.record_path, "w")

            # 初始化订阅者
            self.clock_sub = rospy.Subscriber("/clock", Clock, self._clock_callback)

            if self.record_all:
                # 记录所有话题
                self._init_all_topics()
            else:
                # 记录指定话题
                for pub_topic in self.topics:
                    try:
                        self._init_topic_subscriber(pub_topic)
                    except Exception as e:
                        logger.error(f"初始化话题 {pub_topic['name']} 失败: {e}")
                        self._cleanup_resources()  # 清理已创建的资源
                        raise

            self._initialized = True
            self._stopped = False
            if self.record_all:
                logger.info("记录器初始化成功，记录所有话题")
            else:
                logger.info(f"记录器初始化成功，topics: {self.topics}")

        except Exception as e:
            self._cleanup_resources()
            raise RuntimeError(f"初始化失败: {e}") from e

    def _init_all_topics(self):
        """
        初始化所有可用话题的订阅者。
        使用 rospy.get_published_topics() 获取所有已发布的话题。
        """
        try:
            # 获取所有已发布的话题
            published_topics = rospy.get_published_topics()
            logger.debug(f"发现了 {len(published_topics)} 个活跃话题")

            # 跳过 /clock 话题，因为我们已经单独订阅了它
            for topic_name, topic_type_str in published_topics:
                if topic_name == "/clock":
                    continue

                try:
                    # 创建话题配置字典
                    pub_topic = {"name": topic_name, "type": topic_type_str}

                    # 初始化该话题的订阅者
                    self._init_topic_subscriber(pub_topic)
                    logger.info(f"成功订阅话题: {topic_name} 类型: {topic_type_str}")
                except Exception as e:
                    logger.error(f"初始化话题 {topic_name} 失败: {e}")
                    # 继续处理其他话题，不中断整个过程

            # 创建一个周期性定时器，定期检查新话题
            self._topic_check_timer = rospy.Timer(
                rospy.Duration(5.0), self._check_new_topics
            )

        except Exception as e:
            logger.error(f"初始化所有话题失败: {e}", exc_info=True)
            raise

    def _check_new_topics(self, event=None):
        """
        定期检查新话题并订阅它们。
        该方法由rospy.Timer周期性调用。

        参数:
            event: rospy.TimerEvent对象，由Timer传入
        """
        if self._stopped or not self._initialized:
            return

        try:
            # 获取当前已订阅的话题列表
            subscribed_topics = set(sub.topic for sub in self.subscribers)

            # 获取所有已发布的话题
            published_topics = rospy.get_published_topics()

            # 找出新的话题
            for topic_name, topic_type_str in published_topics:
                if topic_name not in subscribed_topics and topic_name != "/clock":
                    try:
                        # 创建话题配置字典
                        pub_topic = {"name": topic_name, "type": topic_type_str}

                        # 初始化该话题的订阅者
                        self._init_topic_subscriber(pub_topic)
                        logger.info(
                            f"发现并订阅新话题: {topic_name} 类型: {topic_type_str}"
                        )
                    except Exception as e:
                        logger.error(f"初始化新话题 {topic_name} 失败: {e}")

        except Exception as e:
            logger.error(f"检查新话题失败: {e}", exc_info=True)

    def _init_topic_subscriber(self, pub_topic):
        def callback(msg, current_topic=copy.deepcopy(pub_topic)):
            try:
                current_clock = self.cur_clock  # 获取线程安全的副本
                with self._write_lock:  # 使用锁保护写入操作
                    if not self._stopped and current_clock is not None:
                        self.bag.write(
                            current_topic.get("remap", current_topic["name"]),
                            msg,
                            current_clock,
                        )  # 使用本地副本
                        logger.debug(
                            f"写入主题: {current_topic['name']} "
                            f"时间戳: {current_clock}"
                        )
                    elif self._stopped:
                        logger.debug(f"记录器已停止，跳过主题: {current_topic['name']}")
                    else:
                        # self.bag.write(
                        #     current_topic.get("remap", current_topic["name"]), msg
                        # )
                        # logger.debug(
                        #     f"写入主题: {current_topic['name']} "
                        #     f"时间戳: {msg.header.stamp}"
                        # )
                        logger.info(
                            f"写入主题: {current_topic['name']} 的current_clock不存在"
                        )
                        pass
            except Exception as e:
                logger.error(f"写入消息失败: {str(e)}", exc_info=True)

        try:
            logger.info(
                f"Creating subscriber for topic: {pub_topic['name']} "
                f"with type: {pub_topic['type']}"
            )
            self.subscribers.append(
                rospy.Subscriber(
                    pub_topic["name"],
                    get_message_class(pub_topic["type"]),
                    callback,
                    queue_size=100,
                )  # 增加队列大小，防止消息堆积
            )
        except Exception as e:
            logger.error(
                f"Error during subscriber creating {pub_topic['type']}: {str(e)}"
            )

    def _clock_callback(self, msg):
        """
        处理 /clock 主题的回调函数。

        参数:
            msg (Clock): Clock 消息
        """
        self.cur_clock = msg.clock  # 时钟回调使用setter

    def check_bag_info(self, bag_path: Optional[str] = None):
        """
        检查并格式化输出 rosbag 文件的信息。

        参数:
            bag_path (Optional[str]): bag 文件路径，如果为 None 则使用当前实例的 record_path
        """
        path = bag_path or self.record_path
        try:
            # 打开一个新的只读 bag 实例来获取统计信息
            with rosbag.Bag(path, "r") as info_bag:
                bag_info = info_bag.get_type_and_topic_info()
                total_messages = sum(
                    topic.message_count for topic in bag_info.topics.values()
                )

                result = ["Rosbag 文件统计:"]
                result.append("=" * 50)
                result.append(f"总消息数: {total_messages}")
                result.append("\n话题详情:")
                result.append("-" * 50)

                for topic_name, topic_info in bag_info.topics.items():
                    # 截断过长的话题名
                    topic_display = (
                        topic_name if len(topic_name) < 40 else topic_name[:37] + "..."
                    )
                    result.append(f"话题: {topic_display}")
                    result.append(f"  - 消息类型: {topic_info.msg_type}")
                    result.append(f"  - 消息数量: {topic_info.message_count}")
                    result.append(f"  - 发布频率: {topic_info.frequency} Hz")
                    result.append("-" * 50)

                logger.info("\n".join(result))
        except Exception as e:
            logger.error(f"获取 bag 统计信息失败: {str(e)}", exc_info=True)

    def _cleanup_resources(self):
        """清理所有资源"""
        if not self._stopped:
            try:
                # 首先设置停止标志，防止回调函数继续写入
                self._stopped = True
                
                # 清理定时器
                if self._topic_check_timer:
                    try:
                        self._topic_check_timer.shutdown()
                    except Exception as e:
                        logger.warning(f"清理定时器失败: {e}")
                    self._topic_check_timer = None

                # 清理订阅者
                for sub in self.subscribers:
                    try:
                        sub.unregister()
                    except Exception as e:
                        logger.warning(f"清理订阅者失败: {e}")
                self.subscribers.clear()

                if self.clock_sub:
                    try:
                        self.clock_sub.unregister()
                    except Exception as e:
                        logger.warning(f"清理时钟订阅者失败: {e}")
                    self.clock_sub = None

                # 关闭bag文件
                if self.bag:
                    try:
                        self.bag.close()
                    except Exception as e:
                        logger.error(f"关闭bag文件失败: {e}")
                    self.bag = None

            except Exception as e:
                logger.error(f"清理资源时发生错误: {e}", exc_info=True)

    def stop(self):
        """
        停止记录并释放所有资源。
        """
        if not self._initialized:
            return

        try:
            self._cleanup_resources()
            # 在完全关闭后进行统计
            self.check_bag_info()
        except Exception as e:
            logger.error(f"停止记录失败: {e}", exc_info=True)
        finally:
            self._initialized = False
            logger.info("Recorder stopped and all resources released")

    def __del__(self):
        """析构函数确保资源释放"""
        self.stop()
