#  Copyright (C) Mogo.ai Information and Technology Co.Ltd
#
#  All rights reserved.
#
#  This document contains proprietary information belonging to Mogo.ai Co.Ltd.
#  Passing on and copying of this document, and communication of its contents
#  is not permitted without prior written authorization.
#
#
#  <AUTHOR>
#  @date 10/11/23, 3:51 PM
import json
import time
from pathlib import Path
from typing import Tuple, Optional, Iterable, List, Dict

from api.recorder.recorder_base import RecorderBase
from data.task_data import TaskData
from data.common_options import RecordOptions
from utils.helper import dynamic_import
from utils.mcap_util import recover_sim_time, check_mcap
from utils.path import get_record_info_path, get_record_path
from utils.loggers import LoggerProxy

logger = LoggerProxy("player")


class RecorderManager:

    def __init__(self, task_data: TaskData):
        self._task_data = task_data
        self._device_id = task_data.vehicle.device_id
        self._recorder_instance: Optional[RecorderBase] = None
        self._record_options = RecordOptions()

        self._record_info_file = get_record_info_path(self._task_data.task_id)

        self._record_file = None

        self._IMPL_PATH = "api.recorder.impl"
        self.__RECORD_IMPL = "mcap_recorder.McapRecorder"

    def create_recorder(
        self, output: str, topics: Optional[Iterable[str]], *args, **kwargs
    ):
        class_name = self.__RECORD_IMPL.split(".")[-1]
        import_path = f"{self._IMPL_PATH}.{class_name}".replace(class_name, "").strip(
            "."
        )
        recorder_class = dynamic_import(import_path, class_name)
        self._recorder_instance = recorder_class(output, topics, *args, **kwargs)
        self._recorder_instance.init()
        return self._recorder_instance

    def repair_record(self, record_path: str):
        pass

    def recover_sim_time(self, source: str, target: str) -> Tuple[bool, str]:
        """
        recover mcap file from system time to sim time -> by /clock
        :param source: source mcap file
        :param target: target mcap file
        :return: success, message
        """
        res, msg = recover_sim_time(source, target)
        if res:
            self._record_options.use_case_storage = True
        return res, msg

    def use_case_storage(self) -> None:
        self._record_options.use_case_storage = True

    def get_record_mcap_path(self, timeout: int = 60) -> Optional[str]:
        if self._record_file:
            return self._record_file

        record_path = Path(get_record_path(self._task_data.task_id))
        mcap_files = list(record_path.glob("*.mcap")) if record_path.exists() else []
        if not mcap_files:
            return None
        mcap_file = mcap_files[0]
        s_t = time.time()
        while s_t + timeout > time.time():
            check_res = check_mcap(str(mcap_file))
            if check_res:
                logger.warning("Check mcap successfully")
                break
        else:
            logger.warning("Check mcap timeout, maybe still writing")
        self._record_file = str(mcap_file)
        return self._record_file

    def disable_upload(self) -> None:
        self._record_options.upload_mode = 0

    def _generate_record_info(self):
        with open(self._record_info_file, "w") as f:
            f.write(json.dumps(self._record_options.to_dict()))

    def add_source_topic_replay(self, topics: List[str]):
        self.add_source_topics_remap_replay({t: t for t in topics})

    def add_source_topics_remap_replay(self, topics: Dict[str, str]):
        self._record_options.source_topics_replay = topics

    def cleanup(self):
        if self._recorder_instance:
            self._recorder_instance.stop()
        self._generate_record_info()
