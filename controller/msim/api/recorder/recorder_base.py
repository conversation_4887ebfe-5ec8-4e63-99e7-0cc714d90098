#  Copyright (C) Mogo.ai Information and Technology Co.Ltd 
#
#  All rights reserved.
#
#  This document contains proprietary information belonging to Mogo.ai Co.Ltd.
#  Passing on and copying of this document, and communication of its contents
#  is not permitted without prior written authorization.
#
#
#  <AUTHOR>
#  @date 10/11/23, 3:52 PM
import os
from typing import Optional, Iterable


class RecorderBase:
    def __init__(self, output: str, topics: Optional[Iterable[str]] = None, **kwargs):
        self._output = output
        self._topics = topics

    def init(self):
        raise NotImplementedError

    def start(self):
        raise NotImplementedError

    def stop(self):
        raise NotImplementedError

    def __del__(self):
        pass
