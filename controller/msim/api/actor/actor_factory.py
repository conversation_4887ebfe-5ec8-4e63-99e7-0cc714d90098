#  Copyright (C) Mogo.ai Information and Technology Co.Ltd 
#
#  All rights reserved.
#
#  This document contains proprietary information belonging to Mogo.ai Co.Ltd.
#  Passing on and copying of this document, and communication of its contents
#  is not permitted without prior written authorization.
#
#
#  <AUTHOR>
#  @date 9/25/23, 3:43 PM

from data.task_data import TaskData


class ActorFactory:
    def __init__(self, task_data: 'TaskData'):
        self.task_data = task_data

    def create_actor(self, actor_name, actor_type, parent=None):
        from api.actor.impl.rt_actor import RTActor
        return RTActor(actor_name, '0', actor_type, parent, vehicle_params=self.task_data.vehicle.vehicle_params)
