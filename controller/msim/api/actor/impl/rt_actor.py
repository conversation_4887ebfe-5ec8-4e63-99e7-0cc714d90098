#  Copyright (C) Mogo.ai Information and Technology Co.Ltd 
#
#  All rights reserved.
#
#  This document contains proprietary information belonging to Mogo.ai Co.Ltd.
#  Passing on and copying of this document, and communication of its contents
#  is not permitted without prior written authorization.
#
#
#  <AUTHOR>
#  @date 9/25/23, 3:43 PM
import math
from typing import Optional

from api.actor.actor_base import Actor
from api.common.common_objects import Vector3D, Transform, Rotation
from api.common.node import get_node
from proto.localization.localization_pose_pb2 import LocalizationPose


class RTActor(Actor):
    ODOM_TOPIC = "/localization/global_odom"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._bounding_box = Vector3D(
            x=self._vehicle_params.get("length", 0.0),
            y=self._vehicle_params.get("width", 0.0),
            z=self._vehicle_params.get("height", 0.0)
        )

        self._pb_pose: Optional[LocalizationPose] = None

        def set_pb_pose(pb_pose: LocalizationPose):
            self._pb_pose = pb_pose

        self._odom_sub = get_node().create_reader(self.ODOM_TOPIC, set_pb_pose, msg_type=LocalizationPose)

    def get_acceleration(self) -> Vector3D:
        """
        Returns the actor's 3D acceleration vector
        Return: Vector3D - m/s2
        :return:
        """

        return Vector3D(
            x=self._pb_pose.pose.linear_acceleration_vrf.x,
            y=self._pb_pose.pose.linear_acceleration_vrf.y,
            z=self._pb_pose.pose.linear_acceleration_vrf.z
        ) if self._pb_pose else Vector3D()

    def get_angular_velocity(self) -> Vector3D:
        """
        Returns the actor's angular velocity vector
        Return: Vector3D - deg/s
        :return:
        """
        return Vector3D(
            x=self._pb_pose.pose.angular_velocity_vrf.x,
            y=self._pb_pose.pose.angular_velocity_vrf.y,
            z=self._pb_pose.pose.angular_velocity_vrf.z
        ) if self._pb_pose else Vector3D()

    def get_location(self) -> Vector3D:
        """
        Returns the actor's location
        Return: Vector3D - meters
        :return:
        """
        return Vector3D(
            x=self._pb_pose.pose.position.x,
            y=self._pb_pose.pose.position.y,
            z=self._pb_pose.pose.position.z
        ) if self._pb_pose else Vector3D()

    def get_transform(self) -> Transform:
        """
        Returns the actor's transform (location and rotation)  back wheels center
        :return:
        """
        location = self.get_location()
        return Transform(
            location=location,
            rotation=Rotation(
                roll=self._pb_pose.pose.euler_angles.x,
                pitch=self._pb_pose.pose.euler_angles.y,
                yaw=self._pb_pose.pose.euler_angles.z,
            ) if self._pb_pose else Rotation()
        )

    def get_velocity(self) -> Vector3D:
        """
        Returns the actor's velocity vector
        Return: Vector3D - m/s
        :return:
        """
        return Vector3D(
            x=self._pb_pose.pose.linear_velocity.x,
            y=self._pb_pose.pose.linear_velocity.y,
            z=self._pb_pose.pose.linear_velocity.z
        ) if self._pb_pose else Vector3D()

    def get_current_toward_speed(self) -> Vector3D:
        return Vector3D(
            x=self._pb_pose.pose.linear_velocity_vrf.x,
            y=self._pb_pose.pose.linear_velocity_vrf.y,
            z=self._pb_pose.pose.linear_velocity_vrf.z
        ) if self._pb_pose else Vector3D()

    def get_toward_acceleration(self) -> Vector3D:
        """
        Returns the actor's toward acceleration vector
        Return: Vector3D - m/s2
        :return:
        """
        return self.get_acceleration()
