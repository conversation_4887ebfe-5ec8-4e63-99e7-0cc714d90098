#  Copyright (C) Mogo.ai Information and Technology Co.Ltd 
#
#  All rights reserved.
#
#  This document contains proprietary information belonging to Mogo.ai Co.Ltd.
#  Passing on and copying of this document, and communication of its contents
#  is not permitted without prior written authorization.
#
#
#  <AUTHOR>
#  @date 9/25/23, 3:43 PM
import math

from api.actor.actor_base import Actor
from api.common.common_objects import Vector3D, Transform, Rotation
from api.common.transforms import vector_to_vector_rotated
from utils.log import logger


class CarlaActor(Actor):
    def __init__(self, name, model_type, parent=None, carla_actor=None):
        super().__init__(name, model_type, parent)

        if carla_actor is None:
            from api.dataprovider.impl.carla_data_provider import CarlaDataProvider
            logger.info(f"Start to wait for the carla actor: {name}")
            self._carla_actor = CarlaDataProvider.wait_for_vehicle(name)
            logger.info(f"Find carla actor: {name}")
            self._id = self._carla_actor.id
            CarlaDataProvider.register_actor(self)
            carla_v = self._carla_actor.bounding_box.extent
            self._bounding_box = Vector3D(x=carla_v.x, y=carla_v.y, z=carla_v.z)

    def get_acceleration(self) -> Vector3D:
        """
        Returns the actor's 3D acceleration vector
        Return: Vector3D - m/s2
        :return:
        """
        carla_acc = self._carla_actor.get_acceleration()
        return Vector3D(x=carla_acc.x, y=-carla_acc.y, z=carla_acc.z)

    def get_angular_velocity(self) -> Vector3D:
        """
        Returns the actor's angular velocity vector
        Return: Vector3D - deg/s
        :return:
        """
        carla_ang_v = self._carla_actor.get_angular_velocity()
        return Vector3D(x=carla_ang_v.x, y=-carla_ang_v.y, z=-carla_ang_v.z)

    def get_location(self) -> Vector3D:
        """
        Returns the actor's location
        Return: Vector3D - meters
        :return:
        """
        carla_loc = self._carla_actor.get_location()
        return Vector3D(x=carla_loc.x, y=-carla_loc.y, z=carla_loc.z)

    def get_transform(self) -> Transform:
        """
        Returns the actor's transform (location and rotation)  back wheels center
        :return:
        """
        carla_transform = self._carla_actor.get_transform()
        wheels = self._carla_actor.get_physics_control().wheels
        if len(wheels) > 3:
            carla_transform.location.x = (wheels[2].position.x + wheels[3].position.x) / 200
            carla_transform.location.y = (wheels[2].position.y + wheels[3].position.y) / 200
        else:
            carla_transform.location.x = (wheels[1].position.x + wheels[2].position.x) / 200
            carla_transform.location.y = (wheels[1].position.y + wheels[2].position.y) / 200
        pitch = -math.radians(carla_transform.rotation.pitch)
        yaw = -math.radians(carla_transform.rotation.yaw)
        roll = math.radians(carla_transform.rotation.roll)
        return Transform(location=Vector3D(x=carla_transform.location.x, y=-carla_transform.location.y, z=carla_transform.location.z),
                         rotation=Rotation(pitch=pitch, yaw=yaw, roll=roll))

    def get_velocity(self) -> Vector3D:
        """
        Returns the actor's velocity vector
        Return: Vector3D - m/s
        :return:
        """
        carla_v = self._carla_actor.get_velocity()
        return Vector3D(x=carla_v.x, y=-carla_v.y, z=carla_v.z)

    def get_current_toward_speed(self) -> Vector3D:
        velocity_v = self.get_velocity()
        transform = self.get_transform()
        return vector_to_vector_rotated(velocity_v, transform.rotation)

    def get_toward_acceleration(self) -> Vector3D:
        """
        Returns the actor's toward acceleration vector
        Return: Vector3D - m/s2
        :return:
        """
        accel = self.get_acceleration()
        transform = self.get_transform()
        return vector_to_vector_rotated(accel, transform.rotation)
