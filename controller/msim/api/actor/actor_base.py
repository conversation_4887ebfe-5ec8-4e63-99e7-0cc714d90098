#  Copyright (C) Mogo.ai Information and Technology Co.Ltd 
#
#  All rights reserved.
#
#  This document contains proprietary information belonging to Mogo.ai Co.Ltd.
#  Passing on and copying of this document, and communication of its contents
#  is not permitted without prior written authorization.
#
#
#  <AUTHOR>
#  @date 9/25/23, 3:43 PM
from abc import abstractmethod
from typing import Optional, Dict, Any

from api.common.common_objects import Vector3D, Transform


class Actor:

    def __init__(
            self,
            name: str,
            model_type: str,
            actor_id: str = '',
            parent: Optional['Actor'] = None,
            vehicle_params: Optional[Dict[str, Any]] = None
    ):
        self._name = name
        self._id = actor_id
        self._model_type = model_type
        self._parent = parent
        self._bounding_box = Vector3D()
        self._vehicle_params = vehicle_params or {}

    @property
    def name(self) -> str:
        """
        Returns the actor's name
        :return:
        """
        return self._name

    @property
    def id(self) -> str:
        """
        Returns the actor's id
        :return:
        """
        return self._id

    @property
    def model_type(self) -> str:
        """
        Returns the actor's model type
        :return:
        """
        return self._model_type

    @property
    def parent(self) -> Optional['Actor']:
        """
        Returns the actor's parent
        :return:
        """
        return self._parent

    @property
    def bounding_box(self) -> Vector3D:
        """
        Returns the actor's bounding box
        :return:
        """
        return self._bounding_box

    @abstractmethod
    def get_acceleration(self) -> Vector3D:
        """
        Returns the actor's 3D acceleration vector
        Return: Vector3D - m/s2
        :return:
        """
        raise NotImplementedError

    @abstractmethod
    def get_angular_velocity(self) -> Vector3D:
        """
        Returns the actor's angular velocity vector
        Return: Vector3D - deg/s
        :return:
        """
        raise NotImplementedError

    @abstractmethod
    def get_location(self) -> Vector3D:
        """
        Returns the actor's location
        Return: Vector3D - meters
        :return:
        """
        raise NotImplementedError

    @abstractmethod
    def get_transform(self) -> Transform:
        """
        Returns the actor's transform (location and rotation)
        :return:
        """
        raise NotImplementedError

    @abstractmethod
    def get_velocity(self) -> Vector3D:
        """
        Returns the actor's velocity vector
        Return: Vector3D - m/s
        :return:
        """
        raise NotImplementedError

    @abstractmethod
    def get_current_toward_speed(self) -> Vector3D:
        """
            Returns the actor's toward velocity vector
        Return: Vector3D - m/s
        :return:
        """
        raise NotImplementedError

    @abstractmethod
    def get_toward_acceleration(self) -> Vector3D:
        """
        Returns the actor's toward acceleration vector
        Return: Vector3D - m/s2
        :return:
        """
        raise NotImplementedError
