#  Copyright (C) Mogo.ai Information and Technology Co.Ltd 
#
#  All rights reserved.
#
#  This document contains proprietary information belonging to Mogo.ai Co.Ltd.
#  Passing on and copying of this document, and communication of its contents
#  is not permitted without prior written authorization.
#
#
#  <AUTHOR>
#  @date 9/26/23, 2:11 PM
from typing import Dict, Any, Optional

from utils.compatibility.node_factory import NodeFactory, NodeBase


def get_node(params: Optional[Dict[str, Any]] = None) -> NodeBase:
    return NodeFactory().get_node(params=params)


def destroy_node():
    NodeFactory().destroy()
