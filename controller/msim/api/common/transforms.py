import numpy as np

from api.common.common_objects import Rotation, Vector3D


def rotation_to_numpy_rotation_matrix(rotation: Rotation) -> np.array:
    from transforms3d.euler import euler2mat
    numpy_array = euler2mat(rotation.roll, rotation.pitch, rotation.yaw)
    rotation_matrix = numpy_array[:3, :3]
    return rotation_matrix


def vector_to_vector_rotated(vector: Vector3D, rotation: Rotation):
    """
    Rotate vector
    """
    rotation_matrix = rotation_to_numpy_rotation_matrix(rotation)
    tmp_array = rotation_matrix.dot(np.array([vector.x, vector.y, vector.z]))
    return Vector3D(x=tmp_array[0], y=tmp_array[1], z=tmp_array[2])
