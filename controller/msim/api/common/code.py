from typing import Type, Tuple

from google.protobuf.message import Message

"""
NOTE: All prefix should be small endian
---------------------------------------------------------------------------------------------------------------------------------
|      CDR Header     | Timestamp |   Type string size prefix   |Type string|    Filling    |Data string size prefix|Data string|
| 0x00 0x01 0x00 0x00 |  8 bytes  |           4 bytes           |           |  to 4x bytes  |        4 bytes        |           |
---------------------------------------------------------------------------------------------------------------------------------
"""

CDR_HEADER_SIZE = 4
CDR_HEADER_BYTES = b'\x00\x01\x00\x00'
TIME_SIZE = 8
TYPE_PREFIX_SIZE = 4
TYPE_BEGIN = 16
DATA_PREFIX_SIZE = 4
FILLING_SIZE = 4
FILLING_BYTES = b'\x00'


def get_content_from_bytes(cdr_bytes: bytes, pb_type: Type[Message]) -> Tuple[int, str, Message]:
    timestamp = int.from_bytes(cdr_bytes[CDR_HEADER_SIZE:CDR_HEADER_SIZE + TIME_SIZE], byteorder='little', signed=False)
    type_size = int.from_bytes(
        cdr_bytes[CDR_HEADER_SIZE + TIME_SIZE:TYPE_BEGIN], byteorder='little', signed=False)
    type_name = cdr_bytes[TYPE_BEGIN:TYPE_BEGIN + type_size - 1].decode('utf-8')
    content_begin = (-(TYPE_BEGIN + type_size) % FILLING_SIZE) + (
            TYPE_BEGIN + type_size) + DATA_PREFIX_SIZE
    data_size = int.from_bytes(cdr_bytes[content_begin - DATA_PREFIX_SIZE:content_begin], byteorder='little', signed=False)
    pb_msg = pb_type()
    pb_msg.ParseFromString(cdr_bytes[content_begin:content_begin + data_size])
    return timestamp, type_name, pb_msg


def get_cdr_bytes_from_content(timestamp: int, pb_type: str, pb_msg: bytes) -> bytes:
    # c string ends with '\x00'
    type_size = len(pb_type) + 1
    content_size = len(pb_msg)
    cdr_bytes = \
        CDR_HEADER_BYTES + timestamp.to_bytes(TIME_SIZE, byteorder='little', signed=False) + \
        type_size.to_bytes(TYPE_PREFIX_SIZE, byteorder='little', signed=False) + pb_type.encode('utf-8') + b'\x00' + \
        FILLING_BYTES * (-(TYPE_BEGIN + type_size) % FILLING_SIZE) + \
        content_size.to_bytes(DATA_PREFIX_SIZE, byteorder='little', signed=False) + pb_msg
    return cdr_bytes
