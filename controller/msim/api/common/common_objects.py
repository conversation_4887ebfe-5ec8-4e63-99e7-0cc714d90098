#  Copyright (C) Mogo.ai Information and Technology Co.Ltd 
#
#  All rights reserved.
#
#  This document contains proprietary information belonging to Mogo.ai Co.Ltd.
#  Passing on and copying of this document, and communication of its contents
#  is not permitted without prior written authorization.
#
#
#  <AUTHOR>
#  @date 9/25/23, 3:41 PM


class Vector2D:
    def __init__(self, x=0.0, y=0.0):
        self.x = x
        self.y = y


class Vector3D:
    def __init__(self, x=0.0, y=0.0, z=0.0):
        self.x = x or 0.0
        self.y = y or 0.0
        self.z = z or 0.0

    def distance(self, other):
        return ((self.x - other.x)**2 + (self.y - other.y)**2 + (self.z - other.z)**2)**0.5

    def length(self):
        return (self.x**2 + self.y**2 + self.z**2)**0.5

    def __add__(self, other):
        return Vector3D(self.x + other.x, self.y + other.y, self.z + other.z)

    def __sub__(self, other):
        return Vector3D(self.x - other.x, self.y - other.y, self.z - other.z)

    def __eq__(self, other):
        return self.x == other.x and self.y == other.y and self.z == other.z


class Rotation:
    def __init__(self, pitch=0.0, yaw=0.0, roll=0.0):
        self.pitch = pitch or 0.0
        self.yaw = yaw or 0.0
        self.roll = roll or 0.0

    def to_quaternion(self):
        """
        Convert the rotation to quaternion
        :return: List[float] - [w, x, y, z]
        """
        from transforms3d.euler import euler2quat
        return euler2quat(self.roll, self.pitch, self.yaw)

    def __eq__(self, other):
        return self.pitch == other.pitch and self.yaw == other.yaw and self.roll == other.roll


class Transform:
    def __init__(self, location=Vector3D(), rotation=Rotation()):
        self.location = location
        self.rotation = rotation
