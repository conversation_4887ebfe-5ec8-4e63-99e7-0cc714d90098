#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from utils.loggers import LoggerProxy

logger = LoggerProxy()
import yaml
import os


class EngineSimulationConfig:
    """
    负责加载和管理所有仿真所需的配置。
    """

    def __init__(self, config_file_path=None):
        """
        初始化配置加载器

        参数:
            config_file_path (str, optional): 配置文件路径。如果未提供，将在 engine/dp_logsim 目录查找默认配置文件
        """
        self.config_file_path = config_file_path
        self.main_config_data = self._load_main_config_file()
        logger.info("EngineSimulationConfig 初始化完成。")

    def _load_main_config_file(self) -> dict:
        """
        加载主仿真配置文件 (pdp_logsim_config.yaml)。
        """
        logger.info("开始加载主仿真配置文件...")
        try:
            config_file_name = "pdp_logsim_config.yaml"
            
            if self.config_file_path:
                # 如果提供了配置文件路径，直接使用
                config_path = self.config_file_path
            else:
                # 否则在 engine/dp_logsim 目录中查找配置文件
                # 从当前文件位置向上查找到项目根目录，然后定位到 engine/dp_logsim
                current_dir = os.path.dirname(__file__)
                project_root = current_dir
                # 向上查找直到找到包含 engine 目录的根目录
                while project_root and not os.path.exists(os.path.join(project_root, "engine")):
                    parent = os.path.dirname(project_root)
                    if parent == project_root:  # 已经到达文件系统根目录
                        break
                    project_root = parent

                if os.path.exists(os.path.join(project_root, "engine")):
                    config_path = os.path.join(project_root, "engine", "dp_logsim", config_file_name)
                else:
                    # 如果找不到项目根目录，尝试相对路径
                    config_path = os.path.join(current_dir, "..", "..", "..", "..", "engine", "dp_logsim", config_file_name)
                    config_path = os.path.abspath(config_path)

            if not os.path.exists(config_path):
                logger.error(f"主配置文件未找到: {config_path}")
                raise FileNotFoundError(f"主配置文件未找到: {config_path}")

            with open(config_path, "r") as f:
                config_data = yaml.safe_load(f)
            logger.info(f"主配置文件加载成功: {config_path}")
            return config_data if config_data else {}
        except Exception as e:
            logger.error(f"加载主配置文件失败: {e}", exc_info=True)
            raise

    def get_map_config(self) -> dict:
        """
        获取MAP配置。
        所有MAP相关配置均从主配置文件 (pdp_logsim_config.yaml) 中的 "map" 部分加载。
        """
        return self.main_config_data.get("map", {})

    def get_lgsvl_config(self) -> dict:
        """获取LGSVL相关配置。"""
        return self.main_config_data.get("lgsvl", {})

    def get_rosbag_config(self) -> dict:
        """获取播包相关配置。"""
        return self.main_config_data.get("rosbag", {})


if __name__ == "__main__":
    logger.info("测试 EngineSimulationConfig...")
    try:
        config = EngineSimulationConfig()
        logger.info(f"MAP Config: {config.get_map_config()}")
        logger.info(f"LGSVL Config: {config.get_lgsvl_config()}")
        logger.info(f"Rosbag Config: {config.get_rosbag_config()}")
    except Exception as e:
        logger.error(f"EngineSimulationConfig 测试失败: {e}", exc_info=True)
