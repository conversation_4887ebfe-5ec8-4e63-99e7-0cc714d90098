#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
PDPC 共享模块

这个包包含了 dp_logsim 和 pdp_logsim 之间共享的通用功能，
包括各种处理器类、配置管理和工具函数。

主要组件：
- BaseHandler: 所有处理器的基类
- MapHandler: 地图服务处理器
- LgsvlHandler: LGSVL仿真处理器  
- RosbagHandler: 播包处理器
- EngineSimulationConfig: 配置管理器
"""

__version__ = "1.0.0"
__author__ = "MogoSim Team"

# 导入主要的类，方便外部使用
try:
    from .base_handler import BaseHandler
    from .map_handler import MapHandler
    from .lgsvl_handler import LgsvlHandler
    from .rosbag_handler import RosbagHandler
    from .engine_config import EngineSimulationConfig
    
    __all__ = [
        'BaseHandler',
        'MapHandler', 
        'LgsvlHandler',
        'RosbagHandler',
        'EngineSimulationConfig'
    ]
except ImportError:
    # 如果导入失败，至少确保包可以被导入
    __all__ = []
