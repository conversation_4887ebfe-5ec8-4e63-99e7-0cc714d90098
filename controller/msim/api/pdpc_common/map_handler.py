#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import subprocess
import time
import json
from base_handler import BaseHandler
from typing import Optional

from utils.path import get_task_log_path, get_task_record_path, get_task_vehicle_path, get_tasks_info


class MapHandler(BaseHandler):
    """处理MAP算法镜像和节点的启动与清理。"""

    def __init__(
        self,
        config: dict,
        logger_instance=None,
        container_id: Optional[str] = None,
        case_id: Optional[str] = None,
        case_path: Optional[str] = None,
    ):
        super().__init__(
            component_name="MAP服务",
            config=config,
            logger_instance=logger_instance,
        )
        self.container_id = container_id
        self.running_processes = {}  # 记录运行中的进程信息
        self.case_id = case_id
        self.case_path = case_path

    def _get_vehicle_type_mapping(self, vehicle_code: str) -> str:
        """
        根据车辆代码获取对应的D_CAR_TYPE值
        
        参数:
            vehicle_code (str): 车辆代码，如 "JL", "HQ", "DF"
            
        返回:
            str: 对应的D_CAR_TYPE值
        """
        # 车辆类型映射
        vehicle_type_mapping = {
            "JL": "jinlv",
            "HQ": "hq", 
            "DF": "df"
        }
        
        return vehicle_type_mapping.get(vehicle_code.upper(), "jinlv")  # 默认返回jinlv

    def _load_vehicle_from_tasks_info(self) -> str:
        """
        从 tasks_info.json 文件中读取 vehicle 字段值
        
        返回:
            str: vehicle 字段的值，如果读取失败则返回默认值 "JL"
        """
        try:
            tasks_info_path = get_tasks_info()
            self.logger.info(f"[{self.component_name}] 尝试从 {tasks_info_path} 读取 vehicle 字段")

            if not os.path.exists(tasks_info_path):
                self.logger.warning(f"[{self.component_name}] tasks_info.json 文件不存在: {tasks_info_path}")
                return "JL"

            with open(tasks_info_path, "r", encoding="utf-8") as f:
                tasks_data = json.load(f)

            # 首先尝试读取根级别的 vehicle 字段
            vehicle_value = tasks_data.get("vehicle")
            if vehicle_value:
                self.logger.info(f"[{self.component_name}] 从根级别读取到 vehicle: {vehicle_value}")
                return str(vehicle_value)

            # 如果没有 vehicle 字段，尝试从 caseList 中的第一个 case 的 vehicle_config 读取
            case_list = tasks_data.get("caseList", [])
            if case_list and isinstance(case_list, list) and len(case_list) > 0:
                first_case = case_list[0]
                vehicle_config = first_case.get("vehicle_config")
                if vehicle_config:
                    self.logger.info(f"[{self.component_name}] 从第一个case的vehicle_config读取到: {vehicle_config}")
                    return str(vehicle_config)

            # 如果都没有找到，使用默认值
            self.logger.warning(f"[{self.component_name}] 未找到 vehicle 或 vehicle_config 字段，使用默认值 'JL'")
            return "JL"

        except json.JSONDecodeError as e:
            self.logger.error(f"[{self.component_name}] 解析 tasks_info.json 文件失败: {e}")
            return "JL"
        except Exception as e:
            self.logger.error(f"[{self.component_name}] 读取 tasks_info.json 时发生错误: {e}", exc_info=True)
            return "JL"

    def setup(self) -> bool:
        self.logger.info(f"[{self.component_name}] 开始设置...")

        if not self.container_id:
            self.logger.warning(
                f"[{self.component_name}] 未提供 container_id，将跳过所有MAP节点启动。"
            )
            self.initialized_successfully = True
            return True

        self.logger.info(f"[{self.component_name}] 使用容器 ID: {self.container_id}")

        if not self.config:
            self.logger.info(f"[{self.component_name}] 无MAP节点配置，跳过节点启动。")
            self.initialized_successfully = True
            return True

        if not isinstance(self.config, dict):
            self.logger.error(
                f"[{self.component_name}] MAP配置格式错误，应为字典，实际为: {type(self.config)}"
            )
            return False

        # 在启动节点之前先复制hadmap数据库文件（失败不影响后续流程）
        if not self._copy_hadmap_database():
            self.logger.warning(
                f"[{self.component_name}] hadmap数据库文件复制失败，但继续执行后续流程"
            )

        # 在启动节点之前进行车辆配置准备工作
        if not self._prepare_vehicle_config():
            self.logger.error(f"[{self.component_name}] 车辆配置准备失败")
            return False

        # 车辆配置准备完成后，设置ROS参数
        if not self._setup_ros_parameters():
            self.logger.error(f"[{self.component_name}] ROS参数设置失败")
            return False

        for node_name, command_in_yaml in self.config.items():
            self.logger.info(f"启动节点：{node_name}，启动指令：{command_in_yaml}")
            # 创建日志目录并启动节点，将日志重定向到容器内的文件
            log_dir = "/tmp/map_logs"
            log_file = f"{log_dir}/{node_name}.log"

            # 步骤1：先创建日志目录
            mkdir_cmd = [
                "docker",
                "exec",
                self.container_id,
                "sh",
                "-c",
                f"mkdir -p {log_dir}",
            ]

            try:
                mkdir_result = subprocess.run(
                    mkdir_cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    timeout=10,
                )

                if mkdir_result.returncode != 0:
                    self.logger.error(f"创建日志目录失败: {mkdir_result.stderr}")
                    continue
                else:
                    self.logger.info(f"日志目录创建成功: {log_dir}")

                # 步骤2：启动节点（使用nohup确保持续运行，并使用脚本包装确保日志正确输出）
                # 创建启动脚本确保进程持续运行
                
                # 获取车辆类型信息
                vehicle_code = self._load_vehicle_from_tasks_info()
                d_car_type = self._get_vehicle_type_mapping(vehicle_code)
                self.logger.info(f"[{self.component_name}] 车辆代码: {vehicle_code}, D_CAR_TYPE: {d_car_type}")
                
                script_content = f"""#!/bin/bash
# 设置错误处理 - 不立即退出，但记录错误
set +e  # 不要立即退出
export ROS_MASTER_URI=http://localhost:11311

# 设置车辆类型相关环境变量
export D_CAR_TYPE={d_car_type}
export JINLV_SUBTYPE=

# 输出到日志文件的函数
log() {{
    echo "$(date): $1" | tee -a {log_file}
}}

# 开始启动节点
log "开始启动节点 {node_name}"
log "执行命令: {command_in_yaml}"

# 执行命令前先检查环境
log "当前工作目录: $(pwd)"
log "ROS_MASTER_URI: $ROS_MASTER_URI"
log "D_CAR_TYPE: $D_CAR_TYPE"
log "JINLV_SUBTYPE: $JINLV_SUBTYPE"

# 将复合命令分解并逐步执行
{self._generate_command_steps(command_in_yaml, log_file)}

# 如果执行到这里说明命令完成或失败
log "节点 {node_name} 执行完成或退出"
"""

                script_path = f"{log_dir}/{node_name}_start.sh"

                # 将启动脚本写入容器
                write_script_cmd = [
                    "docker",
                    "exec",
                    self.container_id,
                    "sh",
                    "-c",
                    f"cat > {script_path} << 'EOF'\n{script_content}EOF",
                ]

                script_result = subprocess.run(
                    write_script_cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    timeout=10,
                )

                if script_result.returncode != 0:
                    self.logger.error(f"创建启动脚本失败: {script_result.stderr}")
                    continue

                # 给脚本添加执行权限
                chmod_cmd = [
                    "docker",
                    "exec",
                    self.container_id,
                    "chmod",
                    "+x",
                    script_path,
                ]

                subprocess.run(chmod_cmd, timeout=10)

                # 使用nohup在后台运行脚本
                start_command = f"nohup {script_path} >/dev/null 2>&1 & echo $!"
                start_cmd = [
                    "docker",
                    "exec",
                    self.container_id,
                    "sh",
                    "-c",
                    start_command,
                ]

                self.logger.info(f"执行启动命令: {' '.join(start_cmd)}")
                start_result = subprocess.run(
                    start_cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    timeout=30,
                )

                if start_result.returncode == 0:
                    pid = start_result.stdout.strip()
                    self.logger.info(
                        f"已启动节点 {node_name}，PID: {pid}，日志位于容器内: {log_file}"
                    )

                    # 记录进程信息
                    self.running_processes[node_name] = {
                        "pid": pid,
                        "log_file": log_file,
                        "script_path": script_path,
                    }

                    # 等待一小段时间后检查进程是否还在运行
                    time.sleep(2)
                    if self._check_process_running(node_name, pid):
                        self.logger.info(f"节点 {node_name} 正在正常运行")
                    else:
                        self.logger.warning(f"节点 {node_name} 可能已退出，请检查日志")

                else:
                    self.logger.error(
                        f"启动节点 {node_name} 失败，返回码: {start_result.returncode}"
                    )
                    if start_result.stderr:
                        self.logger.error(
                            f"节点 {node_name} 错误输出: {start_result.stderr.strip()}"
                        )

            except subprocess.TimeoutExpired:
                self.logger.error(f"启动节点 {node_name} 超时")
            except Exception as e:
                self.logger.error(f"启动节点 {node_name} 失败: {e}")

        self.initialized_successfully = True
        self.logger.info(
            f"[{self.component_name}] 所有配置的MAP节点启动尝试完成，设置成功。"
        )
        return True

    def _generate_command_steps(self, command: str, log_file: str) -> str:
        """
        将复合命令分解为多个步骤，确保每个步骤都有详细的日志记录
        """
        steps = []

        # 检查是否包含 source 命令
        if "source " in command and "&&" in command:
            # 分解 source 和后续命令
            parts = command.split("&&")

            for i, part in enumerate(parts):
                part = part.strip()

                if part.startswith("source "):
                    # 处理 source 命令
                    source_file = part.replace("source ", "").strip()
                    steps.append(
                        f"""
# 步骤{i+1}: 执行source命令
log "步骤{i+1}: 执行source {source_file}"
if [ -f "{source_file}" ]; then
    log "找到source文件: {source_file}"
    source {source_file}
    if [ $? -eq 0 ]; then
        log "source {source_file} 执行成功"
    else
        log "source {source_file} 执行失败，退出码: $?"
        exit 1
    fi
else
    log "错误: source文件不存在: {source_file}"
    exit 1
fi
"""
                    )
                elif part.startswith("roslaunch "):
                    # 处理 roslaunch 命令
                    steps.append(
                        f"""
# 步骤{i+1}: 执行roslaunch命令
log "步骤{i+1}: 执行 {part}"
log "检查ROS环境..."
if [ -z "$ROS_DISTRO" ]; then
    log "警告: ROS_DISTRO 环境变量未设置"
else
    log "ROS_DISTRO: $ROS_DISTRO"
fi

# 检查roslaunch是否可用
if ! command -v roslaunch &> /dev/null; then
    log "错误: roslaunch 命令不可用"
    exit 1
fi

log "开始执行roslaunch..."
# 使用exec确保roslaunch成为主进程，但保持日志输出
exec {part} 2>&1 | while IFS= read -r line; do
    echo "$(date): [roslaunch] $line" >> {log_file}
done
"""
                    )
                else:
                    # 处理其他命令
                    steps.append(
                        f"""
# 步骤{i+1}: 执行命令 {part}
log "步骤{i+1}: 执行 {part}"
{part} 2>&1 | while IFS= read -r line; do
    echo "$(date): [${{part%% *}}] $line" >> {log_file}
done
if [ ${{PIPESTATUS[0]}} -ne 0 ]; then
    log "命令执行失败: {part}"
    exit 1
fi
"""
                    )
        else:
            # 简单命令，直接执行
            steps.append(
                f"""
log "执行单一命令: {command}"
{command} 2>&1 | while IFS= read -r line; do
    echo "$(date): [cmd] $line" >> {log_file}
done
if [ ${{PIPESTATUS[0]}} -ne 0 ]; then
    log "命令执行失败: {command}"
    exit 1
fi
"""
            )

        return "\n".join(steps)

    def _find_planning_config_folder(self) -> str:
        """
        在容器的/root/vehicle目录中寻找planning_config.txt文件所在的文件夹

        返回:
            str: planning_config.txt所在的文件夹路径，如果未找到返回空字符串
        """
        try:
            # 使用find命令在容器中搜索planning_config.txt文件
            find_cmd = [
                "docker",
                "exec",
                self.container_id,
                "find",
                "/root/vehicle",
                "-name",
                "planning_config.txt",
                "-type",
                "f",
            ]

            self.logger.info(
                f"[{self.component_name}] 搜索planning_config.txt文件: {' '.join(find_cmd)}"
            )
            find_result = subprocess.run(
                find_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                timeout=30,
            )

            if find_result.returncode != 0:
                self.logger.error(
                    f"[{self.component_name}] 搜索planning_config.txt失败: {find_result.stderr}"
                )
                return ""

            # 解析搜索结果
            output_lines = find_result.stdout.strip().split("\n")
            valid_files = [line.strip() for line in output_lines if line.strip()]

            self.logger.info(
                f"[{self.component_name}] 找到planning_config.txt文件: {valid_files}"
            )

            if not valid_files:
                self.logger.error(
                    f"[{self.component_name}] 未找到planning_config.txt文件"
                )
                return ""

            if len(valid_files) > 1:
                self.logger.warning(
                    f"[{self.component_name}] 找到多个planning_config.txt文件，使用第一个: {valid_files[0]}"
                )

            # 获取文件所在的目录
            config_file_path = valid_files[0]
            config_folder_path = os.path.dirname(config_file_path)

            self.logger.info(
                f"[{self.component_name}] planning_config.txt所在文件夹: {config_folder_path}"
            )
            return config_folder_path

        except subprocess.TimeoutExpired:
            self.logger.error(f"[{self.component_name}] 搜索planning_config.txt超时")
            return ""
        except Exception as e:
            self.logger.error(
                f"[{self.component_name}] 搜索planning_config.txt失败: {e}"
            )
            return ""

    def _prepare_vehicle_config(self) -> bool:
        """
        准备车辆配置，包括：
        1. 拷贝vehicle目录到容器中
        2. 寻找planning_config.txt文件所在的文件夹
        3. 创建软链接
        4. 设置环境变量（已在启动脚本中处理）

        返回:
            bool: 准备成功返回True，失败返回False
        """
        try:
            # 步骤1：获取vehicle目录路径
            vehicle_path = get_task_vehicle_path(self.case_id)
            self.logger.info(
                f"[{self.component_name}] 获取到vehicle目录路径: {vehicle_path}"
            )

            # 检查目录是否存在
            if not os.path.exists(vehicle_path):
                self.logger.error(
                    f"[{self.component_name}] vehicle目录不存在: {vehicle_path}"
                )
                return False

            if not os.path.isdir(vehicle_path):
                self.logger.error(
                    f"[{self.component_name}] vehicle路径不是目录: {vehicle_path}"
                )
                return False

            # 步骤2：将vehicle目录拷贝到容器中
            container_vehicle_base_path = "/root/vehicle"

            # 先在容器中创建目标目录的父目录
            mkdir_cmd = ["docker", "exec", self.container_id, "mkdir", "-p", "/root"]

            mkdir_result = subprocess.run(
                mkdir_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                timeout=10,
            )

            if mkdir_result.returncode != 0:
                self.logger.error(
                    f"[{self.component_name}] 创建容器目录失败: {mkdir_result.stderr}"
                )
                return False

            # 拷贝整个vehicle目录到容器中
            copy_cmd = [
                "docker",
                "cp",
                vehicle_path,
                f"{self.container_id}:{container_vehicle_base_path}",
            ]

            self.logger.info(
                f"[{self.component_name}] 执行拷贝命令: {' '.join(copy_cmd)}"
            )
            copy_result = subprocess.run(
                copy_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                timeout=60,  # 拷贝可能需要较长时间
            )

            if copy_result.returncode != 0:
                self.logger.error(
                    f"[{self.component_name}] 拷贝vehicle目录失败: {copy_result.stderr}"
                )
                return False

            self.logger.info(
                f"[{self.component_name}] vehicle目录拷贝成功: {vehicle_path} -> {container_vehicle_base_path}"
            )

            # 步骤3：寻找planning_config.txt文件所在的文件夹
            container_vehicle_path = self._find_planning_config_folder()
            if not container_vehicle_path:
                self.logger.error(
                    f"[{self.component_name}] 未找到planning_config.txt文件所在的文件夹，无法创建软链接"
                )
                return False

            self.logger.info(
                f"[{self.component_name}] 将使用文件夹作为软链接源: {container_vehicle_path}"
            )

            # 步骤4：创建软链接
            link_target_dir = "/autocar-code/install/share/config"

            # 先确保目标目录存在
            mkdir_link_dir_cmd = [
                "docker",
                "exec",
                self.container_id,
                "mkdir",
                "-p",
                link_target_dir,
            ]

            mkdir_link_result = subprocess.run(
                mkdir_link_dir_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                timeout=10,
            )

            if mkdir_link_result.returncode != 0:
                self.logger.error(
                    f"[{self.component_name}] 创建链接目标目录失败: {mkdir_link_result.stderr}"
                )
                return False

            # 创建软链接，将vehicle文件夹链接为vehicle名称
            vehicle_link_path = f"{link_target_dir}/vehicle"
            link_cmd = [
                "docker",
                "exec",
                self.container_id,
                "sudo",
                "ln",
                "-sf",
                container_vehicle_path,
                vehicle_link_path,
            ]

            self.logger.info(
                f"[{self.component_name}] 执行软链接命令: {' '.join(link_cmd)}"
            )
            link_result = subprocess.run(
                link_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                timeout=10,
            )

            if link_result.returncode != 0:
                self.logger.error(
                    f"[{self.component_name}] 创建软链接失败: {link_result.stderr}"
                )
                return False

            self.logger.info(
                f"[{self.component_name}] 软链接创建成功: {container_vehicle_path} -> {vehicle_link_path}"
            )

            # 验证软链接是否创建成功
            verify_cmd = [
                "docker",
                "exec",
                self.container_id,
                "ls",
                "-la",
                vehicle_link_path,
            ]

            verify_result = subprocess.run(
                verify_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                timeout=10,
            )

            if verify_result.returncode == 0:
                self.logger.info(
                    f"[{self.component_name}] 软链接验证成功: {verify_result.stdout.strip()}"
                )
            else:
                self.logger.warning(
                    f"[{self.component_name}] 软链接验证失败: {verify_result.stderr}"
                )

            self.logger.info(f"[{self.component_name}] 车辆配置准备完成")
            return True

        except subprocess.TimeoutExpired:
            self.logger.error(f"[{self.component_name}] 车辆配置准备超时")
            return False
        except Exception as e:
            self.logger.error(f"[{self.component_name}] 车辆配置准备失败: {e}")
            return False

    def _check_process_running(self, node_name: str, pid: str) -> bool:
        """检查进程是否还在运行"""
        try:
            check_cmd = [
                "docker",
                "exec",
                self.container_id,
                "sh",
                "-c",
                f'ps -p {pid} > /dev/null 2>&1 && echo "running" || echo "stopped"',
            ]

            result = subprocess.run(
                check_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                timeout=10,
            )

            if result.returncode == 0 and result.stdout.strip() == "running":
                return True
            else:
                return False

        except Exception as e:
            self.logger.error(f"检查进程 {node_name} 状态失败: {e}")
            return False

    def get_running_processes_status(self) -> dict:
        """获取所有运行中进程的状态"""
        status = {}
        for node_name, process_info in self.running_processes.items():
            pid = process_info["pid"]
            is_running = self._check_process_running(node_name, pid)
            status[node_name] = {
                "pid": pid,
                "running": is_running,
                "log_file": process_info["log_file"],
            }
        return status

    def cleanup(self) -> bool:
        self.logger.info(f"[{self.component_name}] 开始清理...")
        self.logger.info(
            f"[{self.component_name}] 当前记录的运行进程数量: {len(self.running_processes)}"
        )
        self.logger.info(f"[{self.component_name}] container_id: {self.container_id}")
        self.logger.info(f"[{self.component_name}] case_id: {self.case_id}")

        # 首先停止所有运行中的进程
        for node_name, process_info in self.running_processes.items():
            pid = process_info["pid"]
            try:
                # 尝试优雅地停止进程
                kill_cmd = [
                    "docker",
                    "exec",
                    self.container_id,
                    "sh",
                    "-c",
                    f"kill {pid} 2>/dev/null || true",
                ]

                subprocess.run(kill_cmd, timeout=10)
                self.logger.info(f"已停止节点 {node_name} (PID: {pid})")

                # 等待一下再强制杀死（如果需要）
                time.sleep(2)

                # 强制杀死进程（如果还在运行）
                force_kill_cmd = [
                    "docker",
                    "exec",
                    self.container_id,
                    "sh",
                    "-c",
                    f"kill -9 {pid} 2>/dev/null || true",
                ]

                subprocess.run(force_kill_cmd, timeout=10)

            except Exception as e:
                self.logger.error(f"停止节点 {node_name} 时出错: {e}")

        # 移动日志
        self.logger.info(f"[{self.component_name}] 开始移动日志文件...")
        all_cleaned = True

        if self.container_id:
            log_dir = "/tmp/map_logs"
            dest_dir = get_task_record_path(self.case_id)

            self.logger.info(f"[{self.component_name}] 日志源目录: {log_dir}")
            self.logger.info(f"[{self.component_name}] 日志目标目录: {dest_dir}")

            # 确保目标目录存在
            os.makedirs(dest_dir, exist_ok=True)
            self.logger.info(f"[{self.component_name}] 目标目录已创建或确认存在")

            try:
                # 获取容器中的所有日志文件
                list_cmd = ["docker", "exec", self.container_id, "ls", log_dir]
                self.logger.info(
                    f"[{self.component_name}] 执行命令: {' '.join(list_cmd)}"
                )
                result = subprocess.run(
                    list_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True
                )

                self.logger.info(
                    f"[{self.component_name}] ls命令返回码: {result.returncode}"
                )
                if result.stdout:
                    self.logger.info(
                        f"[{self.component_name}] ls命令输出: {result.stdout}"
                    )
                if result.stderr:
                    self.logger.info(
                        f"[{self.component_name}] ls命令错误输出: {result.stderr}"
                    )

                if result.returncode != 0:
                    self.logger.error(f"无法列出容器中的日志文件: {result.stderr}")
                    all_cleaned = False
                else:
                    all_files = result.stdout.splitlines()
                    log_files = [f for f in all_files if f.endswith(".log")]

                    self.logger.info(
                        f"[{self.component_name}] 容器中所有文件: {all_files}"
                    )
                    self.logger.info(
                        f"[{self.component_name}] 找到 {len(log_files)} 个日志文件: {log_files}"
                    )

                    if not log_files:
                        self.logger.warning(
                            f"[{self.component_name}] 未找到任何.log文件"
                        )

                    for log_file in log_files:
                        src_path = f"{self.container_id}:{log_dir}/{log_file}"
                        dest_path = os.path.join(dest_dir, log_file)

                        copy_cmd = ["docker", "cp", src_path, dest_path]
                        self.logger.info(
                            f"[{self.component_name}] 执行拷贝命令: {' '.join(copy_cmd)}"
                        )
                        result = subprocess.run(
                            copy_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE
                        )

                        if result.returncode == 0:
                            self.logger.info(
                                f"已复制日志文件: {log_file} -> {dest_path}"
                            )
                        else:
                            stderr_msg = (
                                result.stderr.decode("utf-8")
                                if result.stderr
                                else "无错误信息"
                            )
                            self.logger.error(
                                f"复制日志文件失败 {log_file}: {stderr_msg}"
                            )
                            all_cleaned = False
            except Exception as e:
                self.logger.error(f"移动日志时发生异常: {str(e)}", exc_info=True)
                all_cleaned = False

            # 拷贝ROS日志文件
            if not self._copy_ros_logs(dest_dir):
                all_cleaned = False
        else:
            self.logger.warning("未提供container_id，跳过日志移动")

        # 清空进程记录
        self.running_processes.clear()

        if all_cleaned:
            self.logger.info(f"[{self.component_name}] 清理成功完成。")
        else:
            self.logger.warning(f"[{self.component_name}] 清理时遇到问题。")
        self.initialized_successfully = False  # Mark as not initialized after cleanup
        return all_cleaned

    def _setup_ros_parameters(self) -> bool:
        """
        在算法容器中设置ROS参数，包括：
        1. 使用rospack find config获取配置路径
        2. 设置local_planning相关的ROS参数

        返回:
            bool: 设置成功返回True，失败返回False
        """
        try:
            self.logger.info(f"[{self.component_name}] 开始设置ROS参数...")

            # 创建ROS参数设置脚本
            ros_param_script = """#!/bin/bash

# 设置ROS环境变量（如果需要）
export ROS_MASTER_URI=http://localhost:11311
source /autocar-code/install/setup.bash

# 获取config路径
config_path=$(rospack find config)
if [ -z "$config_path" ]; then
  echo "错误: config_path 变量未设置."
  exit 1
fi

echo "找到config路径: $config_path"

# 1. 定义文件路径变量
planning_config="${config_path}/vehicle/planning_config.txt"
vehicle_config="${config_path}/vehicle/vehicle_config.txt"

echo "planning_config路径: $planning_config"
echo "vehicle_config路径: $vehicle_config"

# 检查文件是否存在
if [ ! -f "$planning_config" ]; then
    echo "错误: planning_config文件不存在: $planning_config"
    exit 1
fi

if [ ! -f "$vehicle_config" ]; then
    echo "错误: vehicle_config文件不存在: $vehicle_config"
    exit 1
fi

# 2. 使用 rosparam set 设置ROS参数
echo "正在设置 local_planning 的ROS参数..."

# 设置planning配置文件路径
rosparam set /local_planning/config "$planning_config"
echo "已设置 /local_planning/config = $planning_config"

# 设置vehicle配置文件路径
rosparam set /local_planning/vehicle "$vehicle_config"
echo "已设置 /local_planning/vehicle = $vehicle_config"

# 设置vehicle_info配置文件路径（与vehicle_config相同）
rosparam set /local_planning/vehicle_info "$vehicle_config"
echo "已设置 /local_planning/vehicle_info = $vehicle_config"

# 设置obstacles话题名称
rosparam set /local_planning/obstacles_topic "/perception/fusion/obstacles"
echo "已设置 /local_planning/obstacles_topic = /perception/fusion/obstacles"

# 设置hadmap_server的ROS参数
rosparam set /hadmap_server/object_query_distance '200'
rosparam set /hadmap_server/trajectory_query_distance '15'
rosparam set /hadmap_server/fps '10'

# 设置hadmap_engine的ROS参数
rosparam set /hadmap_engine_node/utm_zone_code '50'
rosparam set /hadmap_engine_node/manual_flag 'false'
rosparam set /hadmap_engine_node/use_mid 'false'
rosparam set /hadmap_engine_node/start_lon '116.7324208'
rosparam set /hadmap_engine_node/start_lat '40.1988215'
rosparam set /hadmap_engine_node/mid_lon '116.7391581'
rosparam set /hadmap_engine_node/mid_lat '40.196797'
rosparam set /hadmap_engine_node/end_lon '116.7314106'
rosparam set /hadmap_engine_node/end_lat '40.1968944'
rosparam set /hadmap_engine_node/map_radius '100.0'
rosparam set /hadmap_engine_node/update_dis '1000.0'
rosparam set /hadmap_engine_node/loading_dis '50'
rosparam set /hadmap_engine_node/object_query_distance '200'
rosparam set /hadmap_engine_node/fps '10'

rosparam set /hadmap_engine_node/routing_request_path '/home/<USER>/data/vehicle_monitor/hadmap_data/'
rosparam set /hadmap_engine_node/track_record_path '/home/<USER>/data/vehicle_monitor/hadmap_data/'
rosparam set /hadmap_engine_node/dpqp_track_record_path '/home/<USER>/data/vehicle_monitor/hadmap_data/'
rosparam set /hadmap_engine_node/default_track_record_path '/home/<USER>/data/vehicle_monitor/hadmap_data/'

echo "所有ROS参数设置完成"
"""

            # 将脚本写入容器
            script_path = "/tmp/setup_ros_params.sh"
            script_log_path = "/tmp/setup_ros_params.log"

            # 写入脚本文件
            write_script_cmd = [
                "docker",
                "exec",
                self.container_id,
                "sh",
                "-c",
                f"cat > {script_path} << 'EOF'\n{ros_param_script}EOF",
            ]

            script_result = subprocess.run(
                write_script_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                timeout=30,
            )

            if script_result.returncode != 0:
                self.logger.error(
                    f"[{self.component_name}] 创建ROS参数设置脚本失败: {script_result.stderr}"
                )
                return False

            # 给脚本添加执行权限
            chmod_cmd = [
                "docker",
                "exec",
                self.container_id,
                "chmod",
                "+x",
                script_path,
            ]

            subprocess.run(chmod_cmd, timeout=10)

            # 执行脚本
            execute_cmd = [
                "docker",
                "exec",
                self.container_id,
                "sh",
                "-c",
                f"{script_path} > {script_log_path} 2>&1",
            ]

            self.logger.info(
                f"[{self.component_name}] 执行ROS参数设置脚本: {' '.join(execute_cmd)}"
            )

            execute_result = subprocess.run(
                execute_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                timeout=60,
            )

            # 获取脚本执行的日志
            log_cmd = ["docker", "exec", self.container_id, "cat", script_log_path]

            log_result = subprocess.run(
                log_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                timeout=10,
            )

            if log_result.returncode == 0 and log_result.stdout:
                self.logger.info(
                    f"[{self.component_name}] ROS参数设置脚本执行日志:\n{log_result.stdout}"
                )

            if execute_result.returncode == 0:
                self.logger.info(f"[{self.component_name}] ROS参数设置完成")

                # 验证参数是否设置成功
                if self._verify_ros_parameters():
                    self.logger.info(f"[{self.component_name}] ROS参数验证成功")
                    return True
                else:
                    self.logger.warning(
                        f"[{self.component_name}] ROS参数验证失败，但继续执行"
                    )
                    return True  # 即使验证失败也继续，因为可能是ROS Master未启动
            else:
                self.logger.error(
                    f"[{self.component_name}] ROS参数设置脚本执行失败，返回码: {execute_result.returncode}"
                )
                if execute_result.stderr:
                    self.logger.error(
                        f"[{self.component_name}] 错误输出: {execute_result.stderr}"
                    )
                return False

        except subprocess.TimeoutExpired:
            self.logger.error(f"[{self.component_name}] ROS参数设置超时")
            return False
        except Exception as e:
            self.logger.error(f"[{self.component_name}] ROS参数设置失败: {e}")
            return False

    def _verify_ros_parameters(self) -> bool:
        """
        验证ROS参数是否设置成功

        返回:
            bool: 验证成功返回True，失败返回False
        """
        try:
            # 要验证的参数列表
            params_to_verify = [
                "/local_planning/config",
                "/local_planning/vehicle",
                "/local_planning/vehicle_info",
                "/local_planning/obstacles_topic",
            ]

            for param in params_to_verify:
                # 在验证命令中也添加ROS环境设置
                verify_cmd = [
                    "docker",
                    "exec",
                    self.container_id,
                    "sh",
                    "-c",
                    f"source /autocar-code/install/setup.bash && rosparam get {param} 2>/dev/null || echo 'PARAM_NOT_FOUND'",
                ]

                result = subprocess.run(
                    verify_cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    timeout=10,
                )

                if result.returncode == 0:
                    output = result.stdout.strip()
                    if output != "PARAM_NOT_FOUND":
                        self.logger.info(
                            f"[{self.component_name}] 参数 {param} = {output}"
                        )
                    else:
                        self.logger.warning(
                            f"[{self.component_name}] 参数 {param} 未找到"
                        )
                        return False
                else:
                    self.logger.warning(
                        f"[{self.component_name}] 无法验证参数 {param}: {result.stderr}"
                    )
                    return False

            return True

        except Exception as e:
            self.logger.error(f"[{self.component_name}] 验证ROS参数时发生异常: {e}")
            return False

    def _copy_hadmap_database(self) -> bool:
        """
        将hadmap数据库文件从宿主机复制到算法容器中

        文件将从case_path指定的目录中递归查找

        返回:
            bool: 复制成功返回True，失败返回False
        """
        try:
            self.logger.info(f"[{self.component_name}] 开始复制hadmap数据库文件...")

            # 定义需复制的文件列表及目标目录
            target_dir = "/home/<USER>/data/vehicle_monitor/hadmap_data/"
            files_to_copy = ["db.sqlite", "stop.txt", "traj.csv"]

            # 检查case_path是否有效
            if not self.case_path:
                self.logger.error(
                    f"[{self.component_name}] case_path未提供，无法查找hadmap数据库文件"
                )
                return False

            # 步骤1：在容器中创建目标目录（如不存在）
            mkdir_cmd = ["docker", "exec", self.container_id, "mkdir", "-p", target_dir]
            self.logger.info(
                f"[{self.component_name}] 创建目标目录: {' '.join(mkdir_cmd)}"
            )
            mkdir_result = subprocess.run(
                mkdir_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                timeout=10,
            )

            if mkdir_result.returncode != 0:
                self.logger.error(
                    f"[{self.component_name}] 创建目标目录失败: {mkdir_result.stderr}"
                )
                return False

            self.logger.info(f"[{self.component_name}] 目标目录创建成功")

            # 步骤2：递归查找并复制文件
            all_success = True
            for filename in files_to_copy:
                # 在case_path目录中递归查找文件
                found_files = []
                for root, _, files in os.walk(self.case_path):
                    if filename in files:
                        found_files.append(os.path.join(root, filename))

                if not found_files:
                    self.logger.warning(
                        f"[{self.component_name}] 在 {self.case_path} 中未找到文件: {filename}"
                    )
                    all_success = False
                    continue

                # 如果有多个同名文件，使用第一个找到的
                source_file = found_files[0]
                if len(found_files) > 1:
                    self.logger.warning(
                        f"[{self.component_name}] 找到多个 {filename} 文件，使用第一个: {source_file}"
                    )

                target_path = f"{target_dir}{filename}"

                self.logger.info(
                    f"[{self.component_name}] 准备复制文件: {source_file} -> {self.container_id}:{target_path}"
                )

                copy_cmd = [
                    "docker",
                    "cp",
                    source_file,
                    f"{self.container_id}:{target_path}",
                ]
                self.logger.info(
                    f"[{self.component_name}] 执行复制命令: {' '.join(copy_cmd)}"
                )
                copy_result = subprocess.run(
                    copy_cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    timeout=60,
                )

                if copy_result.returncode != 0:
                    self.logger.error(
                        f"[{self.component_name}] 文件复制失败: {copy_result.stderr}"
                    )
                    self.logger.error(
                        f"[{self.component_name}] 可能的原因：宿主机上文件 {source_file} 不存在或无权限访问"
                    )
                    all_success = False
                    continue  # 尝试复制其他文件

                self.logger.info(f"[{self.component_name}] 文件 {filename} 复制成功")

                # 步骤3：验证文件是否成功复制到容器中
                verify_cmd = [
                    "docker",
                    "exec",
                    self.container_id,
                    "ls",
                    "-la",
                    target_path,
                ]
                verify_result = subprocess.run(
                    verify_cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    timeout=10,
                )

                if verify_result.returncode == 0:
                    self.logger.info(
                        f"[{self.component_name}] 文件验证成功: {verify_result.stdout.strip()}"
                    )
                else:
                    self.logger.warning(
                        f"[{self.component_name}] 文件验证失败: {verify_result.stderr}"
                    )
                    all_success = False

            return all_success

        except subprocess.TimeoutExpired:
            self.logger.error(f"[{self.component_name}] hadmap数据库文件复制超时")
            return False
        except Exception as e:
            self.logger.error(f"[{self.component_name}] hadmap数据库文件复制失败: {e}")
            return False

    def _copy_ros_logs(self, dest_dir: str) -> bool:
        """
        将ROS日志文件从容器的/root/.ros/log/latest目录复制到宿主机
        只复制非roslaunch-开头的日志文件

        参数:
            dest_dir (str): 目标目录路径

        返回:
            bool: 复制成功返回True，失败返回False
        """
        try:
            self.logger.info(f"[{self.component_name}] 开始复制ROS日志文件...")

            # 定义源目录路径
            ros_log_dir = "/root/.ros/log/latest/"

            self.logger.info(f"[{self.component_name}] ROS日志源目录: {ros_log_dir}")
            self.logger.info(f"[{self.component_name}] 目标目录: {dest_dir}")

            # 获取容器中ROS日志目录的所有文件
            list_cmd = ["docker", "exec", self.container_id, "ls", "-la", ros_log_dir]
            self.logger.info(f"[{self.component_name}] 执行命令: {' '.join(list_cmd)}")

            result = subprocess.run(
                list_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                timeout=30,
            )

            self.logger.info(
                f"[{self.component_name}] ls命令返回码: {result.returncode}"
            )

            if result.returncode != 0:
                self.logger.warning(
                    f"[{self.component_name}] 无法列出ROS日志目录文件: {result.stderr}"
                )
                # ROS日志目录可能不存在，这不算错误
                return True

            if result.stdout:
                self.logger.info(
                    f"[{self.component_name}] ROS日志目录内容:\n{result.stdout}"
                )

            # 解析输出，获取文件列表
            output_lines = result.stdout.strip().split("\n")
            all_files = []

            for line in output_lines:
                line = line.strip()
                if line and not line.startswith("total") and not line.startswith("d"):
                    # 提取文件名（ls -la输出的最后一列）
                    parts = line.split()
                    if len(parts) >= 9:
                        filename = parts[-1]
                        # 过滤掉当前目录和上级目录
                        if filename not in [".", ".."]:
                            all_files.append(filename)

            # 过滤掉roslaunch-开头的文件，只保留其他日志文件
            ros_log_files = [
                f
                for f in all_files
                if not f.startswith("roslaunch-") and f.endswith(".log")
            ]

            self.logger.info(f"[{self.component_name}] 找到的所有文件: {all_files}")
            self.logger.info(
                f"[{self.component_name}] 需要复制的ROS日志文件: {ros_log_files}"
            )

            if not ros_log_files:
                self.logger.info(f"[{self.component_name}] 未找到需要复制的ROS日志文件")
                return True

            # 逐个复制文件
            for log_file in ros_log_files:
                src_path = f"{self.container_id}:{ros_log_dir}/{log_file}"
                dest_path = os.path.join(
                    dest_dir, f"ros_{log_file}"
                )  # 添加ros_前缀避免冲突

                copy_cmd = ["docker", "cp", src_path, dest_path]
                self.logger.info(
                    f"[{self.component_name}] 执行ROS日志拷贝命令: {' '.join(copy_cmd)}"
                )

                copy_result = subprocess.run(
                    copy_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, timeout=30
                )

                if copy_result.returncode == 0:
                    self.logger.info(
                        f"[{self.component_name}] 已复制ROS日志文件: {log_file} -> {dest_path}"
                    )
                else:
                    stderr_msg = (
                        copy_result.stderr.decode("utf-8")
                        if copy_result.stderr
                        else "无错误信息"
                    )
                    self.logger.error(
                        f"[{self.component_name}] 复制ROS日志文件失败 {log_file}: {stderr_msg}"
                    )
                    # 单个文件复制失败不影响整体流程
                    continue

            self.logger.info(f"[{self.component_name}] ROS日志文件复制完成")
            return True

        except subprocess.TimeoutExpired:
            self.logger.error(f"[{self.component_name}] 复制ROS日志文件超时")
            return False
        except Exception as e:
            self.logger.error(f"[{self.component_name}] 复制ROS日志文件时发生异常: {e}")
            return False
