#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from abc import ABC, abstractmethod
from utils.loggers import LoggerProxy

logger = LoggerProxy()

from api.process.process_manager import ProcessManager


class BaseHandler(ABC):
    """所有辅助处理器的基类，定义通用接口。"""

    def __init__(
        self,
        component_name: str,
        config: dict,
        logger_instance=None,
    ):
        self.component_name = component_name
        self.config = config
        self.logger = (
            logger_instance if logger_instance else logger
        )  # 使用传入的logger或全局logger
        self.initialized_successfully = False  # 标记初始化状态

    @abstractmethod
    def setup(self) -> bool:
        """
        执行所有特定于此处理器的初始化步骤。
        例如：启动进程、连接到服务、配置硬件等。
        成功返回 True，失败返回 False。
        """
        pass

    @abstractmethod
    def cleanup(self) -> bool:
        """
        清理此处理器管理的所有特定于组件的资源 (非进程相关，进程由ProcessManager处理)。
        例如：断开连接、释放API资源等。
        应尽可能确保所有资源都被正确释放，即使在发生错误时。
        返回True表示清理成功或无操作，False表示清理遇到问题。
        """
        pass
