# import dataclasses
import os
import subprocess
import signal
import threading
import time
from typing import Callable, Optional, Dict

import psutil


# @dataclasses.dataclass
# class Process:
    # proc: subprocess.Popen
    # callback: Optional[Callable[[int], None]]
    # end_signal: signal.Signals

class Process:
    def __init__(self, proc: subprocess.Popen, callback: Optional[Callable[[int], None]], end_signal: signal.Signals):
        self.proc: subprocess.Popen = proc
        self.callback: Optional[Callable[[int], None]] = callback
        self.end_signal: signal.Signals = end_signal

    # 如果您需要 dataclass 自动生成的其他方法，例如 __repr__, __eq__ 等，
    # 您需要手动实现它们。例如：
    def __repr__(self) -> str:
        return f"Process(proc={self.proc!r}, callback={self.callback!r}, end_signal={self.end_signal!r})"

    def __eq__(self, other: object) -> bool:
        if not isinstance(other, Process):
            return NotImplemented
        return (self.proc == other.proc and
                self.callback == other.callback and
                self.end_signal == other.end_signal)


class ProcessManager:
    TIMEOUT = 10

    def __init__(self):
        self._processes: Dict[int, Process] = {}
        self._done = threading.Event()
        self._check_thread = threading.Thread(target=self.__check)
        self._check_thread.start()

    def __check(self):
        while not self._done.is_set():
            for p in list(self._processes.values()):
                if p.proc.poll() is not None:
                    if p.callback:
                        p.callback(p.proc.returncode)
                        p.callback = None
            time.sleep(1)

    @staticmethod
    def _send_signal_to_subproc_recursive(ppid: int, sig: int = signal.SIGTERM):
        try:
            parent = psutil.Process(ppid)
        except psutil.NoSuchProcess:
            parent = None

        if parent:
            children = parent.children(recursive=True)
        else:
            children = [proc for proc in psutil.process_iter(['ppid']) if proc.info['ppid'] == ppid]

        for child in children:
            try:
                os.kill(child.pid, sig)
            except ProcessLookupError:
                pass

        if parent:
            try:
                os.kill(ppid, sig)
            except ProcessLookupError:
                pass

    def terminate_all(self):
        for p in self._processes.values():
            if p.proc and p.proc.poll() is None:
                ppid = os.getpgid(p.proc.pid)
                self._send_signal_to_subproc_recursive(ppid, p.end_signal)
                time.sleep(1)

                alive_proc_cnt = 0
                try:
                    parent = psutil.Process(ppid)
                    children = parent.children(recursive=True)
                    alive_proc_cnt = len(children) + 1  # add parent
                except psutil.NoSuchProcess:
                    children = [proc for proc in psutil.process_iter(['ppid']) if proc.info['ppid'] == ppid]
                    alive_proc_cnt = len(children)

                if alive_proc_cnt > 0:
                    time.sleep(self.TIMEOUT)
                    self._send_signal_to_subproc_recursive(ppid, signal.SIGKILL)

    def add_process(self, process: subprocess.Popen, callback: Optional[Callable[[int], None]] = None,
                    end_signal: signal.Signals = signal.SIGTERM) -> int:
        if process.pid in self._processes:
            raise ValueError(f"Process with pid {process.pid} already exists")
        self._processes[process.pid] = Process(process, callback, end_signal)
        return process.pid

    def add_cmd_process(self, cmd: str, callback: Optional[Callable[[int], None]] = None,
                        end_signal: signal.Signals = signal.SIGTERM) -> int:
        process = subprocess.Popen(
            cmd, shell=True, preexec_fn=os.setpgrp, stdin=subprocess.DEVNULL,
            stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL, executable='/bin/bash'
        )
        return self.add_process(process, callback, end_signal)

    def check_alive(self, pid: int) -> bool:
        return self._processes[pid].proc.poll() is None

    def destroy(self):
        self._done.set()
        self.terminate_all()
        if self._check_thread:
            self._check_thread.join()
            self._check_thread = None

    def __del__(self):
        self.destroy()
