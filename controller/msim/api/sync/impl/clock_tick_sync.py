#  Copyright (C) Mogo.ai Information and Technology Co.Ltd 
#
#  All rights reserved.
#
#  This document contains proprietary information belonging to Mogo.ai Co.Ltd.
#  Passing on and copying of this document, and communication of its contents
#  is not permitted without prior written authorization.
#
#
#  <AUTHOR>
#  @date 10/7/23, 2:41 PM
import threading

from rosgraph_msgs.msg import Clock
from api.sync.sync_base import SyncBase
from utils.loggers import get_server_logger
logger = get_server_logger()


class ClockTickSync(SyncBase):
    """
    Sync with CARLA simulator by ros topic clock

    """
    SIMULATION_TIME_TOPIC = '/clock'
    SIMULATION_TICK_RATE = 0.02

    def __init__(self):
        super().__init__()
        self._node = None
        self._runner_sync_client = None
        self._callback_group = None
        self._start_event = threading.Event()

    def init(self, callback=None, event=None):
        from api.common.node import get_node
        self._node = get_node()

        def _tick():
            try:
                if not self._start_event.is_set():
                    return
                callback()
            except Exception as e:
                logger.error("Error in tick callback: {}".format(e))

        self._node.create_timer(self.SIMULATION_TICK_RATE, _tick, use_clock=True)

    def start(self):
        self._start_event.set()
