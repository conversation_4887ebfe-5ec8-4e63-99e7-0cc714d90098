#  Copyright (C) Mogo.ai Information and Technology Co.Ltd 
#
#  All rights reserved.
#
#  This document contains proprietary information belonging to Mogo.ai Co.Ltd.
#  Passing on and copying of this document, and communication of its contents
#  is not permitted without prior written authorization.
#
#
#  <AUTHOR>
#  @date 10/7/23, 2:42 PM
#
#  All rights reserved.
#
#  This document contains proprietary information belonging to Mogo.ai Co.Ltd.
#  Passing on and copying of this document, and communication of its contents
#  is not permitted without prior written authorization.
#
#
#  <AUTHOR>
#  @date 10/7/23, 2:40 PM
from typing import Optional, Callable, Union


class SyncBase:
    """
    Base class for synchronous mode
    For validator to sync with simulator
    """
    def __init__(self):
        pass

    def init(self, callback: Optional[Callable[[], None]] = None, event=None):
        pass

    def wait(self):
        pass

    def tick(self):
        pass

    def start(self):
        pass
