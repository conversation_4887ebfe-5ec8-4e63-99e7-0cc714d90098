#  Copyright (C) Mogo.ai Information and Technology Co.Ltd 
#
#  All rights reserved.
#
#  This document contains proprietary information belonging to Mogo.ai Co.Ltd.
#  Passing on and copying of this document, and communication of its contents
#  is not permitted without prior written authorization.
#
#
#  <AUTHOR>
#  @date 10/7/23, 5:21 PM
from api.sync.sync_base import SyncBase


class SyncFactory:
    """
    Factory class for synchronous mode
    For validator to sync with simulator
    """
    def __init__(self, task_data):
        pass

    def create_sync(self) -> SyncBase:
        from api.sync.impl.clock_tick_sync import ClockTickSync
        return ClockTickSync()
