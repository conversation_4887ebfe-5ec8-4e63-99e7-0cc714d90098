"""
Docker工具模块，提供Docker镜像和容器管理的通用功能。
"""
import os
import subprocess
import json
import logging
import time
from pathlib import Path
from typing import Dict, List, Optional, Union, Tuple

from utils.file_utils import get_setup_value
from utils.loggers import get_server_logger
logger = get_server_logger()

class DockerError(Exception):
    """Docker操作相关错误"""
    pass

def pull_image(image_name: str) -> bool:
    """
    拉取Docker镜像
    
    Args:
        image_name: 镜像名称，格式为 "repository:tag"
        
    Returns:
        bool: 拉取是否成功
    
    Raises:
        DockerError: 当Docker操作失败时抛出
    """
    try:
        # 拉取镜像
        pull_cmd = f"docker pull {image_name}"
        logger.info(f"开始拉取镜像: {image_name}")
        result = subprocess.run(pull_cmd, shell=True, check=True,
                               stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        logger.info(f"镜像拉取成功: {image_name}")
        return True
    except subprocess.CalledProcessError as e:
        error_msg = f"Docker操作失败: {e.stderr.decode() if e.stderr else str(e)}"
        logger.error(error_msg)
        raise DockerError(error_msg)

def run_algorithm_container(
    image_name: str,
    container_name: str,
    mount_path: Optional[List[str]] = None,
    network: str = None,
    environment: Optional[Dict[str, str]] = None,
    ports: Optional[Dict[str, str]] = None,
    gpu: bool = False,
    privileged: bool = False,
    command: Optional[str] = None
) -> str:
    """
    启动算法容器
    
    Args:
        image_name: 镜像名称
        container_name: 容器名称
        mount_path: 挂载路径，如果为None则使用配置文件中的值
        network: 网络模式，默认为host
        environment: 环境变量
        ports: 端口映射
        gpu: 是否使用GPU
        privileged: 是否使用特权模式
        command: 容器启动命令
        
    Returns:
        str: 容器ID
        
    Raises:
        DockerError: 当Docker操作失败时抛出
    """
    try:   
        # 检查容器是否已存在，如果存在则删除
        check_cmd = f"docker ps -a -q -f name={container_name}"
        result = subprocess.run(check_cmd, shell=True, check=True, 
                               stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        if result.stdout:
            logger.info(f"容器 {container_name} 已存在，正在删除...")
            remove_cmd = f"docker rm -f {container_name}"
            subprocess.run(remove_cmd, shell=True, check=True)
        
        # 构建运行命令
        run_cmd = f"docker run -d --name {container_name}"
        
        # 添加网络设置
        if network:
            run_cmd += f" --network={network}"
        
        # 添加环境变量
        if environment:
            for key, value in environment.items():
                run_cmd += f" -e {key}={value}"
        
        # 添加端口映射
        if ports:
            for host_port, container_port in ports.items():
                run_cmd += f" -p {host_port}:{container_port}"
        
        # 添加挂载
        if mount_path:
            for path in mount_path:
                run_cmd += f" -v path"
        
        # 添加GPU支持
        if gpu:
            run_cmd += " --gpus all -e NVIDIA_VISIBLE_DEVICES=all \
                        -e NVIDIA_DRIVER_CAPABILITIES=all"
        
        if privileged:
            run_cmd += " --privileged"
        
        # 添加镜像名称
        run_cmd += f" {image_name}"
        
        # 添加启动命令
        if command:
            run_cmd += f" {command}"
        
        # 运行容器
        logger.info(f"启动算法容器: {run_cmd}")
        result = subprocess.run(run_cmd, shell=True, check=True,
                               stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        container_id = result.stdout.decode().strip()
        logger.info(f"算法容器启动成功，容器ID: {container_id}")
        
        # 等待容器完全启动
        time.sleep(2)
        
        # 检查容器是否正常运行
        check_cmd = f"docker ps -q -f id={container_id}"
        result = subprocess.run(check_cmd, shell=True, check=True,
                               stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        if not result.stdout:
            raise DockerError(f"容器 {container_name} 启动后异常退出")
        
        return container_id
    
    except subprocess.CalledProcessError as e:
        error_msg = f"启动算法容器失败: {e.stderr.decode() if e.stderr else str(e)}"
        logger.error(error_msg)
        raise DockerError(error_msg)

def stop_container(container_id_or_name: str) -> bool:
    """
    停止并删除容器
    
    Args:
        container_id_or_name: 容器ID或名称
        
    Returns:
        bool: 操作是否成功
    """
    try:
        # 停止容器
        stop_cmd = f"docker stop {container_id_or_name}"
        subprocess.run(stop_cmd, shell=True, check=True,
                      stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # 删除容器
        rm_cmd = f"docker rm {container_id_or_name}"
        subprocess.run(rm_cmd, shell=True, check=True,
                      stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        logger.info(f"容器 {container_id_or_name} 已停止并删除")
        return True
    
    except subprocess.CalledProcessError as e:
        error_msg = f"停止容器失败: {e.stderr.decode() if e.stderr else str(e)}"
        logger.error(error_msg)
        return False

def exec_command(container_id_or_name: str, command: str, 
                interactive: bool = False) -> Tuple[int, str, str]:
    """
    在容器中执行命令
    
    Args:
        container_id_or_name: 容器ID或名称
        command: 要执行的命令
        interactive: 是否以交互模式执行
        
    Returns:
        Tuple[int, str, str]: 返回码、标准输出、标准错误
    """
    try:
        # 构建执行命令
        exec_cmd = f"docker exec"
        if interactive:
            exec_cmd += " -it"
        exec_cmd += f" {container_id_or_name} {command}"
        
        # 执行命令
        process = subprocess.Popen(
            exec_cmd, 
            shell=True, 
            stdout=subprocess.PIPE, 
            stderr=subprocess.PIPE
        )
        stdout, stderr = process.communicate()
        
        return process.returncode, stdout.decode(), stderr.decode()
    
    except Exception as e:
        logger.error(f"在容器中执行命令失败: {str(e)}")
        return -1, "", str(e)
    
def docker_cp_cmd(src_path: str, dest_path: str, container_id_or_name: str, from_docker_to_host: bool) -> str:
    """
    使用Docker cp命令复制文件或目录到容器中，或从容器中复制文件或目录到本地
    
    Args:
        src_path: 源路径
        dest_path: 目标路径
        container_id_or_name: 容器ID或名称
        from_docker_to_host: 是否从容器中复制文件或目录到本地
        
    Returns:
        str: 命令结果
    """
    try:
        # 构建Docker cp命令
        cp_cmd = f"docker cp"
        # 判断是否为从docker拷贝到宿主机
        if from_docker_to_host:
            cp_cmd += f" {container_id_or_name}:{src_path} {dest_path}"
        else:
            cp_cmd += f" {src_path} {container_id_or_name}:{dest_path}"
        
        # 执行Docker cp命令
        result = subprocess.run(cp_cmd, shell=True, check=True,
                               stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        return result.stdout.decode()
    except subprocess.CalledProcessError as e:
        error_msg = f"Docker cp命令执行失败: {e.stderr.decode() if e.stderr else str(e)}"
        logger.error(error_msg)
        raise DockerError(error_msg)


def prune_space():
    """
    清理Docker空间，删除未使用的容器、镜像和卷
    """
    try:
        # 删除停止的容器
        subprocess.run("docker container prune -f", shell=True, check=True)
        
        # 删除未使用的镜像
        subprocess.run("docker image prune -f", shell=True, check=True)
        
        # 删除未使用的卷
        subprocess.run("docker volume prune -f", shell=True, check=True)
        
        logger.info("Docker空间清理完成")
    
    except subprocess.CalledProcessError as e:
        logger.error(f"Docker空间清理失败: {str(e)}")
