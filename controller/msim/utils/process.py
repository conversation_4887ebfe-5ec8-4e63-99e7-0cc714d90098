import os
import signal
from subprocess import Popen

import psutil


class SubProcess(Popen):

    def kill(self):
        super().kill()
        self._send_signal_to_subproc(signal.SIGKILL)

    def terminate(self):
        super().terminate()
        self._send_signal_to_subproc(signal.SIGTERM)

    def _send_signal_to_subproc(self, sig: int = signal.SIGTERM):
        for pid in psutil.pids():
            if os.getpgid(pid) == self.pid:
                try:
                    os.kill(pid, sig)
                except Exception:
                    pass

    def send_signal(self, sig: int):
        self._send_signal_to_subproc(sig)

    def is_alive(self):
        if self.poll() is None:
            return True

        for pid in psutil.pids():
            if os.getpgid(pid) == self.pid:
                return True

        return False
