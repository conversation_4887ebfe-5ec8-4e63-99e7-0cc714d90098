"""
节点工具模块，提供算法节点启动和管理的通用功能。
"""

import os
import signal
import subprocess
from typing import Optional, Callable

from utils.loggers import LoggerProxy

logger = LoggerProxy("player")
from api.process.process_manager import ProcessManager


class NodeError(Exception):
    """节点操作相关错误"""

    pass


class AlgorithmNode:
    """
    算法节点类，用于管理算法节点的生命周期
    """

    def __init__(
        self, node_name: str, process_manager: Optional[ProcessManager] = None
    ):
        """
        初始化算法节点

        Args:
            node_name: 节点名称
            process_manager: 进程管理器，如果为None则创建新的
        """
        self.node_name = node_name
        self.process_manager = process_manager or ProcessManager()
        self.process_id = None
        self.process = None  # 存储进程对象
        self.is_running = False
        self.log_file = None
        self._callback = None

    def start(
        self,
        command: str,
        log_path: str,
        callback: Optional[Callable[[int], None]] = None,
        end_signal: signal.Signals = signal.SIGTERM,
    ) -> int:
        """
        启动算法节点

        Args:
            command: 启动命令
            log_path: 日志路径
            callback: 进程结束回调函数
            end_signal: 结束信号

        Returns:
            int: 进程ID

        Raises:
            NodeError: 当节点启动失败时抛出
        """
        try:
            # 确保日志目录存在
            os.makedirs(os.path.dirname(log_path), exist_ok=True)

            # 打开日志文件用于重定向
            self.log_file = open(log_path, "w")

            # 保存回调函数
            self._callback = callback

            # 启动进程
            logger.info(f"启动算法节点 {self.node_name}，命令: {command}")

            # 使用subprocess.Popen创建进程，并进行重定向
            self.process = subprocess.Popen(
                command,
                shell=True,
                preexec_fn=os.setpgrp,
                stdin=subprocess.DEVNULL,
                stdout=self.log_file,
                stderr=self.log_file,
                executable="/bin/bash",
            )

            # 使用add_process添加进程
            self.process_id = self.process_manager.add_process(
                self.process, self._process_callback, end_signal
            )

            self.is_running = True
            logger.info(
                f"算法节点 {self.node_name} 启动成功，进程ID: {self.process_id}"
            )

            return self.process_id

        except Exception as e:
            error_msg = f"启动算法节点 {self.node_name} 失败: {str(e)}"
            logger.error(error_msg)
            raise NodeError(error_msg)

    def _process_callback(self, return_code: int):
        """
        进程结束回调

        Args:
            return_code: 进程返回码
        """
        self.is_running = False
        logger.info(f"算法节点 {self.node_name} 已结束，返回码: {return_code}")

        if self._callback:
            self._callback(return_code)

    def stop(self):
        """
        停止算法节点

        Returns:
            bool: 操作是否成功
        """
        if not self.is_running or self.process is None:
            logger.warning(f"算法节点 {self.node_name} 未运行")
            return True

        try:
            # 直接从进程对象终止进程
            if self.process.poll() is None:  # 检查进程是否还在运行
                # 先尝试优雅终止
                self.process.terminate()
                # 等待一秒
                try:
                    self.process.wait(timeout=1)
                except subprocess.TimeoutExpired:
                    # 如果超时，则强制终止
                    self.process.kill()

            # 从进程管理器中移除
            if self.process_id in self.process_manager._processes:
                del self.process_manager._processes[self.process_id]

            self.is_running = False
            logger.info(f"算法节点 {self.node_name} 已停止")

            # 关闭日志文件
            if self.log_file:
                self.log_file.close()

            return True

        except Exception as e:
            logger.error(f"停止算法节点 {self.node_name} 失败: {str(e)}")
            return False


class AlgorithmNodeManager:
    """
    算法节点管理器，用于管理多个算法节点
    """

    def __init__(self):
        """初始化算法节点管理器"""
        self.nodes = {}
        self.process_manager = ProcessManager()

    def start_node(
        self,
        node_name: str,
        command: str,
        log_path: str,
        callback: Optional[Callable[[int], None]] = None,
        end_signal: signal.Signals = signal.SIGTERM,
    ) -> AlgorithmNode:
        """
        启动算法节点

        Args:
            node_name: 节点名称
            command: 启动命令
            log_path: 日志路径
            callback: 进程结束回调函数
            end_signal: 结束信号

        Returns:
            AlgorithmNode: 算法节点对象
        """
        # 如果节点已存在，先停止
        if node_name in self.nodes:
            self.nodes[node_name].stop()

        # 创建新节点
        node = AlgorithmNode(node_name, self.process_manager)
        node.start(command, log_path, callback, end_signal)

        # 保存节点
        self.nodes[node_name] = node

        return node

    def stop_node(self, node_name: str) -> bool:
        """
        停止算法节点

        Args:
            node_name: 节点名称

        Returns:
            bool: 操作是否成功
        """
        if node_name not in self.nodes:
            logger.warning(f"节点 {node_name} 不存在")
            return False

        result = self.nodes[node_name].stop()
        if result:
            del self.nodes[node_name]

        return result

    def stop_all(self):
        """停止所有节点"""
        for node_name in list(self.nodes.keys()):
            self.stop_node(node_name)

    def get_node(self, node_name: str) -> Optional[AlgorithmNode]:
        """
        获取节点对象

        Args:
            node_name: 节点名称

        Returns:
            Optional[AlgorithmNode]: 节点对象，如果不存在则返回None
        """
        return self.nodes.get(node_name)

    def is_node_running(self, node_name: str) -> bool:
        """
        检查节点是否运行中

        Args:
            node_name: 节点名称

        Returns:
            bool: 节点是否运行中
        """
        node = self.get_node(node_name)
        return node is not None and node.is_running

    def destroy(self):
        """
        Stops all managed algorithm nodes and destroys the internal process manager.
        """
        # logger.info(f"AlgorithmNodeManager: Destroying all nodes and internal process manager...")
        # Use self.nodes.copy().keys() or list(self.nodes.keys()) if stop_node modifies the dict during iteration
        # However, stop_all iterates over list(self.nodes.keys()) so it's safe.
        self.stop_all()  # This already stops and removes nodes from self.nodes

        if hasattr(self, "process_manager") and self.process_manager:
            # logger.info(f"AlgorithmNodeManager: Destroying internal ProcessManager.")
            try:
                self.process_manager.destroy()
                # logger.info(f"AlgorithmNodeManager: Internal process manager destroyed.")
            except Exception as e:
                logger.error(
                    f"AlgorithmNodeManager: Error destroying internal process manager: {e}",
                    exc_info=True,
                )
        # else:
        # logger.info(f"AlgorithmNodeManager: No internal process manager to destroy or already destroyed.")
        self.nodes.clear()  # Ensure nodes dictionary is cleared

    def __del__(self):
        """析构函数，确保所有节点都被停止并且ProcessManager被销毁"""
        # logger.debug(f"AlgorithmNodeManager: __del__ called. Cleaning up...")
        # It's generally better to explicitly call destroy rather than relying on __del__
        # but having it here can be a fallback.
        if hasattr(self, "process_manager"):  # Check if initialization completed
            self.destroy()
