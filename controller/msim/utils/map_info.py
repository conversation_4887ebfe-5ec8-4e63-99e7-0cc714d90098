import os.path
import time
from pathlib import Path
from typing import List, Optional

from mcap_protobuf.writer import Writer


def gen_map_info_mcap(
        uuid: str,
        output: str,
        freq: float,
        frame_cnt: int,
        time_range: Optional[List[int]] = None,
):
    from proto.umap.map_info_pb2 import MapInfo
    output_path = Path(output)
    output_path.mkdir(parents=True, exist_ok=True)
    output_file = output_path.joinpath(f"map_info.mcap")
    writer = Writer(str(output_file))

    map_info = MapInfo()
    map_info.meta.uuid = uuid
    if not time_range:
        t = time.time_ns()
        time_range = [t, t + int(1e9)]

    delta_time = 1 / freq * 1e9

    for i in range(frame_cnt):
        l_t = int(time_range[0] + i * delta_time)
        if l_t > time_range[1]:
            break
        writer.write_message("/map/map_info", map_info, log_time=l_t)
    writer.finish()


if __name__ == '__main__':
    import argparse

    # pylint: disable=line-too-long
    parser = argparse.ArgumentParser(description="Setup the controller configuration",
                                     formatter_class=argparse.RawTextHelpFormatter)
    parser.add_argument('--uuid', type=str, help='The uuid of map')
    parser.add_argument('--output', default='', type=str, help='The output path of mcap')

    arguments = parser.parse_args()

    if not arguments.uuid:
        raise ValueError("uuid is required")

    if not arguments.output:
        arguments.output = os.path.abspath("./")

    gen_map_info_mcap(arguments.uuid, arguments.output, 10, 10)


