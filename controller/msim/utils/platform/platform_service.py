#!/usr/bin/env python3
# coding=utf-8

import os
import shutil
import platform
import tos
import yaml
import docker
import datetime
import time
import re
import requests
import urllib.request
import tarfile
from datetime import datetime, timedelta
from docker import from_env
from docker.errors import ImageNotFound
from pathlib import Path
from pypinyin import lazy_pinyin
from qcloud_cos import CosConfig
from qcloud_cos import CosS3Client
from typing import Optional

# 新增：默认使用腾讯云 COS，如需切换到字节 TOS，将此变量改为 True。
USE_TOS = False

from utils.loggers import get_server_logger
logger = get_server_logger()
from utils.metaclass import SingletonMeta
from utils.wraps import retry
from utils.path import SETUP_PATH
from utils.file_utils import get_setup_value

"""
Platform functions, including various functions related to the platform, 
obtaining system architecture types, downloading images, downloading case data, 
downloading raster maps, and downloading vehicle configurations
"""


class PlatformService(metaclass=SingletonMeta):

    def __init__(self):
        self.bucket_instance = None
        # 根据setup.ini中的配置初始化cos和tos相关信息
        self.cos_url = get_setup_value("cos", "cos_url")
        self.cos_secret_id = get_setup_value("cos", "secret_id")
        self.cos_secret_key = get_setup_value("cos", "secret_key")
        self.cos_region = get_setup_value("cos", "region")
        self.cos_bucket_name = get_setup_value("cos", "bucketName")

        self.tos_ak_id = get_setup_value("tos", "ak")
        self.tos_sk_id = get_setup_value("tos", "sk")
        self.tos_bucket_name = get_setup_value("tos", "bucket_name")
        self.tos_region = get_setup_value("tos", "region")
        self.tos_endpoint = get_setup_value("tos", "endpoint")

        self.init_bucket()
        # 根据setup.ini中的配置初始化mogo_platform
        self.login_url = get_setup_value("mogo_platform", "login_url")
        self.username = get_setup_value("mogo_platform", "username")
        self.password = get_setup_value("mogo_platform", "password")
        self.vehicle_config_url = get_setup_value("mogo_platform", "vehicle_config_url")
        self.gridmap_query_url = get_setup_value("mogo_platform", "gridmap_query_url")
        self.gridmap_download_url = get_setup_value(
            "mogo_platform", "gridmap_download_url"
        )

        data = {"username": self.username, "password": self.password}
        try:
            res = requests.post(self.login_url, json=data, timeout=10)
            res_json = res.json()
            self.jwt_token = res_json["data"]["jwt_token"]
        except requests.Timeout:
            logger.warning("登录请求超时（10秒），跳过此步骤")
            self.jwt_token = None
        except Exception as e:
            logger.error(f"登录请求失败: {str(e)}")
            self.jwt_token = None

    def get_platform_type(self):
        # 定义架构映射关系，便于扩展
        architecture_mapping = {
            "x86_64": "x86_64",
            "AMD64": "x86_64",
            "arm64": "aarch64",
            "aarch64": "aarch64",
        }

        # 获取当前架构
        architecture = platform.machine()

        # 记录日志，包含输入的架构
        logger.info(f"Detected platform architecture: {architecture}")

        # 查找匹配的平台类型
        platform_type = architecture_mapping.get(architecture, "")

        # 如果未匹配到任何已知架构，记录警告日志
        if not platform_type:
            logger.warning(
                f"Unknown platform architecture: {architecture}. Returning empty string."
            )

        return platform_type

    def init_bucket(self):
        try:
            # 检查是否需要重新初始化 bucket_instance
            if not self.bucket_instance:
                # 根据开关决定使用 COS 还是 TOS，默认为 COS
                if USE_TOS:
                    self.bucket_instance = tos.TosClientV2(
                        self.tos_ak_id,
                        self.tos_sk_id,
                        self.tos_endpoint,
                        self.tos_region,
                    )
                else:
                    config = CosConfig(
                        Region=self.cos_region,
                        SecretId=self.cos_secret_id,
                        SecretKey=self.cos_secret_key,
                    )
                    self.bucket_instance = CosS3Client(config)
        except Exception as e:
            logger.error(f"Failed to initialize bucket instance: {e}")
            raise  # 将异常向上抛出，以便调用方处理

        return self.bucket_instance

    @retry(tries=3)
    def download_from_cloud(
        self, bucket_path: Optional[str] = None, target_path: Optional[str] = None
    ):
        """
        下载云存储中的文件到指定的本地目录。

        :param bucket_path: 云存储中的文件路径
        :param target_path: 本地目标路径
        """
        # 校验输入参数
        if not bucket_path or not target_path:
            raise ValueError("bucket_path 和 target_path 必须提供有效值")

        # 类型断言，消除类型检查器关于 Optional 的警告
        assert bucket_path is not None and target_path is not None

        bucket_path_str: str = bucket_path  # for type checker clarity

        # 如果 target_path 是目录，则保留 bucket_path 的相对层级结构
        # 例如 bucket_path = "LBPNP/LBPNP.tar.gz"，target_path = "/data/casefile"，
        # 结果路径应为 "/data/casefile/LBPNP/LBPNP.tar.gz"
        if os.path.isdir(target_path):
            dest_file_path = os.path.join(
                target_path, os.path.basename(bucket_path_str)
            )
        else:
            dest_file_path = target_path

        # 确保目标目录存在（递归创建）
        dest_dir = os.path.dirname(dest_file_path)
        if dest_dir and not os.path.exists(dest_dir):
            os.makedirs(dest_dir, exist_ok=True)

        try:
            # 根据开关选择下载方式
            if USE_TOS:
                resp = self._download_via_get_object(bucket_path_str, dest_file_path)
            else:
                resp = self._download_via_download_file(bucket_path_str, dest_file_path)

            logger.info("成功从云存储下载文件，响应: %s", resp)
        except Exception as e:
            logger.error(
                f"从云存储下载文件失败，bucket_path: {bucket_path}, target_path: {dest_file_path}, 错误: {str(e)}"
            )
            raise  # 将异常重新抛出，以便 retry 装饰器生效
        # 根据bucket_path获取下载的文件名
        file_name = os.path.basename(bucket_path)
        return file_name

    def _download_via_get_object(self, bucket_path: str, target_path: str):
        """
        使用 get_object_to_file 方法下载文件。
        """
        return self.bucket_instance.get_object_to_file(
            self.tos_bucket_name, bucket_path, target_path
        )

    def _download_via_download_file(self, bucket_path: str, target_path: str):
        """
        使用 download_file 方法下载文件。
        """
        if self.cos_url in bucket_path:
            bucket_path = bucket_path.replace(self.cos_url, "")

        # Add progress callback for COS
        def progress_callback(consumed_bytes, total_bytes):
            import sys
            if total_bytes > 0:
                percentage = (consumed_bytes / total_bytes) * 100
                logger.info(f"Download progress: {consumed_bytes}/{total_bytes} ({percentage:.1f}%)")
            else:
                logger.info(f"Downloaded: {consumed_bytes} bytes")
            sys.stdout.flush()

        return self.bucket_instance.download_file(
            Bucket=self.cos_bucket_name, Key=bucket_path, DestFilePath=target_path, progress_callback=progress_callback
        )

    @retry(tries=3)
    def upload_to_cos(
        self,
        local_log_path: str = None,
        bucket_path: str = None,
        expire_hours: int = None,
    ):
        # 参数校验
        if not local_log_path or not os.path.isfile(local_log_path):
            raise ValueError(f"Invalid local_log_path: {local_log_path}")
        if not bucket_path:
            raise ValueError(f"Invalid bucket_path: {bucket_path}")

        # 处理 expire_hours 的默认值
        if expire_hours is None:
            expire_hours = 7 * 24  # 如果未指定，默认为7天
        elif expire_hours == 0:
            expire_hours = 0  # 用户明确指定为0，表示无过期时间

        try:
            # 构造上传路径，使用 os.path.join 确保路径安全
            key = os.path.join(bucket_path, os.path.basename(local_log_path))
            response = self.bucket_instance.upload_file(
                Bucket=self.cos_bucket_name,
                LocalFilePath=local_log_path,  # 本地文件的路径
                Key=key,  # 上传到桶之后的文件名
            )
            logger.info(f"Uploaded file successfully. Response: {response}")
        except Exception as e:
            # 记录详细错误信息，包括路径和参数
            logger.error(
                f"Failed to upload file to bucket. "
                f"Local path: {local_log_path}, Bucket path: {bucket_path}, Error: {str(e)}"
            )
            raise  # 重新抛出异常以便调用方处理

    def get_log_bucket_url(
        self, bucket_path: str = None, file_name: str = None, expire_hours: int = 7
    ):
        # get the URL of the case log on the cos bucket
        # 默认过期时间为7小时
        if expire_hours is None or expire_hours <= 0:
            expire_hours = 7

        # 参数校验
        if not bucket_path or not file_name:
            logger.error(
                "get_log_bucket_url failed: bucket_path or file_name is invalid"
            )
            return None

        # 路径拼接安全性改进
        try:
            key = os.path.join(bucket_path, file_name).replace(
                "\\", "/"
            )  # 防止路径注入
        except Exception as e:
            logger.error(
                f"get_log_bucket_url failed during path construction: {str(e)}"
            )
            return None

        # 获取预签名 URL
        url = None
        try:
            url = self.bucket_instance.get_presigned_url(
                Method="GET",
                Bucket=self.cos_bucket_name,
                Key=key,
                SignHost=False,
                Expired=expire_hours * 60 * 60,
            )
            logger.info("get_log_bucket_url succeeded: {}", url)
        except Exception as e:
            logger.error(f"get_log_bucket_url failed: {str(e)}")

        return url

    @retry(tries=3)
    def pull_algo_image(self, image_full_name: str):
        # 参数校验
        if not image_full_name:
            raise ValueError("image_full_name must be provided and non-empty")

        if self.get_platform_type() == "x86_64":
            actual_image_name = image_full_name
        else:
            actual_image_name = image_full_name.replace(
                "mogohub.tencentcloudcr.com/autocar/jinlv", "************/autocar/jinlv"
            )

        # 创建Docker客户端并确保资源释放
        try:
            client = docker.from_env()
            image = self.pull_image_with_sdk(client, actual_image_name)

            # 如果拉取失败，检查是否因为镜像不存在导致
            if image is None:
                tag_date_str = self.extract_tag_date(actual_image_name)
                if tag_date_str:
                    tag_date = tag_date_str.group()
                    current_date = (datetime.now() - timedelta(weeks=2)).strftime(
                        "%Y%m%d"
                    )
                    if tag_date >= current_date:
                        # 如果时间日期在两周之内，每隔60s重新执行拉取操作
                        self.retry_pull_with_backoff(client, actual_image_name)
                    else:
                        # 如果时间日期在两周之外，按照原镜像名进行拉取
                        logger.info(
                            f"Image {actual_image_name} doesn't exist, falling back to original image name."
                        )
                        image = self.pull_image_with_sdk(client, image_full_name)
                        if image is None:
                            logger.error(
                                f"Failed to pull fallback image: {image_full_name}"
                            )
                else:
                    logger.warning(
                        f"No valid date found in image tag: {actual_image_name}"
                    )
            # 返回镜像id
            image_id = image.short_id.split(":")[-1] if image else None
            return image_id
        except Exception as e:
            logger.error(f"Failed to pull image: {str(e)}")
            return None

    def pull_image_with_sdk(self, client, image_name):
        """封装镜像拉取逻辑"""
        try:
            image = client.images.pull(image_name)
            return image
        except ImageNotFound as e:
            logger.error(f"Image not found: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Failed to pull image: {str(e)}")
            return None

    def extract_tag_date(self, image_name):
        """从镜像名中提取日期标签"""
        try:
            tag_part = image_name.split(":")[-1]
            return re.search(r"\d{8}", tag_part)
        except IndexError:
            logger.warning(f"Invalid image name format: {image_name}")
            return None

    def retry_pull_with_backoff(self, client, image_name):
        """使用指数退避策略重试拉取镜像"""
        max_retries = 6  # 最大重试次数
        base_delay = 60  # 初始延迟时间（秒）
        time_start = time.time()

        for attempt in range(max_retries):
            delay = base_delay * (2**attempt)  # 指数退避
            if time.time() - time_start > 3600:  # 超过1小时停止重试
                logger.error(
                    f"Failed to pull image after 1 hour of retries: {image_name}"
                )
                return

            time.sleep(delay)
            pull_res = self.pull_image_with_sdk(client, image_name)
            if pull_res is True:
                logger.info(f"Successfully pulled image after retry: {image_name}")
                return

        logger.error(f"Failed to pull image after maximum retries: {image_name}")

    @retry(tries=3)
    def download_veh_config(self, config_name: str = None, target_path: str = None):
        # 确保目标目录存在并有正确的权限
        if not os.path.exists(target_path):
            os.makedirs(target_path, mode=0o777)
            logger.info(f"创建车辆配置目录: {target_path}")
        os.chmod(target_path, 0o777)
        logger.info(f"设置车辆配置目录权限: {oct(os.stat(target_path).st_mode)[-3:]}")

        veh_config_url = self.vehicle_config_url + config_name
        headers = {"jwt_token": self.jwt_token}
        res = requests.get(veh_config_url, headers=headers)
        res_json = res.json()
        logger.info("vehicle config download response: " + str(res_json))

        # 获取当前工作目录并记录
        current_dir = os.getcwd()
        logger.info(f"当前工作目录: {current_dir}")

        # 在目标目录中创建临时文件
        tar_path = os.path.join(target_path, config_name + ".tar")
        logger.info(f"下载车辆配置到: {tar_path}")

        try:
            urllib.request.urlretrieve(res_json["data"], tar_path)
            logger.info(f"车辆配置下载完成: {tar_path}")

            # 设置tar文件的权限
            os.chmod(tar_path, 0o777)
            logger.info(f"设置tar文件权限: {oct(os.stat(tar_path).st_mode)[-3:]}")

            with tarfile.open(tar_path, "r") as tar:
                tar.extractall(target_path)
            logger.info(f"车辆配置解压完成到: {target_path}")

            # 设置解压后目录的权限
            config_dir = os.path.join(target_path, config_name)
            if os.path.exists(config_dir):
                os.chmod(config_dir, 0o777)
                logger.info(
                    f"设置车辆配置目录权限: {oct(os.stat(config_dir).st_mode)[-3:]}"
                )

            os.remove(tar_path)
            logger.info("临时tar文件已删除")

        except Exception as e:
            logger.error(f"下载或解压车辆配置时出错: {str(e)}")
            if os.path.exists(tar_path):
                os.remove(tar_path)
            raise

        return True

    def transform_pinyin(self, cn_name: str = None):
        # 将map_name转换为拼音
        pinyin_list = lazy_pinyin(cn_name)
        result = "".join(pinyin_list)
        return result

    @retry(tries=3)
    def download_map(
        self,
        map_name_cn: str = None,
        map_type: str = None,
        map_version: str = None,
        download_path: str = None,
    ):
        map_name = self.transform_pinyin(map_name_cn)
        if map_type is None:
            map_type = "22"
        headers = {"jwt_token": self.jwt_token}
        params = {
            "bizType": map_type,
            "city": map_name_cn,
            "current": 1,
            "pageSize": 10,
            "artifactType": "MAP_ARTIFACT",
        }

        res = requests.post(self.gridmap_query_url, json=params, headers=headers)
        records = res.json()["data"]["records"]
        newest_record = records[0]
        id, version = newest_record["id"], newest_record["versionNo"]
        logger.info(f"map_id: {id}, version: {version}")

        # 确保下载目录存在并有正确的权限
        if not os.path.exists(download_path):
            os.makedirs(download_path, mode=0o777)
            logger.info(f"创建下载目录: {download_path}")
        os.chmod(download_path, 0o777)
        logger.info(f"设置下载目录权限: {oct(os.stat(download_path).st_mode)[-3:]}")

        if os.path.exists(
            download_path + "/map/" + map_name + "/" + map_name + "_config.yaml"
        ):
            with open(
                download_path + "/map/" + map_name + "/" + map_name + "_config.yaml",
                "r",
                encoding="utf-8",
            ) as file:
                data = yaml.safe_load(file)
            now_version = data["version"]
            logger.info(f"now_version: {now_version}")
            if re.sub(r"\D", "", now_version) < re.sub(r"\D", "", version):
                logger.info(f"{map_name} is up to date")
                shutil.rmtree(download_path + "/map/" + map_name)
            else:
                logger.info(f"{map_name} is not up to date")
                return True

        url = self.gridmap_download_url + "?type=MAP_ARTIFACT&id=" + str(id)
        res = requests.get(url, headers=headers)
        res_json = res.json()

        # 获取当前工作目录并记录
        current_dir = os.getcwd()
        logger.info(f"当前工作目录: {current_dir}")

        # 在下载目录中创建临时文件
        tar_path = os.path.join(download_path, map_name + ".tar")
        logger.info(f"下载地图到: {tar_path}")

        try:
            urllib.request.urlretrieve(res_json["data"], tar_path)
            logger.info(f"地图下载完成: {tar_path}")

            # 设置tar文件的权限
            os.chmod(tar_path, 0o777)
            logger.info(f"设置tar文件权限: {oct(os.stat(tar_path).st_mode)[-3:]}")

            with tarfile.open(tar_path, "r") as tar:
                tar.extractall(download_path)
            logger.info(f"地图解压完成到: {download_path}")

            # 设置解压后目录的权限
            map_dir = os.path.join(download_path, "map", map_name)
            if os.path.exists(map_dir):
                os.chmod(map_dir, 0o777)
                logger.info(f"设置地图目录权限: {oct(os.stat(map_dir).st_mode)[-3:]}")

            os.remove(tar_path)
            logger.info("临时tar文件已删除")

        except Exception as e:
            logger.error(f"下载或解压地图时出错: {str(e)}")
            if os.path.exists(tar_path):
                os.remove(tar_path)
            raise

        logger.info("download map success")
        return True

    @retry(tries=3)
    def download_sqlite_file(self, sqlite_name: str, target_path: str):
        """
        从COS下载sqlite文件到指定目录
        
        Args:
            sqlite_name: sqlite文件名
            target_path: 目标路径（casefile目录）
            
        Returns:
            bool: 下载是否成功
        """
        # 参数校验
        if not sqlite_name or not target_path:
            raise ValueError("sqlite_name 和 target_path 必须提供有效值")
        
        # 确保目标目录存在并有正确的权限
        if not os.path.exists(target_path):
            os.makedirs(target_path, mode=0o777)
            logger.info(f"创建sqlite文件目录: {target_path}")
        os.chmod(target_path, 0o777)
        logger.info(f"设置sqlite文件目录权限: {oct(os.stat(target_path).st_mode)[-3:]}")
        
        # 构建COS路径：hadmap/ + sqlite文件名
        if self.cos_url in sqlite_name:
            cos_path = sqlite_name.replace(self.cos_url, "")
        else:
            cos_path = f"hadmap/{sqlite_name}"
        # 固定保存为 db.sqlite，符合MapHandler的期望
        dest_file_path = os.path.join(target_path, "db.sqlite")
        
        logger.info(f"开始从COS下载sqlite文件: {cos_path} 到 {dest_file_path}")
        
        try:
            # 确保bucket_instance已初始化
            if not self.bucket_instance:
                self.init_bucket()
            
            if not self.bucket_instance:
                raise Exception("无法初始化云存储客户端")
            
            # 使用专门的COS桶下载sqlite文件
            response = self.bucket_instance.download_file(
                Bucket="autocar-mogosim-1255510688",
                Key=cos_path,
                DestFilePath=dest_file_path
            )
            logger.info(f"sqlite文件下载完成: {sqlite_name} -> {dest_file_path}")
            logger.info(f"下载响应: {response}")
            
            # 设置下载文件的权限
            os.chmod(dest_file_path, 0o777)
            logger.info(f"设置sqlite文件权限: {oct(os.stat(dest_file_path).st_mode)[-3:]}")
            
        except Exception as e:
            logger.error(f"下载sqlite文件时出错: {str(e)}")
            # 清理可能存在的不完整文件
            if os.path.exists(dest_file_path):
                os.remove(dest_file_path)
            raise
            
        return True


if __name__ == "__main__":
    platform_service = PlatformService()
    image_id = platform_service.pull_algo_image(
        "mogohub.tencentcloudcr.com/autocar/jinlv:MAP_RoboBus_B2_1_20250519_X86_dev"
    )
    print(image_id)
