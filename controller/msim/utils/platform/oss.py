import os
import shutil
from typing import Tuple, Dict, Any

import oss2 # TODO 待改为腾讯云对象存储

from utils.exception import UsimException
from utils.file_utils import get_setup_section_items
from utils.log import logger
from utils.metaclass import SingletonMeta
from utils.wraps import retry


class OSSClient(metaclass=SingletonMeta):
    endpoint = "https://oss-cn-hangzhou-internal.aliyuncs.com"

    class Bucket:
        def __init__(self, bucket_id: str = None, access_secret: str = None, auth_id: str = None, endpoint=None):
            self.bucket_id = bucket_id
            self.access_secret = access_secret
            self.auth_id = auth_id
            self.endpoint = endpoint
            self._oss2_bucket_instance = None

        @property
        def bucket(self):
            if self._oss2_bucket_instance is None:
                session = oss2.Session(pool_size=20)
                self._oss2_bucket_instance = oss2.Bucket(
                    oss2.Auth(self.auth_id, self.access_secret),
                    self.endpoint,
                    self.bucket_id,
                    enable_crc=False,
                    connect_timeout=120,
                    session=session
                )
            return self._oss2_bucket_instance

    def __init__(self):
        self._env = (os.environ.get("ENV") or "daily")
        self._service_bucket_map = {}
        self._auth_section = f"auth_{self._env}"
        self._init_bucket()

    def _init_bucket(self):
        auth_items = get_setup_section_items(self._auth_section)
        for key, value in auth_items:
            service_id, attr = key.split('_', 1)
            if service_id not in self._service_bucket_map:
                self._service_bucket_map[service_id] = self.Bucket(endpoint=self.endpoint)
            setattr(self._service_bucket_map[service_id], attr, value)

    def download(self, service_id: str, file_url: str, target_dir: str, missing_ok: bool = False) -> str:
        if service_id not in self._service_bucket_map:
            raise UsimException(10003, f"Service bucket not register: {service_id}")
        file_url = file_url.strip('/')
        file_base_name = os.path.basename(file_url)
        target_path = os.path.join(target_dir, file_base_name)

        if os.path.exists(target_path):
            logger.info(f"file {target_path} already exists, remove it")
            if os.path.isdir(target_path):
                shutil.rmtree(target_path)
            else:
                os.remove(target_path)
        try:
            logger.info(f"start download file {file_url}, from service {service_id} to {target_path}")
            if missing_ok:
                if not self._head_object(service_id, file_url):
                    logger.info(f"file {file_url} not exist, skip")
                    return ""
            self._download(service_id, file_url, target_path)
        except Exception as e:
            logger.error(f"download file {file_url} failed: {e}")
            raise UsimException(90002, file_url, str(e))

        return target_path

    def upload(self, service_id: str, source: str, target: str, file_size_threshold=None):
        file_size_map = {}

        if service_id not in self._service_bucket_map:
            raise UsimException(10003, f"bucket not register: {service_id}")

        for root, dirs, files in os.walk(source):
            for file in files:
                file_path = os.path.join(root, file)
                relative_path = file_path.replace(source, '')
                file_size_map[file_path] = os.path.getsize(file_path)
                if file_size_threshold and file_size_map[file_path] > file_size_threshold:
                    logger.info(f"file {file_path} size {file_size_map[file_path]} "
                                f"exceed threshold {file_size_threshold}, drop it")
                    continue
                try:
                    self._upload(service_id, file_path, target + relative_path)
                except Exception as e:
                    logger.error(f"upload file {source} to oss bucket-{service_id} path-{target} failed: {str(e)}")
                    raise UsimException(10011, f"upload file {source} failed: {str(e)}")

        logger.info(f"after upload: {file_size_map}")

    @retry(tries=3)
    def _upload(self, service_id: str, source: str, target: str):
        oss2.resumable_upload(self._service_bucket_map[service_id].bucket, target, source)

    @retry(tries=3, spec_exceptions=(oss2.exceptions.NoSuchKey,))
    def _head_object(self, service_id: str, file_url: str):
        return self._service_bucket_map[service_id].bucket.head_object(file_url)

    @retry(tries=3)
    def _download(self, service_id: str, oss_path: str, save_path: str):
        oss2.resumable_download(self._service_bucket_map[service_id].bucket, oss_path, save_path)

    def check_file_exist(self, service_id: str, file_url: str) -> Tuple[bool, Dict[str, Any]]:
        if service_id not in self._service_bucket_map:
            raise UsimException(10003, f"Service bucket not register: {service_id}")
        file_url = file_url.rstrip('/')
        try:
            result = self._head_object(service_id, file_url)
            return True, {"size": result.content_length, "last_modified": result.last_modified}
        except oss2.exceptions.NoSuchKey:
            return False, {}

    def get_bucket_info(self, service_id: str):
        return self._service_bucket_map.get(service_id)

