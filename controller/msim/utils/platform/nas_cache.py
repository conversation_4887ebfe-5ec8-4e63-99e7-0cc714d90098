import os
import shutil
from enum import Enum

from utils.file_utils import get_setup_value
from utils.lock import file_write_lock
from utils.loggers import get_server_logger
logger = get_server_logger()
from utils.metaclass import SingletonMeta


class CacheType(Enum):
    MAP = "maps"
    ALGO = "algos"
    CASE = "cases"


class NasCache(metaclass=SingletonMeta):

    def __init__(self):
        self._cache_mount = get_setup_value("system", "cache_mount_path")
        self._cacheable = False

        if self._cache_mount and os.path.exists(self._cache_mount):
            self._cacheable = True
            self._init()
        else:
            self._cacheable = False

    @staticmethod
    def _makedirs(path: str):
        # nas mount path mkdir will delete the original permission
        os.makedirs(path, exist_ok=True, mode=0o755)
        # os.chmod(path, mode=0o755)

    def _init(self):
        for sub_dir in CacheType:
            abs_sub_dir = os.path.join(self._cache_mount, sub_dir.value)
            if os.path.exists(abs_sub_dir):
                continue
            self._makedirs(abs_sub_dir)

    def can_cache(self) -> bool:
        return self._cacheable

    def add_cache(self, _type: CacheType, md5: str, path: str):
        if not self._cacheable:
            return
        dst = os.path.join(self._cache_mount, _type.value, md5).rstrip(os.path.sep)
        self.safe_copy(path, dst)

    def get_cache(self, _type: CacheType, md5: str) -> str:
        if not self._cacheable:
            return ''
        cache_path = os.path.join(self._cache_mount, _type.value, md5)
        cache_md5_file = cache_path + ".md5"
        if not os.path.exists(cache_md5_file):
            return ''
        file_cnt = len(os.listdir(cache_path))
        if not file_cnt:
            logger.warning(f"Cache {cache_path} is empty")
            return ''
        elif file_cnt == 1:
            return os.path.join(cache_path, os.listdir(cache_path)[0])
        else:
            return cache_path

    def get_case_cache(self, md5: str) -> str:
        return self.get_cache(CacheType.CASE, md5)

    def add_case_cache(self, md5: str, path: str):
        self.add_cache(CacheType.CASE, md5, path)

    def get_map_cache(self, md5: str) -> str:
        return self.get_cache(CacheType.MAP, md5)

    def add_map_cache(self, md5: str, path: str):
        self.add_cache(CacheType.MAP, md5, path)

    @staticmethod
    def safe_copy(src: str, dst: str):
        """
        Copy file or dir with lock
        param dst: is a directory, end with md5
        """
        md5 = os.path.basename(dst)
        with file_write_lock(dst, retry=False) as lock_file:
            if lock_file is None:
                logger.info(f"{md5} is locked, skip")
                return

            NasCache._makedirs(dst)

            if os.path.isdir(src):
                dst_dir = os.path.join(dst, os.path.basename(src))
                shutil.copytree(src, dst_dir)
            else:
                shutil.copy2(src, dst)

            md5_file = dst + ".md5"
            with open(md5_file, 'w') as f:
                f.write(md5)
