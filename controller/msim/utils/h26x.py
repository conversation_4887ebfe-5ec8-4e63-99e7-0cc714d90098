import concurrent.futures
import os
import threading
from typing import Optional

try:
    import ffmpeg
except ImportError:
    raise ImportError("Please install ffmpeg-python")

import numpy as np
import cv2
from mcap.reader import make_reader
from mcap.well_known import SchemaEncoding
from mcap_protobuf.reader import DecoderFactory
from mcap_protobuf.writer import Writer

try:
    from proto.drivers.image_pb2 import Image
except Exception as e:
    raise ImportError("Please make sure the proto file is generated and the PYTHONPATH is correct")


PROTOBUF_H26X_NAME = "udeer.drivers.H26xFrame"


def bgr_to_pb(bgr_frame: np.array, timestamp: int, width: int, height: int, frame_id: str) -> Optional[Image]:
    pb_msg = Image()
    pb_msg.utime = timestamp
    pb_msg.frame_id = frame_id
    pb_msg.header.timestamp_usec = timestamp
    pb_msg.header.camera_timestamp = timestamp
    pb_msg.width = width
    pb_msg.height = height
    pb_msg.step = width * 3
    pb_msg.data = bgr_frame.tobytes()
    pb_msg.compression = Image.RAW
    pb_msg.type = Image.BGR8
    return pb_msg


def bgr_to_jpeg(bgr_frame: np.array, timestamp: int, width: int, height: int, frame_id: str, quality=95) -> Optional[Image]:
    pb_msg = Image()
    pb_msg.utime = timestamp
    pb_msg.frame_id = frame_id
    pb_msg.header.timestamp_usec = timestamp
    pb_msg.header.camera_timestamp = timestamp
    pb_msg.width = width
    pb_msg.height = height
    pb_msg.step = width * 4
    _, jpeg_data = cv2.imencode('.jpg', bgr_frame, [int(cv2.IMWRITE_JPEG_QUALITY), quality])
    pb_msg.data = jpeg_data.tobytes()
    pb_msg.compression = Image.JPEG
    pb_msg.type = Image.MONO8
    return pb_msg


def read_msg(topics_info: dict, topic: str):
    target_mcap_file = topics_info[topic].get("output_file")
    writer = Writer(target_mcap_file)
    target_topic = topics_info[topic].get("target_topic")
    event = topics_info[topic].get("event")
    if event:
        event.wait()

    width = topics_info[topic].get("width")
    height = topics_info[topic].get("height")
    frame_id = topics_info[topic].get("frame_id")

    process = topics_info[topic].get("ffmpeg_process")

    while True:
        in_bytes = process.stdout.read(3 * width * height)

        if not in_bytes:
            break

        frame = np.frombuffer(in_bytes, np.uint8).reshape((height, width, 3))

        timestamp = topics_info[topic].get("timestamp").pop(0)
        logtime = topics_info[topic].get("logtime").pop(0)

        pb_msg = bgr_to_jpeg(frame, timestamp, width, height, frame_id)
        writer.write_message(target_topic, pb_msg, logtime)

    writer.finish()


def convert_h26x_mcap(mcap_source: str, output_path: str, overwrite: bool = False):
    """
    convert h26x mcap to N X images mcap (N is the number of frames in the h26x mcap file)
    :param mcap_source: source of the mcap file path (absolute path)
    :param output_path: output path (if source has multiple topics, output path should be a directory, or can be a file path)
    :param overwrite: overwrite the output file or not
    :return:
    """
    if not os.path.exists(mcap_source):
        raise FileNotFoundError(f"mcap source file not found: {mcap_source}")

    if os.path.isfile(output_path) and not overwrite:
        raise FileExistsError(f"output file already exists: {output_path}")

    output_mcap_file = None
    if output_path.endswith('.mcap'):
        output_mcap_file = output_path
        output_dir = os.path.dirname(output_path)
    else:
        output_dir = output_path

    os.makedirs(output_dir, exist_ok=True)
    need_change_topics = {}
    fd = None

    try:
        fd = open(mcap_source, "rb")
        reader = make_reader(fd, decoder_factories=[DecoderFactory()])

        summary = reader.get_summary()

        for c_id, channel in summary.channels.items():
            schema = summary.schemas.get(channel.schema_id)
            if schema.encoding == SchemaEncoding.Protobuf and schema.name == PROTOBUF_H26X_NAME:
                need_change_topics[channel.topic] = {}

        if not need_change_topics:
            raise ValueError("no h26x topic found in the mcap file")

        elif len(need_change_topics) > 1:
            if output_mcap_file:
                raise ValueError(f"multiple h26x topics {need_change_topics} found in the mcap file, "
                                 f"please specify a dir output not a file {output_mcap_file}")

        else:
            only_topic = list(need_change_topics.keys())[0]
            need_change_topics[only_topic].update({"output_file": output_mcap_file})

        for topic in need_change_topics:
            if need_change_topics[topic].get("output_file"):
                continue
            file_name = topic.replace('/', '_').replace('compressed_h26x', 'image_raw') + '.mcap'
            output_topic_file = output_mcap_file or os.path.join(output_dir, file_name)
            if os.path.exists(output_topic_file):
                raise FileExistsError(f"output topic file already exists: {output_topic_file}")
            need_change_topics[topic].update({"output_file": output_topic_file})

        for topic in need_change_topics:
            need_change_topics[topic].update(
                {
                    "event": threading.Event(),
                    "ffmpeg_process": (
                                        ffmpeg
                                        .input('pipe:')
                                        .output('pipe:', format='rawvideo', pix_fmt='bgr24')
                                        .overwrite_output()
                                        .run_async(pipe_stdin=True, pipe_stdout=True)
                                       ),
                    "target_topic": topic.replace("compressed/h26x", "image_raw"),
                    "width": None,
                    "height": None,
                    "timestamp": [],
                    "logtime": [],
                    "frame_id": '',
                }
            )

        with concurrent.futures.ThreadPoolExecutor() as executor:
            futures = []
            for topic in list(need_change_topics.keys()):
                futures.append(executor.submit(read_msg, need_change_topics, topic))
            first_i_frame = {t: False for t in need_change_topics.keys()}
            for pb_msg in reader.iter_decoded_messages(topics=list(need_change_topics.keys())):
                if not first_i_frame[pb_msg.channel.topic]:
                    if pb_msg.decoded_message.frame_type == pb_msg.decoded_message.FrameType.I_FRAME:
                        first_i_frame[pb_msg.channel.topic] = True
                    else:
                        continue
                width = pb_msg.decoded_message.width
                height = pb_msg.decoded_message.height
                frame_id = pb_msg.decoded_message.frame_id
                need_change_topics[pb_msg.channel.topic].update({"width": width, "height": height, "frame_id": frame_id})
                need_change_topics[pb_msg.channel.topic].get("ffmpeg_process").stdin.write(pb_msg.decoded_message.data)
                need_change_topics[pb_msg.channel.topic].get("event").set()
                need_change_topics[pb_msg.channel.topic].get("logtime").append(pb_msg.message.log_time)
                need_change_topics[pb_msg.channel.topic].get("timestamp").append(pb_msg.decoded_message.timestamp)

            for topic, topic_info in need_change_topics.items():
                need_change_topics[pb_msg.channel.topic].get("event").set()
                topic_info.get("ffmpeg_process").stdin.close()

            executor.shutdown()

    except Exception as e:
        raise e

    finally:
        if fd is not None:
            fd.close()
        for topic, topic_info in need_change_topics.items():
            topic_info.get("ffmpeg_process").stdin.close()
            topic_info.get("ffmpeg_process").wait()
