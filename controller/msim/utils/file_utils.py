import configparser
import fnmatch
import os
import tarfile
import zipfile
from functools import lru_cache
from typing import List, Tuple

from utils.loggers import get_server_logger
# 获取服务器日志记录器
logger = get_server_logger()
from utils.path import SETUP_PATH


@lru_cache(maxsize=3)
def parse_ini_file(file_path):
    """
    Parse .ini file
    :param file_path: .ini file path
    :return: config object
    """
    config = configparser.ConfigParser()
    config.read(file_path)
    return config


def get_setup_value(section: str, key: str):
    """
    Get value from setup.ini
    :param section: section name
    :param key: key name
    :return: value
    """
    config = parse_ini_file(SETUP_PATH)
    return config.get(section, key)


def get_setup_section_items(section) -> List[Tuple[str, str]]:
    """
    Get value from setup.ini
    :param section: section name
    :return: List[Tuple[str, str]]
    """
    config = parse_ini_file(SETUP_PATH)
    return config.items(section)


def create_directory(paths: [List, str]):
    """
    Create dir
    :param paths: dir path
    :return:
    """
    if isinstance(paths, str):
        paths = [paths]
    try:
        for path in paths:
            if not os.path.exists(path):
                os.makedirs(path)
    except OSError as e:
        logger.error(f"Create common path failed: {str(e)}")
        raise e


def find_files(directory, pattern):
    matches = []
    for root, dirnames, filenames in os.walk(directory):
        for filename in filenames:
            if fnmatch.fnmatch(filename, pattern):
                matches.append(os.path.join(root, filename))
    return matches


def unzip_to_dest(zip_file, target_dir):
    """
    :param: zip_file
    :param: target_dir
    :return: unzip file list
    """
    unzip_file_list = []
    with zipfile.ZipFile(zip_file, 'r') as f:
        for file in f.namelist():
            f.extract(file, target_dir)
            unzip_file_list.append(os.path.join(target_dir, file))
    return unzip_file_list


def untar_to_dest(tar_gz_file, target_dir):
    """
    :param: tar_gz_file: .tar.gz 文件路径
    :param: target_dir: 解压目标目录
    :return: 解压后的文件列表
    """
    with tarfile.open(tar_gz_file, 'r:gz') as tar:
        tar.extractall(target_dir)
