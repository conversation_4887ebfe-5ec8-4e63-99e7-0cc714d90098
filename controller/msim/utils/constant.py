import enum


class PlatformName:
    UDATA = "udata"
    USIM = "usim"
    UMAP = 'umap'
    FMS = 'fms'


class ClientName:
    VALIDATOR = 'validator'
    ENGINE = 'engine'
    ALGORITHM = 'algo'


class RunActionType(enum.Enum):
    REPROCESS = 'reprocess'
    SIMULATING = 'simulating'
    SIMPLE_COMPUTING = 'simple_computing'
    DAG_COMPUTING = 'dag_computing'


class States(enum.Enum):
    """Client（Task） state constants."""
    # Client is uninitialized.
    UNINITIALIZED = 0
    # Client is initialized and connected to the server.
    INIT = 1
    # C<PERSON> has parsed the task param
    READY = 2
    # C<PERSON> is running the task
    EXECUTING = 3
    # <PERSON><PERSON> has finished the task
    FINISHED = 4
    # <PERSON><PERSON> has handled the exception
    ABNORMAL = 5
    # Client is cleaning up the task
    CLEANUP = 6


class ReportStates(enum.Enum):
    PREPARING = "preparing"
    RUNNING = "running"
    EVALUATING = "evaluating"
    CANCELING = "canceling"
    UPLOADING = "uploading"
    FINISH = "finish"


class ClientAction(enum.Enum):
    """
    Client action constants.
    """
    INITIALISE = 0
    EXECUTE = 1
    FINISH = 2
    CLEANUP = 3


class DataBasename:
    """Data basename constants."""
    # Data basename of the task
    TASK = 'task'
    # Data basename of the case
    CASES = 'cases'
    # Data basename of the map
    MAPS = 'maps'
    # Data basename of the vehicle
    VEHICLE = 'vehicle'
    # Data basename of the runtime
    EXECUTOR = 'executor'
    # Data basename of the validator
    VALIDATORS = 'validators'
    # Data basename of the deb
    ALGO = 'algo'
    # Data basename of the log
    LOG = 'log'
    # Data basename of the log
    RECORD = 'record'
    # Data basename of the log
    RESULT = 'result'
    # Data basename of the log
    ARTIFACTS = 'artifacts'
    # DATA basename of the custom
    CUSTOM = 'custom'
    # DATA basename of the case cache
    CASE_CACHE = 'case_cache'


class DataType:
    """Data type constants."""
    COMMON = 'common'
    EXCLUSIVE = 'exclusive'


class ServerMsgKeys:
    """
    Server response keys constants.
    """
    ACTION = "action"
    ID = "task_id"
    DATA = "data"


class ClientMsgKeys:
    """
    Client request keys constants.
    """
    STATE = "state"
    CLIENT_ID = "client_id"
    TASK_ID = "task_id"
    DATA = "data"


class CaseType:
    WORLD_SIM = "worldsim"
    LOG_SIM = "logsim"


class EnvironmentType(enum.Enum):
    TEST = "test"
    DAILY = "daily"
    PRE = "pre"
    PROD = "prod"
    DEV = "dev"


class RunMode(enum.Enum):
    ONLINE = "online"
    LOCAL = "local"

# TODO: need to remove this
class ProjectType(enum.Enum):
    PDC = "pdc"
    PERCEPTION = "perception"
    LOCALIZATION = "localization"
    UMAP = "umap"
