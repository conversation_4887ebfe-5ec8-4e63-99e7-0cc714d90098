# /usr/bin/python3
# -*- coding: utf-8 -*-

import os
import socket
import time
import requests

from utils.loggers import LoggerProxy

logger = LoggerProxy("player")
from utils.file_utils import get_setup_value


warn_url = get_setup_value("warnreport", "warn_url")
headers_put = {"Content-Type": "application/json"}


def get_host_name():
    global host_name
    try:
        if host_name is None:
            host_name = socket.gethostname()
            logger.info("host name:{}", host_name)
    finally:
        return host_name


def get_worker():
    sim_ip = os.environ.get("SIM_IP", "0.0.0.0")
    return get_host_name() + "#" + sim_ip


def warn_report(title, e: Exception = None):
    if os.environ["warn_notify_switch"] == str(False):
        return
    global headers_put
    err_msg = str(e) if e is not None else "无"
    if len(err_msg.encode("utf-8")) > 1800:
        err_msg = err_msg.encode("utf-8")[:1800].decode("utf-8", errors="ignore")
    content = (
        '警报<font color="warning">警报</font>，请相关同事注意。\n \
        >环境: <font color="warning">'
        + "mogosim_engine"
        + ":"
        + os.environ["SIM_ENV"]
        + '</font> \n \
        >主机: <font color="comment">'
        + get_worker()
        + '</font> \n \
        >时间: <font color="comment">'
        + time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        + '</font> \n\
        >标题: <font color="comment">'
        + title
        + '</font> \n\
        >异常: <font color="comment">'
        + err_msg
        + " </font>"
    )
    send_values = {
        "msgtype": "markdown",
        "markdown": {"content": content},
    }
    response = requests.post(warn_url, json=send_values, headers=headers_put)
    logger.info("url %s params %s response %s" % (warn_url, send_values, response))
    response.close()
