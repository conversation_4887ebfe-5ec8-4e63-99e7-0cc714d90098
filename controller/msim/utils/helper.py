#  Copyright (C) Mogo.ai Information and Technology Co.Ltd 
#
#  All rights reserved.
#
#  This document contains proprietary information belonging to Mogo.ai Co.Ltd.
#  Passing on and copying of this document, and communication of its contents
#  is not permitted without prior written authorization.
#
#
#  <AUTHOR>
#  @date 10/13/23, 5:16 PM
from __future__ import annotations

import base64
import importlib
import json
import os
import shutil
import subprocess
import sys
from pathlib import Path
from typing import List, Optional

import jinja2

from utils.exception import UsimException
from utils.file_utils import get_setup_value
from utils.loggers import get_server_logger
logger = get_server_logger()
from utils.path import get_share_path


def dynamic_import(module_path: str, cls: str) -> Optional[type]:
    module = importlib.import_module(module_path)
    return getattr(module, cls) if hasattr(module, cls) else None


def base64_encode(content: str) -> str:
    return base64.b64encode(content.encode('utf-8')).decode('utf-8')


def base64_decode(content: str) -> str:
    return base64.b64decode(content.encode('utf-8')).decode('utf-8')


def git_clone(url: str, branch: str = "", commit_id: str = "") -> str | None:
    """
    url: e.g. codeup.aliyun.com/udeer/udeer.git
    """
    # handle url
    if url.startswith("https"):
        elements = url.replace("https://", "").split("/")
        domain = elements[0]
        project_name = elements[-1].replace(".git", "")
        repository = "/".join(elements[1:])

    elif url.startswith("git"):
        elements = url.replace("git@", "").split(":")
        domain = elements[0]
        project_name = elements[-1].split('/')[-1].replace(".git", "")
        repository = elements[-1]
    else:
        raise Exception(f"Unknown git url {url}")

    username = get_setup_value("git", "username")
    password = base64_decode(get_setup_value("git", "password")).replace("@", "%40")
    dest = Path(get_share_path()).joinpath("code", domain, project_name)
    if dest.exists():
        shutil.rmtree(str(dest))
    dest.mkdir(parents=True, exist_ok=True)
    git_clone_cmd = f"git clone https://{username}:{password}@{domain}/{repository} {str(dest)}"
    subprocess.run(git_clone_cmd, shell=True, check=True)
    ref = commit_id or branch
    if ref:
        checkout_cmd = f"cd {str(dest)} && git fetch origin {ref} &&git checkout {ref}"
        subprocess.run(checkout_cmd, shell=True, check=True)

    if dest.exists():
        return str(dest)
    raise Exception(f"Unknown reason that git {url} {branch} can not clone.")


def gen_docker_json(registry: str, username: str, password: str) -> str:
    dest = Path(get_share_path()).joinpath("docker", "config", "config.json")
    dest.parent.mkdir(parents=True, exist_ok=True)
    json_content = {
        "auths": {
            registry: {
                "username": username,
                "password": password,
                "auth": base64.b64encode(f"{username}:{password}".encode('utf-8')).decode('utf-8')
            }
        }
    }
    with open(str(dest), 'w') as f:
        f.write(json.dumps(json_content))
    return str(dest)


def download_dependency(algo_version: str, download_path: str, package_file) -> None:
    """
    下载算法极其依赖
    :param algo_version: 算法版本
    :param download_path: 下载路径
    :param package_file: 下载的配置文件toml
    :return: None
    """

    def generate_file():
        with open(package_file, 'r') as raw_f:
            render_content = jinja2.Template(raw_f.read()).render(version=algo_version)

        file_path = '/tmp/package.toml'
        # 将配置写入文件
        with open(file_path, 'w') as config_file:
            config_file.write(render_content)

        return file_path

    toml_path = generate_file()
    down_cmd = f"rm -rf {download_path.rstrip(os.sep)}/* && python3 /home/<USER>/get-dependencies.py -f {toml_path} -p {download_path} --no-check"

    logger.info(f"download dependency cmd: {down_cmd}")
    retry_time = 3
    while retry_time > 0:
        try:
            proc = subprocess.Popen(down_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True)
            out, err = proc.communicate(timeout=1000)
            break
        except Exception as e:
            retry_time += 1
    else:
        raise UsimException(90001, f"timeout")

    if proc.returncode != 0:
        error_msg = out.decode().strip() if out else ""
        error_msg += err.decode().strip() if err else ""
        raise UsimException(90001, f"{error_msg}")


def download_bag_from_udata(
        urls: List[str],
        output: str,
        time_range: str = None,
        topics: List[str] = None,
        *,
        json_file: str = "",
        algo_path: str = "",
        source_platform: str = "udata"
) -> None:
    """
    Download bag from udata
    """
    from utils.platform.oss import OSSClient

    bucket_info = OSSClient().get_bucket_info(source_platform)
    bucket_id = bucket_info.bucket_id
    convert_urls = list(map(lambda url: url if url.startswith("oss://") else f"oss://{bucket_id}/{url}", urls))
    if not convert_urls:
        raise UsimException(20004, f"bag oss url is empty")
    auth_json = json.dumps({
        "accessKeyID": bucket_info.auth_id,
        "accessKeySecret": bucket_info.access_secret,
        "endpoint": bucket_info.endpoint
    })

    cmd = [
        "ulogutil",
        "--urls", ','.join([f"\'{url}\'" for url in convert_urls]),
        "--output", output,
        "--tmp-path", os.path.join(output, "tmp"),
        "-c", f"\'{auth_json}\'",
        "--convert-h26x"
    ]
    if json_file:
        cmd.extend(["--json-path", json_file])
    if time_range:
        cmd.extend(["--filter", time_range])
    if topics:
        cmd.extend(["--topics", ",".join(topics)])

    logger.info(f"download bag from udata: {' '.join(cmd)}")
    python_path = ':'.join(sys.path)

    if algo_path:
        environ = get_algo_environ(algo_path)
        python_path = environ.get("PYTHONPATH", "") + ":" + python_path

    ulogutil_cmd = f"export PYTHONPATH={python_path} && cd {output} && {' '.join(cmd)}"
    proc = subprocess.Popen(ulogutil_cmd, shell=True, stderr=subprocess.PIPE,
                            stdout=subprocess.PIPE, executable='/bin/bash')
    _, err = proc.communicate()
    if proc.returncode != 0:
        logger.error(err.decode().strip())
        raise UsimException(90004, f"{convert_urls} please check the network or path exist.")
        # raise UsimException(10019, f"{convert_urls}", f"please check the whether the path exist or not.")


def get_rt_type(algo_path: str):
    interface_type_file = os.path.join(algo_path, "udeer-common/scripts/interface.conf")
    if os.path.exists(interface_type_file):
        with open(interface_type_file, 'r') as f:
            content = f.read().lower().strip()
    else:
        content = 'ros2'
    return content


def get_algo_environ(algo_path: str) -> dict:
    """
    source algorithm just for pb && messages
    """
    environ = os.environ.copy()
    proto_py_path = os.path.join(algo_path, "proto/lib/python3.8.10/dist-packages")
    if os.path.exists(proto_py_path):
        environ["PYTHONPATH"] = proto_py_path + ":" + environ.get("PYTHONPATH", '')

    message_path = os.path.join(algo_path, "messages")
    cyber_path = os.path.join(algo_path, "cyber")
    ld_library_path = ""
    python_path = ""
    ament_prefix_path = ""
    if os.path.exists(message_path):
        for msg_type in os.listdir(message_path):
            msg_path = os.path.join(message_path, msg_type)
            if os.path.isdir(msg_path):
                sys.path.insert(0, f"{msg_path}/lib/python3.8/site-packages")
                ld_library_path = f"{msg_path}/lib:" + ld_library_path
                python_path = f"{msg_path}/lib/python3.8/site-packages:" + python_path
                ament_prefix_path = f"{msg_path}" + ament_prefix_path

        environ["LD_LIBRARY_PATH"] = ld_library_path + environ.get("LD_LIBRARY_PATH", '')
        environ["PYTHONPATH"] = python_path + environ.get("PYTHONPATH", '')
        environ["AMENT_PREFIX_PATH"] = ament_prefix_path + environ.get("AMENT_PREFIX_PATH", '')
        environ["COLCON_PREFIX_PATH"] = message_path + ":" + environ.get("COLCON_PREFIX_PATH", '')

    if os.path.exists(cyber_path):
        environ["LD_LIBRARY_PATH"] = (
                os.path.join(cyber_path, "lib", "libcyber.so") + ":" + environ.get("LD_LIBRARY_PATH", ''))
        environ["PYTHONPATH"] = os.path.join(cyber_path, "python") + ":" + environ.get("PYTHONPATH", '')

    content = get_rt_type(algo_path)
    environ["USIM_RT_TYPE"] = content

    if environ.get("PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION"):
        del environ["PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION"]
    return environ


if __name__ == "__main__":
    from utils.path import get_algo_path
    try:
        version = sys.argv[1]
        download_dependency(version, os.path.join(get_algo_path(), str(0)), os.path.join(os.path.dirname(__file__), "../config/default_package.toml"))
    except Exception as e:
        print("error: ", e)
    finally:
        sys.exit(0)
