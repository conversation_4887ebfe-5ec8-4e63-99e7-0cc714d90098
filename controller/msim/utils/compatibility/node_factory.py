import importlib
import inspect
import os
import threading
from abc import ABC, abstractmethod
from enum import Enum
from typing import Optional, Dict, Callable, Any

from google.protobuf.message import Message

from utils.loggers import LoggerProxy

logger = LoggerProxy("player")


class SingletonMeta(type):
    _instances = {}

    def __call__(cls, *args, **kwargs):
        if cls not in cls._instances:
            cls._instances[cls] = super().__call__(*args, **kwargs)
        return cls._instances[cls]


def dynamic_import(module_path: str, cls: str) -> Optional[type]:
    module = importlib.import_module(module_path)
    return getattr(module, cls) if hasattr(module, cls) else None


class RunEnvType(Enum):
    ROS1 = "ros1"
    ROS2 = "ros2"
    CYBER = "cyber"


class WriterBase(ABC):
    @property
    def topic(self) -> str:
        raise NotImplementedError

    @property
    def type(self) -> Any:
        raise NotImplementedError

    @abstractmethod
    def write(self, data: Any):
        raise NotImplementedError

    def destroy(self):
        pass


class TimeWriter(ABC):
    @abstractmethod
    def write(self, sec: int, nanosec: int):
        raise NotImplementedError


class ReaderBase(ABC):
    @property
    def topic(self) -> str:
        raise NotImplementedError

    @property
    def type(self) -> Any:
        raise NotImplementedError

    def destroy(self):
        pass


class TimerBase(ABC):
    @property
    def timer_period_sec(self) -> float:
        raise NotImplementedError

    def destroy(self):
        pass


class ServiceBase(ABC):
    @property
    def name(self) -> str:
        raise NotImplementedError

    def destroy(self):
        pass


class ClientBase(ABC):
    @property
    def name(self) -> str:
        raise NotImplementedError

    @abstractmethod
    def send(self, data: Any, timeout=None):
        raise NotImplementedError

    def destroy(self):
        pass


class NodeBase(metaclass=SingletonMeta):

    def __init__(self, name: str = "", remap: Dict[str, str] = None, **kwargs):
        import uuid

        self._rand_id = str(uuid.uuid4())[:8]
        self._name = name or f"mogosim_node_{self._rand_id}"
        self._params: Dict[str, Any] = {}

    @abstractmethod
    def ok(self) -> bool:
        raise NotImplementedError

    @property
    def name(self) -> str:
        return self._name

    def set_parameter(self, key: str, value: Any):
        self._params[key] = value

    def set_parameters(self, params: Dict[str, Any]):
        self._params.update(params)

    def get_param(self, name, alternative_value=None):
        return self._params.get(name, alternative_value)

    @abstractmethod
    def get_time(self) -> float:
        """
        return sec
        """
        raise NotImplementedError

    @abstractmethod
    def create_time_writer(self, topic: str, *args, **kwargs) -> TimeWriter:
        raise NotImplementedError

    @abstractmethod
    def create_reader(
        self, topic: str, callback: Callable[[Any], None], *args, **kwargs
    ) -> ReaderBase:
        raise NotImplementedError

    @abstractmethod
    def create_writer(self, topic, msg_type: Any, *args, **kwargs) -> WriterBase:
        raise NotImplementedError

    @abstractmethod
    def create_timer(
        self, frequency: float, callback: Callable[[], None], *args, **kwargs
    ) -> TimerBase:
        raise NotImplementedError

    @abstractmethod
    def create_service(
        self,
        srv_name: str,
        callback: Callable[[Any, Any], Any],
        req_type: Any,
        rep_type: Any,
        *args,
        **kwargs,
    ) -> ServiceBase:
        raise NotImplementedError

    @abstractmethod
    def create_client(
        self, srv_name: str, req_type: Any, rep_type: Any, *args, **kwargs
    ) -> ClientBase:
        raise NotImplementedError

    @abstractmethod
    def start(self):
        raise NotImplementedError

    @abstractmethod
    def destroy(self):
        raise NotImplementedError

    @staticmethod
    def check_proto_args(func: Callable[..., None], idx: int = 0):
        sig = inspect.signature(func)
        if len(list(sig.parameters.values())) <= idx:
            raise ValueError(f"callback function should have more than {idx} argument")
        if not len(sig.parameters):
            raise TypeError("callback function should have more than one argument")
        if not issubclass(list(sig.parameters.values())[idx].annotation, Message):
            raise TypeError(
                f"callback function '{func.__name__}' should have type hints of protobuf message"
            )
        return list(sig.parameters.values())[idx].annotation

    def __del__(self):
        self.destroy()


class NodeFactory(metaclass=SingletonMeta):

    def __init__(self):
        self._node_types = {
            RunEnvType.ROS1: "utils.compatibility.ros1.node::RosNode",
            RunEnvType.ROS2: "utils.compatibility.ros2.node::RosNode",
            RunEnvType.CYBER: "utils.compatibility.cyber.node::CyberNode",
        }
        self._instances: Dict[RunEnvType, NodeBase] = {}

        usim_rt_type = os.environ.get("USIM_RT_TYPE", "")
        self._env_default_type = (
            RunEnvType.ROS2 if usim_rt_type.lower() == "ros2" else RunEnvType.CYBER
        )

    def _get_run_env(self, run_env: Optional[RunEnvType] = None) -> RunEnvType:
        if run_env is None:
            logger.warning(
                f"No run env specified, using default environment: {self._env_default_type.value}"
            )
            return self._env_default_type
        return run_env

    def get_node(
        self,
        run_env: Optional[RunEnvType] = None,
        params: Optional[Dict[str, Any]] = None,
    ) -> NodeBase:
        run_env = self._get_run_env(run_env)
        if run_env in self._instances:
            return self._instances[run_env]

        node_type = self.get_node_class(run_env)
        node_instance = node_type(**params) if params else node_type()
        self._instances[run_env] = node_instance
        return node_instance

    def get_node_class(self, run_env: Optional[RunEnvType] = None) -> type:
        run_env = self._get_run_env(run_env)
        import_path = self._node_types.get(run_env)
        if not import_path:
            logger.error(
                f"Cannot find node implementation for run environment: {run_env.value}"
            )
            raise ValueError(f"Unsupported run env: {run_env.value}")

        module_path, class_name = import_path.rsplit("::")
        logger.info(
            f"Loading node class - Environment: {run_env.value}, Module: {module_path}, Class: {class_name}"
        )
        import_class = dynamic_import(module_path, class_name)
        if import_class is None:
            raise ImportError(f"Failed to import {class_name} from {module_path}")
        return import_class

    def destroy(self):
        for instance in self._instances.values():
            try:
                instance.destroy()
            except Exception as e:
                logger.error(f"node destroy error: {str(e)}")

        self._instances = {}

    def __del__(self):
        self.destroy()
