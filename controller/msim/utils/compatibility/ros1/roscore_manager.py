import os
import signal
import subprocess
import time
import rosgraph
from typing import Optional

from utils.loggers import LoggerProxy

logger = LoggerProxy("player")


class RoscoreManager:
    """ROS Master (roscore) 管理器"""

    def __init__(self):
        self._roscore_process: Optional[subprocess.Popen] = None
        self._is_running = False
        self._ros_master_check = rosgraph.is_master_online

    def start(self, timeout: float = 10.0) -> bool:
        """
        启动 roscore 进程

        参数:
            timeout (float): 等待 roscore 启动的超时时间(秒)

        返回:
            bool: 启动是否成功
        """
        if self._is_running:
            logger.warning("roscore 已经在运行")
            return True

        try:
            # 使用 subprocess.PIPE 捕获输出
            self._roscore_process = subprocess.Popen(
                ["roscore"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                preexec_fn=os.setsid,
            )

            # 等待 roscore 完全启动
            start_time = time.time()
            while time.time() - start_time < timeout:
                if self._check_roscore_running():
                    self._is_running = True
                    logger.info("roscore 启动成功")
                    return True
                time.sleep(0.1)

            self.stop()  # 超时则停止进程
            logger.error(f"roscore 启动超时 (>{timeout}s)")
            return False

        except Exception as e:
            logger.error(f"启动 roscore 失败: {str(e)}")
            self.stop()
            return False

    def stop(self) -> None:
        """
        停止 roscore 进程
        """
        if self._roscore_process:
            try:
                # 向进程组发送终止信号
                os.killpg(os.getpgid(self._roscore_process.pid), signal.SIGTERM)
                self._roscore_process.wait(timeout=5)  # 等待进程结束
            except Exception as e:
                logger.error(f"停止 roscore 时出错: {str(e)}")
            finally:
                self._roscore_process = None
                self._is_running = False
                logger.info("roscore 已停止")

    def _check_roscore_running(self) -> bool:
        """
        检查 roscore 是否正在运行

        返回:
            bool: roscore 是否在运行
        """
        try:
            return self._ros_master_check()
        except Exception:
            return False

    def __del__(self):
        """
        析构函数，确保进程被清理
        """
        self.stop()
