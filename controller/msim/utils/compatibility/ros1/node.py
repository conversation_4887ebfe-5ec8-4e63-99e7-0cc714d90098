#!/usr/bin/env python3

"""
本模块提供 ROS1 节点的兼容性工具。
"""

import time
from typing import Any, Callable, Optional, Type

import rospy
from rosgraph_msgs.msg import Clock
from std_msgs.msg import Header

from utils.compatibility.node_factory import NodeBase
from utils.loggers import get_server_logger
logger = get_server_logger()


class RosWriter:
    """ROS 写入器类，用于发布消息到 ROS 话题。"""
    
    def __init__(self, topic: str, publisher: rospy.Publisher, pb_type: Optional[Type] = None, shm_mode: bool = False):
        """
        初始化 ROS 写入器。

        参数:
            topic (str): 要发布的话题
            publisher (rospy.Publisher): ROS 发布器实例
            pb_type (Optional[Type]): protobuf 消息类型
            shm_mode (bool, optional): 共享内存模式标志
        """
        self._topic = topic
        self._publisher = publisher
        self._pb_type = pb_type
        self._shm_mode = shm_mode

    def write(self, data: Any) -> None:
        """
        向 ROS 话题写入数据。

        参数:
            data (Any): 要发布的数据
        """
        if self._pb_type and hasattr(data, 'SerializeToString'):
            pass
        else:
            self._publisher.publish(data)


class RosTimeWriter:
    """ROS 时间写入器类，用于发布时间消息。"""

    def __init__(self, publisher: rospy.Publisher):
        """
        初始化 ROS 时间写入器。

        参数:
            publisher (rospy.Publisher): ROS 发布器实例
        """
        self._publisher = publisher
        self._clock_msg = Clock()

    def write(self, sec: int, nanosec: int) -> None:
        """
        向 ROS 话题写入时间。

        参数:
            sec (int): 时间的秒数部分
            nanosec (int): 时间的纳秒部分
        """
        self._clock_msg.clock = rospy.Time(sec, nanosec)
        self._publisher.publish(self._clock_msg)


class RosReader:
    """ROS 读取器类，用于订阅 ROS 话题。"""
    
    def __init__(self, topic: str, subscriber: rospy.Subscriber, msg_type: Optional[Type] = None):
        """
        初始化 ROS 读取器。

        参数:
            topic (str): 要订阅的话题
            subscriber (rospy.Subscriber): ROS 订阅器实例
            msg_type (Optional[Type]): 消息类型
        """
        self._topic = topic
        self._subscriber = subscriber
        self._msg_type = msg_type

    @property
    def topic(self) -> str:
        """获取话题名称。"""
        return self._topic

    @property
    def type(self) -> Any:
        """获取消息类型。"""
        return self._msg_type


class RosClient:
    """ROS 客户端类，用于调用 ROS 服务。"""
    
    def __init__(self, name: str, client: rospy.ServiceProxy, srv_type: Any):
        """
        初始化 ROS 客户端。

        参数:
            name (str): 服务名称
            client (rospy.ServiceProxy): ROS 服务代理实例
            srv_type (Any): 服务类型
        """
        self._name = name
        self._client = client
        self._srv_type = srv_type

    @property
    def name(self) -> str:
        """获取服务名称。"""
        return self._name

    @property
    def type(self) -> Any:
        """获取服务类型。"""
        return self._srv_type

    def send(self, req_msg: Any, timeout: float = None) -> Any:
        """
        发送请求到 ROS 服务。

        参数:
            req_msg (Any): 请求消息
            timeout (float, optional): 服务调用超时时间

        返回:
            Any: 服务的响应

        异常:
            RuntimeError: 服务调用失败时抛出
        """
        try:
            if timeout:
                rospy.wait_for_service(self._name, timeout=timeout)
            return self._client(req_msg)
        except (rospy.ServiceException, rospy.ROSException) as e:
            raise RuntimeError(f"服务调用失败: {str(e)}")


class RosService:
    """ROS 服务类，用于提供 ROS 服务。"""
    
    def __init__(self, name: str, service: rospy.Service, srv_type: Any):
        """
        初始化 ROS 服务。

        参数:
            name (str): 服务名称
            service (rospy.Service): ROS 服务实例
            srv_type (Any): 服务类型
        """
        self._name = name
        self._service = service
        self._srv_type = srv_type

    @property
    def name(self) -> str:
        """获取服务名称。"""
        return self._name

    @property
    def type(self) -> Any:
        """获取服务类型。"""
        return self._srv_type


class RosNode(NodeBase):
    """ROS 节点类，提供 ROS 节点的基本功能。"""

    def __init__(self, name: str, **kwargs):
        """
        初始化 ROS 节点。

        参数:
            name (str): 节点名称
            **kwargs: 节点初始化的额外参数
        
        异常:
            RuntimeError: 节点初始化失败时抛出
        """
        super().__init__(name)
        self._is_destroyed = False
        remap_args = [f"{k}:={v}" for k, v in kwargs.get("remap", {}).items()]
        
        try:
            rospy.set_param('use_sim_time', True)
        except ConnectionRefusedError as e:
            logger.error(f"节点 {name} 初始化失败: {str(e)}")
            self.destroy()
            raise RuntimeError(f"节点初始化失败: {str(e)}")
        
        logger.info(f"节点 {name} 初始化成功")
        self._reader_list = []
        self._writer_list = []
        self._service_list = []
        self._client_list = []
        self._timer_list = []
        self._time_writer = None

    def destroy(self) -> None:
        """销毁 ROS 节点，清理资源。"""
        if self._is_destroyed:
            return
            
        # rospy.signal_shutdown('节点已销毁')
        # while not rospy.is_shutdown():
        #     time.sleep(0.5)
        self._is_destroyed = True

    def create_reader(self, topic: str, callback: Callable[[Any], None], *, 
                     msg_type: Type, pb_type: Optional[Any] = None) -> RosReader:
        """
        创建 ROS 读取器。

        参数:
            topic (str): 要订阅的话题
            callback (Callable[[Any], None]): 回调函数
            msg_type (Type): 消息类型
            pb_type (Optional[Any]): protobuf 消息类型

        返回:
            RosReader: ROS 读取器实例
        """
        def pb_callback(raw_msg: Any, pb_type: Optional[Any]) -> None:
            try:
                data = raw_msg.data
                obs = pb_type()
                obs.ParseFromString(data)
                callback(obs)
            except Exception as e:
                logger.error(f"消息解析失败: {str(e)}")
        if pb_type:
            final_callback = lambda raw_msg: pb_callback(raw_msg, pb_type)
        else:
            final_callback = callback
        reader = RosReader(topic, rospy.Subscriber(topic, msg_type, final_callback), msg_type)
        self._reader_list.append(reader)
        return reader

    def create_writer(self, topic: str, msg_type: Any) -> RosWriter:
        """
        创建 ROS 写入器。

        参数:
            topic (str): 要发布的话题
            msg_type (Any): 消息类型

        返回:
            RosWriter: ROS 写入器实例
        """
        writer = RosWriter(topic, rospy.Publisher(topic, msg_type, queue_size=10), msg_type)
        self._writer_list.append(writer)
        return writer

    def create_time_writer(self, topic: str) -> RosTimeWriter:
        """
        创建 ROS 时间写入器。

        参数:
            topic (str): 要发布的话题

        返回:
            RosTimeWriter: ROS 时间写入器实例
        """
        publisher = rospy.Publisher(topic, Clock, queue_size=1)
        return RosTimeWriter(publisher)

    def create_timer(self, period: float, callback: Callable) -> rospy.Timer:
        """
        创建 ROS 定时器。

        参数:
            period (float): 定时器周期（秒）
            callback (Callable): 回调函数

        返回:
            rospy.Timer: ROS 定时器实例
        """
        timer = rospy.Timer(rospy.Duration(period), callback)
        self._timer_list.append(timer)
        return timer

    def create_service(self, name: str, srv_type: Any, handler: Callable) -> RosService:
        """
        创建 ROS 服务。

        参数:
            name (str): 服务名称
            srv_type (Any): 服务类型
            handler (Callable): 服务处理函数

        返回:
            RosService: ROS 服务实例
        """
        service = rospy.Service(name, srv_type, handler)
        ros_service = RosService(name, service, srv_type)
        self._service_list.append(ros_service)
        return ros_service

    def create_client(self, name: str, srv_type: Any) -> RosClient:
        """
        创建 ROS 客户端。

        参数:
            name (str): 服务名称
            srv_type (Any): 服务类型

        返回:
            RosClient: ROS 客户端实例
        """
        client = rospy.ServiceProxy(name, srv_type)
        ros_client = RosClient(name, client, srv_type)
        self._client_list.append(ros_client)
        return ros_client

    def get_time(self) -> float:
        """
        获取当前 ROS 时间。

        返回:
            float: 当前时间（秒）
        """
        now = rospy.Time.now()
        return now.to_sec()
