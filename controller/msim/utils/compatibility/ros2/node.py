#!/usr/bin/env python3

"""
本模块提供 ROS2 节点的兼容性工具。
"""

import sys
import os
import time
import threading
from typing import Any, Callable, Optional, Type, Dict, List

import rclpy
from rclpy.node import Node
from rclpy.publisher import Publisher
from rclpy.subscription import Subscription
from rclpy.timer import Timer
from rclpy.client import Client
from rclpy.service import Service
from rclpy.qos import QoSProfile, ReliabilityPolicy, HistoryPolicy
from rosgraph_msgs.msg import Clock
from std_msgs.msg import Header

from utils.compatibility.node_factory import NodeBase
from utils.loggers import LoggerProxy

logger = LoggerProxy("player")


class RosWriter:
    """ROS2 写入器类，用于发布消息到 ROS 话题。"""

    def __init__(
        self, topic: str, publisher: Publisher, pb_type: Optional[Type] = None
    ):
        """
        初始化 ROS2 写入器。

        参数:
            topic (str): 要发布的话题
            publisher (Publisher): ROS2 发布器实例
            pb_type (Optional[Type]): protobuf 消息类型
        """
        self._topic = topic
        self._publisher = publisher
        self._pb_type = pb_type

    @property
    def topic(self) -> str:
        """获取话题名称。"""
        return self._topic

    @property
    def type(self) -> Any:
        """获取消息类型。"""
        return self._publisher.msg_type

    def write(self, data: Any) -> None:
        """
        向 ROS2 话题写入数据。

        参数:
            data (Any): 要发布的数据
        """
        if self._pb_type and hasattr(data, "SerializeToString"):
            pass
        else:
            self._publisher.publish(data)


class RosTimeWriter:
    """ROS2 时间写入器类，用于发布时间消息。"""

    def __init__(self, publisher: Publisher):
        """
        初始化 ROS2 时间写入器。

        参数:
            publisher (Publisher): ROS2 发布器实例
        """
        self._publisher = publisher
        self._clock_msg = Clock()

    def write(self, sec: int, nanosec: int) -> None:
        """
        向 ROS2 话题写入时间。

        参数:
            sec (int): 时间的秒数部分
            nanosec (int): 时间的纳秒部分
        """
        self._clock_msg.clock.sec = sec
        self._clock_msg.clock.nanosec = nanosec
        self._publisher.publish(self._clock_msg)


class RosReader:
    """ROS2 读取器类，用于订阅 ROS 话题。"""

    def __init__(
        self, topic: str, subscriber: Subscription, msg_type: Optional[Type] = None
    ):
        """
        初始化 ROS2 读取器。

        参数:
            topic (str): 要订阅的话题
            subscriber (Subscription): ROS2 订阅器实例
            msg_type (Optional[Type]): 消息类型
        """
        self._topic = topic
        self._subscriber = subscriber
        self._msg_type = msg_type

    @property
    def topic(self) -> str:
        """获取话题名称。"""
        return self._topic

    @property
    def type(self) -> Any:
        """获取消息类型。"""
        return self._msg_type


class RosClient:
    """ROS2 客户端类，用于调用 ROS 服务。"""

    def __init__(self, name: str, client: Client, srv_type: Any):
        """
        初始化 ROS2 客户端。

        参数:
            name (str): 服务名称
            client (Client): ROS2 客户端实例
            srv_type (Any): 服务类型
        """
        self._name = name
        self._client = client
        self._srv_type = srv_type

    @property
    def name(self) -> str:
        """获取服务名称。"""
        return self._name

    @property
    def type(self) -> Any:
        """获取服务类型。"""
        return self._srv_type

    async def send(self, req_msg: Any, timeout: float = None) -> Any:
        """
        发送请求到 ROS2 服务。

        参数:
            req_msg (Any): 请求消息
            timeout (float, optional): 服务调用超时时间

        返回:
            Any: 服务的响应

        异常:
            RuntimeError: 服务调用失败时抛出
        """
        try:
            if timeout:
                if not await self._client.wait_for_service(timeout_sec=timeout):
                    raise RuntimeError(f"服务 {self._name} 在 {timeout} 秒内未就绪")
            future = self._client.call_async(req_msg)
            rclpy.spin_until_future_complete(self._client.node, future)
            return future.result()
        except Exception as e:
            raise RuntimeError(f"服务调用失败: {str(e)}")


class RosService:
    """ROS2 服务类，用于提供 ROS 服务。"""

    def __init__(self, name: str, service: Service, srv_type: Any):
        """
        初始化 ROS2 服务。

        参数:
            name (str): 服务名称
            service (Service): ROS2 服务实例
            srv_type (Any): 服务类型
        """
        self._name = name
        self._service = service
        self._srv_type = srv_type

    @property
    def name(self) -> str:
        """获取服务名称。"""
        return self._name

    @property
    def type(self) -> Any:
        """获取服务类型。"""
        return self._srv_type


class RosNode(NodeBase):
    """ROS2 节点类，提供 ROS2 节点的基本功能。"""

    # 默认QoS配置
    DEFAULT_QOS = QoSProfile(
        reliability=ReliabilityPolicy.RELIABLE,
        history=HistoryPolicy.KEEP_LAST,
        depth=10,
    )

    # 时间话题的默认QoS配置
    DEFAULT_CLOCK_QOS = QoSProfile(
        reliability=ReliabilityPolicy.RELIABLE, history=HistoryPolicy.KEEP_LAST, depth=1
    )

    def __init__(self, name: str, **kwargs):
        """
        初始化 ROS2 节点。

        参数:
            name (str): 节点名称
            **kwargs: 节点初始化的额外参数

        异常:
            RuntimeError: 节点初始化失败时抛出
        """
        NodeBase.__init__(self, name)

        # 初始化标志和资源列表
        self._is_destroyed = False
        self._node_name = name  # 保存节点名称，以便在初始化失败时使用
        self._reader_list = []
        self._writer_list = []
        self._service_list = []
        self._client_list = []
        self._timer_list = []
        self._time_writer = None
        self._ros_node = None
        self._executor = None
        self._spin_thread = None
        self._thread_running = False

        try:
            # 避免重复初始化
            if not rclpy.ok():
                rclpy.init()

            # 创建ROS2节点实例
            params = kwargs.get("parameter_overrides", [])
            self._ros_node = rclpy.create_node(name, parameter_overrides=params)

            logger.info(f"节点 {name} 初始化成功")

            # 设置参数，检查是否已经声明
            try:
                # 尝试获取参数，如果已存在则不再声明
                self._ros_node.get_parameter("use_sim_time")
                logger.debug("参数 use_sim_time 已存在，无需重复声明")
            except:
                # 参数不存在，进行声明
                self._ros_node.declare_parameter("use_sim_time", True)
                logger.debug("已声明参数 use_sim_time")

            # 创建并启动自动回调处理线程
            try:
                self._executor = rclpy.executors.MultiThreadedExecutor()
                self._executor.add_node(self._ros_node)
                self._thread_running = True
                self._spin_thread = threading.Thread(
                    target=self._spin_function, daemon=True
                )
                self._spin_thread.start()
                logger.info(f"节点 {name} 启动后台处理线程成功")
            except Exception as e:
                logger.error(f"启动后台处理线程失败: {str(e)}")
                raise RuntimeError(f"启动后台处理线程失败: {str(e)}")

        except Exception as e:
            logger.error(f"节点 {name} 初始化失败: {str(e)}")
            self.destroy()
            raise RuntimeError(f"节点初始化失败: {str(e)}")

    def _spin_function(self):
        """后台线程函数，处理ROS2回调"""
        try:
            while self._thread_running and rclpy.ok():
                self._executor.spin_once(timeout_sec=0.1)
        except Exception as e:
            logger.error(f"后台处理线程异常退出: {str(e)}")

    def destroy(self) -> None:
        """销毁 ROS2 节点，清理资源。"""
        if self._is_destroyed:
            return

        # 清理资源前记录日志
        try:
            node_name = self._ros_node.get_name() if self._ros_node else self._node_name
        except Exception:
            # 如果get_name()失败，使用保存的节点名称
            node_name = self._node_name

        logger.info(f"正在销毁节点 {node_name}")

        # 停止后台线程
        if self._spin_thread is not None and self._thread_running:
            self._thread_running = False
            try:
                # 等待线程结束，但设置超时避免无限等待
                self._spin_thread.join(timeout=1.0)
                logger.info("后台处理线程已停止")
            except Exception as e:
                logger.error(f"停止后台处理线程时出错: {str(e)}")

        # 销毁节点
        if self._ros_node:
            self._ros_node.destroy_node()

        import rclpy.utilities

        rclpy.utilities.try_shutdown()
        self._is_destroyed = True

    def create_reader(
        self,
        topic: str,
        callback: Callable[[Any], None],
        *,
        msg_type: Type,
        pb_type: Optional[Any] = None,
        qos_profile: Optional[QoSProfile] = None,
    ) -> RosReader:
        """
        创建 ROS2 读取器。

        参数:
            topic (str): 要订阅的话题
            callback (Callable[[Any], None]): 回调函数
            msg_type (Type): 消息类型
            pb_type (Optional[Any]): protobuf 消息类型
            qos_profile (Optional[QoSProfile]): QoS配置，如果为None则使用默认配置

        返回:
            RosReader: ROS2 读取器实例
        """

        def pb_callback(raw_msg: Any, pb_type: Optional[Any]) -> None:
            try:
                data = raw_msg.data
                obs = pb_type()
                obs.ParseFromString(data)
                callback(obs)
            except Exception as e:
                logger.error(f"消息解析失败: {str(e)}")

        # 使用默认QoS配置如果未提供
        if qos_profile is None:
            qos_profile = self.DEFAULT_QOS

        if pb_type:
            final_callback = lambda raw_msg: pb_callback(raw_msg, pb_type)
        else:
            final_callback = callback

        subscriber = self._ros_node.create_subscription(
            msg_type, topic, final_callback, qos_profile=qos_profile
        )

        reader = RosReader(topic, subscriber, msg_type)
        self._reader_list.append(reader)
        logger.debug(f"创建读取器: {topic}, 类型: {msg_type.__name__}")
        return reader

    def create_writer(
        self, topic: str, msg_type: Any, qos_profile: Optional[QoSProfile] = None
    ) -> RosWriter:
        """
        创建 ROS2 写入器。

        参数:
            topic (str): 要发布的话题
            msg_type (Any): 消息类型
            qos_profile (Optional[QoSProfile]): QoS配置，如果为None则使用默认配置

        返回:
            RosWriter: ROS2 写入器实例
        """
        # 使用默认QoS配置如果未提供
        if qos_profile is None:
            qos_profile = self.DEFAULT_QOS

        publisher = self._ros_node.create_publisher(
            msg_type, topic, qos_profile=qos_profile
        )

        writer = RosWriter(topic, publisher, msg_type)
        self._writer_list.append(writer)
        return writer

    def create_time_writer(
        self, topic: str, qos_profile: Optional[QoSProfile] = None
    ) -> RosTimeWriter:
        """
        创建 ROS2 时间写入器。

        参数:
            topic (str): 要发布的话题
            qos_profile (Optional[QoSProfile]): QoS配置，如果为None则使用默认配置

        返回:
            RosTimeWriter: ROS2 时间写入器实例
        """
        # 使用默认QoS配置如果未提供
        if qos_profile is None:
            qos_profile = self.DEFAULT_CLOCK_QOS

        publisher = self._ros_node.create_publisher(
            Clock, topic, qos_profile=qos_profile
        )
        return RosTimeWriter(publisher)

    def create_timer(self, period: float, callback: Callable) -> Timer:
        """
        创建 ROS2 定时器。

        参数:
            period (float): 定时器周期（秒）
            callback (Callable): 回调函数

        返回:
            Timer: ROS2 定时器实例
        """
        timer = self._ros_node.create_timer(period, callback)
        self._timer_list.append(timer)
        return timer

    def create_service(
        self, name: str, srv_type: Any, handler: Callable, **kwargs
    ) -> RosService:
        """
        创建 ROS2 服务。

        参数:
            name (str): 服务名称
            srv_type (Any): 服务类型
            handler (Callable): 服务处理函数
            **kwargs: 额外参数，如qos_profile等

        返回:
            RosService: ROS2 服务实例
        """
        try:
            # 处理来自参数服务的特殊情况
            if "qos_profile" in kwargs and kwargs.get("callback_group", None) is None:
                # 这可能是来自ROS2参数服务的调用
                # 移除qos_profile参数，因为Node.create_service不接受这个参数
                qos_profile = kwargs.pop("qos_profile", None)

            # 调用Node类的create_service方法
            service = self._ros_node.create_service(srv_type, name, handler, **kwargs)
            ros_service = RosService(name, service, srv_type)
            self._service_list.append(ros_service)
            return ros_service
        except Exception as e:
            logger.error(f"创建服务 {name} 失败: {str(e)}")
            # 如果是参数服务初始化导致的错误，返回一个空的RosService对象
            if "parameter" in str(e).lower():
                return RosService(name, None, srv_type)
            raise

    def create_client(self, name: str, srv_type: Any) -> RosClient:
        """
        创建 ROS2 客户端。

        参数:
            name (str): 服务名称
            srv_type (Any): 服务类型

        返回:
            RosClient: ROS2 客户端实例
        """
        client = self._ros_node.create_client(srv_type, name)
        ros_client = RosClient(name, client, srv_type)
        self._client_list.append(ros_client)
        return ros_client

    def get_time(self) -> float:
        """
        获取当前 ROS2 时间。

        返回:
            float: 当前时间（秒）
        """
        now = self._ros_node.get_clock().now()
        return now.nanoseconds / 1e9

    def spin(self) -> None:
        """
        运行节点，处理回调。

        此方法会阻塞直到节点被销毁。
        """
        try:
            rclpy.spin(self._ros_node)
        except KeyboardInterrupt:
            logger.info("接收到键盘中断，正在关闭节点...")
        finally:
            self.destroy()

    def spin_once(self, timeout_sec: float = 1.0) -> None:
        """
        运行一次节点回调。

        参数:
            timeout_sec (float): 超时时间（秒）
        """
        # 如果后台线程正在运行，这个方法可以不做任何事
        # 但为了兼容性，保留此方法
        if self._ros_node and not self._thread_running:
            rclpy.spin_once(self._ros_node, timeout_sec=timeout_sec)

    def ok(self) -> bool:
        """检查节点是否正常运行"""
        return not self._is_destroyed and rclpy.ok()


# 如果此文件作为主程序运行，则执行测试代码
if __name__ == "__main__":
    # 添加项目根目录到Python路径
    sys.path.insert(
        0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../../../"))
    )

    import sys
    from std_msgs.msg import String

    try:
        # 创建测试节点
        print("正在创建ROS2测试节点...")
        node = RosNode("ros2_node_test")

        # 创建消息回调函数
        def message_callback(msg):
            print(f"收到消息: {msg.data}")

        # 使用类来管理状态，避免nonlocal问题
        class CounterManager:
            def __init__(self):
                self.count = 0

        counter = CounterManager()

        # 创建定时器回调函数
        def timer_callback():
            counter.count += 1
            msg = String()
            msg.data = f"测试消息 #{counter.count}"
            writer.write(msg)
            print(f"发布消息: {msg.data}")

            # 测试10次后退出
            if counter.count >= 10:
                print("测试完成，正在退出...")
                node.destroy()
                sys.exit(0)

        # 创建读取器和写入器
        print("创建读取器和写入器...")
        reader = node.create_reader("test_topic", message_callback, msg_type=String)
        writer = node.create_writer("test_topic", String)

        # 创建定时器，每秒发布一次消息
        print("创建定时器...")
        timer = node.create_timer(1.0, timer_callback)

        print("节点已启动，按Ctrl+C退出...")

        # 由于节点已在后台线程中运行，这里只需等待
        while node.ok():
            time.sleep(0.1)

    except KeyboardInterrupt:
        print("接收到键盘中断，正在退出...")
    except Exception as e:
        print(f"发生错误: {str(e)}")
        import traceback

        traceback.print_exc()
    finally:
        # 确保节点被正确销毁
        if "node" in locals() and node is not None:
            print("正在销毁节点...")
            node.destroy()
