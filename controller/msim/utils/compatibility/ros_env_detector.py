#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
"""ROS 环境检测器模块

提供可靠的 ROS1 和 ROS2 环境检测功能。
"""

import os
import sys
import importlib
import subprocess
import traceback
from typing import Tuple, Optional, Type

from utils.loggers import LoggerProxy

logger = LoggerProxy("player")


def is_ros1_available() -> bool:
    """检测 ROS1 环境是否可用

    检测方法：
    1. 检查环境变量 ROS_DISTRO 是否存在且不是 ROS2 发行版
    2. 尝试导入 rospy 模块
    3. 检查 rosversion 命令是否可用

    返回:
        bool: ROS1 环境是否可用
    """
    # 检查环境变量
    ros_distro = os.environ.get("ROS_DISTRO", "")
    if ros_distro and ros_distro not in [
        "foxy",
        "galactic",
        "humble",
        "iron",
        "jazzy",
        "rolling",
    ]:
        # 可能是 ROS1 发行版
        logger.debug(f"检测到可能的 ROS1 发行版: {ros_distro}")
    else:
        logger.debug("环境变量中未检测到 ROS1 发行版")

    # 尝试导入 rospy
    try:
        importlib.import_module("rospy")
        logger.debug("成功导入 rospy 模块")
        return True
    except ImportError:
        logger.debug("无法导入 rospy 模块")

    # 检查 rosversion 命令
    try:
        result = subprocess.run(
            ["rosversion", "-d"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            timeout=1,
            text=True,
        )
        if result.returncode == 0:
            logger.debug(f"检测到 ROS1 版本: {result.stdout.strip()}")
            return True
    except (subprocess.SubprocessError, FileNotFoundError):
        logger.debug("rosversion 命令不可用")

    return False


def is_ros2_available() -> bool:
    """检测 ROS2 环境是否可用

    检测方法：
    1. 检查环境变量 ROS_DISTRO 是否存在且是 ROS2 发行版
    2. 尝试导入 rclpy 模块
    3. 检查 ros2 命令是否可用

    返回:
        bool: ROS2 环境是否可用
    """
    # 检查环境变量
    ros_distro = os.environ.get("ROS_DISTRO", "")
    if ros_distro in ["foxy", "galactic", "humble", "iron", "jazzy", "rolling"]:
        # 是 ROS2 发行版
        logger.debug(f"检测到 ROS2 发行版: {ros_distro}")
    else:
        logger.debug("环境变量中未检测到 ROS2 发行版")

    # 尝试导入 rclpy
    try:
        importlib.import_module("rclpy")
        logger.debug("成功导入 rclpy 模块")
        return True
    except ImportError:
        logger.debug("无法导入 rclpy 模块")

    # 检查 ros2 命令
    try:
        result = subprocess.run(
            ["ros2", "--help"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            timeout=1,
        )
        if result.returncode == 0:
            logger.debug("ros2 命令可用")
            return True
    except (subprocess.SubprocessError, FileNotFoundError):
        logger.debug("ros2 命令不可用")

    return False


def get_ros_version() -> str:
    """获取当前环境中的 ROS 版本

    返回:
        str: ROS 版本，可能的值为 'ros1', 'ros2', 'both', 'none'
    """
    ros1_available = is_ros1_available()
    ros2_available = is_ros2_available()

    if ros1_available and ros2_available:
        return "both"
    elif ros1_available:
        return "ros1"
    elif ros2_available:
        return "ros2"
    else:
        return "none"


def detect_bag_file_type(file_path: str) -> str:
    """检测 bag 文件类型

    参数:
        file_path: bag 文件路径

    返回:
        str: bag 文件类型，可能的值为 'ros1', 'ros2', 'unknown'
    """
    if not os.path.exists(file_path):
        logger.warning(f"文件不存在: {file_path}")
        return "unknown"

    # 根据文件扩展名或目录结构判断
    if file_path.endswith(".bag"):
        logger.debug(f"检测到 ROS1 bag 文件: {file_path}")
        return "ros1"
    elif (
        os.path.isdir(file_path)
        or file_path.endswith(".db3")
        or file_path.endswith(".mcap")
    ):
        logger.debug(f"检测到 ROS2 bag 文件: {file_path}")
        return "ros2"

    # 如果无法通过扩展名判断，尝试其他方法
    try:
        # 尝试读取文件头部判断类型
        with open(file_path, "rb") as f:
            header = f.read(100)  # 读取前 100 个字节

            # MCAP 文件头部特征
            if header.startswith(b"\x89MCAP\x30\r\n"):
                logger.debug(f"通过文件头部检测到 MCAP 文件: {file_path}")
                return "ros2"

            # SQLite3 文件头部特征
            if header.startswith(b"SQLite format 3\x00"):
                logger.debug(f"通过文件头部检测到 SQLite3 文件: {file_path}")
                return "ros2"
    except Exception as e:
        logger.debug(f"读取文件头部时出错: {e}")

    logger.warning(f"无法确定文件类型: {file_path}")
    return "unknown"


def get_message_class_ros2(type_str: str) -> Optional[Type]:
    """
    根据类型字符串获取ROS2消息类。

    参数:
        type_str: 消息类型字符串，格式为 "package_name/MsgName"

    返回:
        消息类，如果找不到则返回 None
    """
    try:
        pkg_name, msg_name = type_str.split("/")
        module_name = f"{pkg_name}.msg"
        try:
            # 尝试直接使用pkg_name和msg_name引入
            module = importlib.import_module(pkg_name)
            return getattr(module, msg_name)
        except Exception as e:
            logger.error(f"导入模块 {type_str} 失败: {e}")
            logger.error(traceback.format_exc())

        try:
            module = importlib.import_module(module_name)
            return getattr(module, msg_name)
        except (ImportError, AttributeError):
            # 尝试使用ROS2工具获取消息类
            from rosidl_runtime_py.utilities import get_message

            return get_message(type_str)

    except Exception as e:
        logger.error(f"获取消息类 {type_str} 失败: {e}")
        logger.error(traceback.format_exc())
        return None
