#  Copyright (C) Mogo.ai Information and Technology Co.Ltd 
#
#  All rights reserved.
#
#  This document contains proprietary information belonging to Mogo.ai Co.Ltd.
#  Passing on and copying of this document, and communication of its contents
#  is not permitted without prior written authorization.
#
#
#  <AUTHOR>
#  @date 9/26/23, 2:11 PM
import threading
import time
from typing import Type, Optional, Any, Dict

import rclpy
from google.protobuf.message import Message

from cyber.python.cyber_py3 import cyber
from cyber.python.cyber_py3 import cyber_timer
from cyber.proto.clock_pb2 import Clock as CyberClock
from rosgraph_msgs.msg import Clock as Ros2Clock

from utils.compatibility.node_factory import NodeBase, WriterBase, ReaderBase, TimerBase, ServiceBase, ClientBase, TimeWriter


class CyberWriter(WriterBase):
    def __init__(
            self,
            topic: str,
            writer_ins,
            msg_type: Any,
    ):
        self._topic = topic
        self._writer_ins = writer_ins
        self._type = msg_type

    @property
    def topic(self):
        return self._topic

    @property
    def type(self) -> Any:
        return self._type

    def write(self, data: Any):
        self._writer_ins.write(data)


class CyberReader(ReaderBase):
    def __init__(self, topic: str, reader_ins, msg_type: Optional[Message] = None):
        self._topic = topic
        self._reader_ins = reader_ins
        self._type = msg_type

    @property
    def topic(self):
        return self._topic

    @property
    def type(self) -> Any:
        return self._type


class CyberTimer(TimerBase):
    def __init__(self, timer_period_sec: float, timer_ins):
        self._timer_period_sec = timer_period_sec
        self._timer_ins = timer_ins

    @property
    def timer_period_sec(self):
        return self.timer_period_sec


class CyberClient(ClientBase):
    def __init__(self, name: str, raw_client, msg_type):
        self._name = name
        self._client = raw_client
        self._msg_type = msg_type

    @property
    def name(self) -> str:
        return self._name

    def send(self, req: Any, timeout=None) -> Message:
        return self._client.send_request(req)


class CyberService(ServiceBase):

    def __init__(self, name: str, service_ins):
        self._name = name
        self._srv_ins = service_ins

    @property
    def name(self) -> str:
        return self._name


class CyberTimeWriter(TimeWriter):
    def __init__(self, cyber_ins, ros_ins=None):
        self._cyber_ins = cyber_ins
        self._ros_ins = ros_ins
        self._ros_clock = Ros2Clock()
        self._cyber_clock = CyberClock()

    def write(self, sec: int, nanosec: int):
        self._cyber_clock.clock = int(sec * 10 ** 9) + nanosec
        self._ros_clock.clock.sec = sec
        self._ros_clock.clock.nanosec = nanosec
        self._cyber_ins.write(self._cyber_clock)
        self._ros_ins.publish(self._ros_clock)


class CyberNode(NodeBase):

    def __init__(self, name: str = '', remap: Dict[str, str] = None, **kwargs):
        super().__init__(name)

        if not cyber.ok():
            cyber.init()
        self.__cyber_node = cyber.Node(self.name)
        self.__spin_thread = None

        self._remap = remap or {}

        self._reader_list = []
        self._writer_list = []
        self._service_list = []
        self._client_list = []
        self._timer_list = []

        # TODO: need remove
        if not rclpy.ok():
            rclpy.init()
        self.__ros_node = rclpy.create_node(f"sim_time_manager_{self._rand_id}")

        self._time_writer: Optional[CyberTimeWriter] = None

        self._time_reader = self.__cyber_node.create_reader('/clock', CyberClock, self.update_time)
        self._time = 0.0
        self._time_callbacks = []

        self.__destroy = False

    def update_time(self, clock: CyberClock):
        self._time = clock.clock / 1e9
        for callback in self._time_callbacks:
            try:
                callback()
            except Exception as e:
                self.logerr(f"callback error: {e}")

    def ok(self) -> bool:
        return cyber.ok()

    def start(self):
        pass

    def create_writer(self, topic: str, msg_type: Any, qos_profile=1, **kwargs) -> CyberWriter:
        """
        Create a new publisher for a protobuf message type.(keep same with c++ interface)
        please mark type hints for callback function(or will raise error)
        example:
            def callback(msg: PBMsgType):
                pass
        """
        if topic in self._remap:
            topic = self._remap[topic]

        writer = self.__cyber_node.create_writer(topic, msg_type, self._get_default_qos(qos_profile))
        cyber_writer = CyberWriter(topic, writer, msg_type)
        self._writer_list.append(cyber_writer)
        return cyber_writer

    def create_time_writer(self, topic: str, *args, **kwargs) -> TimeWriter:
        if not self._time_writer:
            cyb_writer = self.create_writer(topic, CyberClock)
            ros_pub = self.__ros_node.create_publisher(Ros2Clock, topic, 5)
            self._time_writer = CyberTimeWriter(cyb_writer, ros_pub)
        return self._time_writer

    def create_reader(
            self,
            topic,
            callback,
            *,
            msg_type: Optional[Type[Message]] = None,
            raw: bool = False,
            **kwargs
    ) -> CyberReader:
        """
        Create a new publisher for a protobuf message type.(keep same with c++ interface)
        please mark type hints for callback function(or will raise error)
        example:
            def callback(msg: PBMsgType):
                pass
        """
        if not raw:
            data_type = msg_type or self.check_proto_args(callback)
        else:
            data_type = "RawData"
        reader = self.__cyber_node.create_reader(topic, data_type, callback)
        cyber_reader = CyberReader(topic, reader, data_type)
        self._reader_list.append(cyber_reader)
        return cyber_reader

    def create_timer(self, timer_period_sec: float, callback, one_shot: int = 1, use_clock: bool = False, **kwargs) -> Optional[CyberTimer]:
        if use_clock:
            self._time_callbacks.append(callback)
            return None
        timer = cyber_timer.Timer(timer_period_sec * 1000, callback, one_shot)
        _timer = CyberTimer(timer_period_sec, timer)
        self._timer_list.append(_timer)
        return _timer

    def create_client(
            self,
            srv_name: str,
            req_type: Any,
            rep_type: Any,
            timeout_sec: float = 180.0,
            **kwargs) -> CyberClient:
        start_time = time.time()
        while time.time() - start_time < timeout_sec:
            time.sleep(0.1)
            services = cyber.ServiceUtils.get_services(3)
            if srv_name in services:
                break
        else:
            raise RuntimeError("Timeout of {} sec while waiting for service".format(timeout_sec))
        client_ins = self.__cyber_node.create_client(srv_name, req_type, rep_type)
        cyber_client = CyberClient(srv_name, client_ins, req_type)
        self._client_list.append(cyber_client)
        return cyber_client

    def create_service(self, srv_name: str, callback, req_type: Any, rep_type: Any, **kwargs) -> CyberService:
        service = self.__cyber_node.create_service(srv_name, req_type, rep_type, callback)
        cyber_service = CyberService(srv_name, service)
        self._service_list.append(cyber_service)
        return cyber_service

    def get_time(self) -> float:
        return self._time

    @staticmethod
    def _get_default_qos(input_qos):
        if input_qos:
            qos = input_qos \
                if isinstance(input_qos, int) else input_qos.depth
        else:
            qos = 1
        return qos

    def destroy(self):
        if self.__destroy:
            return
        # TODO: remove
        try:
            self.loginfo("Ask ros2 to shutdown.")
            rclpy.shutdown()
            self.loginfo("Ros2 shutdown.")
            if self.__ros_node:
                self.__ros_node.destroy_node()
                self.loginfo("Ros2 node destroy.")
        except Exception:
            pass

        try:
            if self.__cyber_node:
                del self.__cyber_node
            self.loginfo("Ask cyber to shutdown.")
            cyber.shutdown()
            self.loginfo("Cyber shutdown.")
            if self.__spin_thread:
                self.loginfo("Wait spin thread join.")
                self.__spin_thread.join()
            self.loginfo("Cyber node has been destroyed.")
        except Exception:
            pass
