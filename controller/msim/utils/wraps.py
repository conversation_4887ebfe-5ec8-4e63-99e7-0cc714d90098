import time
from functools import wraps
from threading import Lock

from utils.loggers import get_server_logger
logger = get_server_logger()

FUNC_LOCK_MAP = {}


def singleton(cls):
    instance = {}

    def _singleton_wrapper(*args, **kwargs):
        if cls not in instance:
            instance[cls] = cls(*args, **kwargs)
        return instance[cls]

    return _singleton_wrapper


def synchronized(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        if func.__name__ in FUNC_LOCK_MAP:
            lock = FUNC_LOCK_MAP.get(func.__name__)
        else:
            lock = Lock()
            FUNC_LOCK_MAP[func.__name__] = lock
        with lock:
            return func(*args, **kwargs)
    return wrapper


def execute_once(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        if not hasattr(wrapper, 'has_executed'):
            wrapper.has_executed = False
        if not wrapper.has_executed:
            wrapper.has_executed = True
            return func(*args, **kwargs)
        else:
            logger.warn(f"Function {func.__name__} has already executed.")
    wrapper.has_executed = False
    return wrapper


def retry(tries=3, delay=1, backoff=1, exceptions=(Exception,), spec_exceptions=()):
    """
    Decorator for retrying a function if it raises an exception.

    :param tries: Number of times to try (first try + retries).
    :param delay: Initial delay between retries.
    :param backoff: Multiplier applied to delay between retries.
    :param exceptions: A tuple of exceptions to catch.
    :param spec_exceptions: A tuple of exceptions which need to raise not retry
    """

    def decorator_retry(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            m_tries, m_delay = tries, delay
            while m_tries > 1:
                try:
                    return func(*args, **kwargs)
                except exceptions as ex:
                    if spec_exceptions and type(ex) in spec_exceptions:
                        raise ex
                    msg = f"{str(ex)}, Retrying in {m_delay} seconds..."
                    logger.warning(msg)
                    time.sleep(m_delay)
                    m_tries -= 1
                    m_delay *= backoff
            # Last attempt without catching exceptions
            return func(*args, **kwargs)

        return wrapper

    return decorator_retry
