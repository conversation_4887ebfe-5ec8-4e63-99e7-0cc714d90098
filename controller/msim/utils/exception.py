import os
from utils.file_utils import parse_ini_file, get_setup_value

from utils.path import ERROR_CODE_PATH


class UsimException(Exception):
    """
    usim exception class
    Example usage::
    code:
        try:
            raise UsimException(1, "arg1")
        except UsimException as e:
            print(e.message)
        try:
            raise UsimException(2, "arg1", "arg2")
        except UsimException as e:
            print(e.message)
    output:
    Error code 1: This is a sample error message with arguments: arg1
    Error code 2: Another sample error message with arguments: arg1 and arg2
    """
    def __init__(self, error_code: [int, str], *args):
        self.error_code = str(error_code)
        self.args = args
        self.message = self._get_error_message()

    def _get_error_message(self) -> str:
        error_template = self._get_error_template()
        if error_template:
            try:
                return error_template.format(*self.args)
            except IndexError:
                return f"Error: Invalid number of arguments for error code {self.error_code}"
        else:
            return f"Error: Unknown error code {self.error_code}"

    def _get_error_template(self):
        sys_lang = get_setup_value("system", "language")
        error_templates = parse_ini_file(os.path.join(ERROR_CODE_PATH, f'error_code_{sys_lang}.ini'))
        return error_templates.get("error_msg", str(self.error_code))

    def __str__(self):
        return self.message
