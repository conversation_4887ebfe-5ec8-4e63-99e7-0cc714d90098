from abc import abstractmethod
from typing import Dict

from api.executor_api import ExecutorAPI


class ExecuteStepBase(object):

    def __init__(self, name, api: ExecutorAPI = None, *args, **kwargs):
        """
        执行工步的基类，用户不应重写该方法，如果需要初始化，请重写init函数
        :param name: 工步名称
        :param api: API接口 获取全局静态参数（可获取地图，车辆信息，源文件等）
                    一些动态信息及设置（例如产物、事件回调注册等）用于后续方便开发
        """
        self.name = name
        self.api = api
        self.kwargs = kwargs

    def param_check(self) -> Dict[str, str]:
        """
        参数检查, 校验下全局参数和自定义的参数是否符合要求(直接返回错误信息)
        校验返回若为空字典，则表示校验通过，否则表示校验失败，不会执行destroy函数
        :return: 错误信息 {错误的参数名: 错误信息}
        """
        return {}

    def init(self):
        """
        初始化函数
        和__init__分阶段实现，主要是对用户暴露的接口，用户不允许修改__init__函数
        """
        pass

    @abstractmethod
    def execute(self):
        """
        执行函数，需要子类实现
        """
        raise NotImplementedError

    def stop(self):
        """
        停止函数，需要子类实现 执行工步的停止函数后， 60s如果execute没有结束，会被强制执行destroy函数并直接退出
        """
        pass

    def destroy(self):
        """
        后处理函数,需可重入，发生异常时也会调用
        """
        pass

