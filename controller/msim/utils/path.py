import os
import json
from pathlib import Path
SOURCE_PATH = Path(__file__).parent.parent
CONFIG_PATH = Path(__file__).parent.parent / 'config'
SETUP_PATH = os.path.join(CONFIG_PATH, 'setup.ini')
ERROR_CODE_PATH = os.path.join(CONFIG_PATH, 'error_code')

def get_workspace_path():
    """
    Get workspace path
    """
    from utils.file_utils import get_setup_value
    return get_setup_value("system", "workspace_path")

def get_mount_path():
    """
    Get mount path
    """
    return os.path.join(get_workspace_path(), "mount")

def get_tasks_info():
    """
    Get tasks_info.json
    """
    return os.path.join(get_workspace_path(), "tasks_info.json")

def get_tasks_log_path():
    """
    Get tasks log directory
    """
    return os.path.join(get_workspace_path(), "logs")

def get_task_workspace_path(task_id):
    """
    Get task workspace path
    """
    return os.path.join(get_workspace_path(), task_id)

def get_task_casefile_path(task_id):
    """
    Get task casefile path
    """
    return os.path.join(get_task_workspace_path(task_id), "casefile")

def get_task_map_path(task_id):
    """
    Get task map path
    """
    return os.path.join(get_task_workspace_path(task_id), "map")

def get_task_vehicle_path(task_id):
    """
    Get task vehicle path
    """
    return os.path.join(get_task_workspace_path(task_id), "vehicle")

def get_task_log_path(task_id):
    """
    Get task log path
    """
    return os.path.join(get_task_workspace_path(task_id), "logs")

def get_task_result_path(task_id):
    """
    Get task result path
    """
    return os.path.join(get_task_workspace_path(task_id), "result")

def get_task_record_path(task_id):
    """
    Get task record path
    """
    return os.path.join(get_task_workspace_path(task_id), "record")

def is_manual_mode() -> bool:
    """
    检测当前是否为手动模式
    
    返回:
        bool: True表示手动模式，False表示容器模式
    """
    try:
        tasks_info_path = get_tasks_info()
        if not os.path.exists(tasks_info_path):
            return False
            
        with open(tasks_info_path, "r", encoding="utf-8") as f:
            tasks_data = json.load(f)
        
        local_value = tasks_data.get("manual", "false")
        
        # 处理字符串形式的布尔值
        if isinstance(local_value, str):
            return local_value.lower() == "true"
        elif isinstance(local_value, bool):
            return local_value
        else:
            return False
            
    except Exception:
        # 异常情况下默认为容器模式
        return False

def is_local_mode() -> bool:
    """
    检测当前是否为本地模式
    
    返回:
        bool: True表示本地模式
    """
    try:
        tasks_info_path = get_tasks_info()
        if not os.path.exists(tasks_info_path):
            return False
            
        with open(tasks_info_path, "r", encoding="utf-8") as f:
            tasks_data = json.load(f)
        
        local_value = tasks_data.get("local", "false")
        
        # 处理字符串形式的布尔值
        if isinstance(local_value, str):
            return local_value.lower() == "true"
        elif isinstance(local_value, bool):
            return local_value
        else:
            return False
            
    except Exception:
        # 异常情况下默认为容器模式
        return False


def get_disk_space(directory):
    stat = os.statvfs(directory)
    total_space = stat.f_frsize * stat.f_blocks
    free_space = stat.f_frsize * stat.f_bfree
    used_space = total_space - free_space

    # unit(MB）
    total_space_MB = total_space / (1024 * 1024)
    free_space_MB = free_space / (1024 * 1024)
    used_space_MB = used_space / (1024 * 1024)

    return {
        "total_space": total_space_MB,
        "free_space": free_space_MB,
        "used_space": used_space_MB
    }
