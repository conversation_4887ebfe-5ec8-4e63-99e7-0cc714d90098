import os
import time
from contextlib import contextmanager
from pathlib import Path


@contextmanager
def file_write_lock(file_path: str, retry: bool = False):
    lock_file_path = f"{file_path}.lock"
    created = False

    while True:
        try:
            fd = os.open(lock_file_path, os.O_CREAT | os.O_EXCL | os.O_RDWR)
            with os.fdopen(fd, 'w') as lock_file:
                created = True
                yield lock_file
                os.remove(lock_file_path)
                break
        except FileExistsError:
            if retry:
                time.sleep(1)
            else:
                yield None
                return
        finally:
            if created and os.path.exists(lock_file_path):
                os.remove(lock_file_path)


def write_once(file_path: str, content: str):
    if os.path.exists(file_path):
        return

    with file_write_lock(file_path, False) as lock_file:
        if lock_file is None:
            return

        with open(file_path, 'a') as f:
            f.write(content + '\n')


def safe_write(file_path: str, content: str):
    with file_write_lock(file_path, True) as lock_file:
        if lock_file is None:
            return

        with open(file_path, 'a') as f:
            f.write(content + '\n')
