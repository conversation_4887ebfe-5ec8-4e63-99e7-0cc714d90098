from typing import Optional, Union, Tuple

import py_trees

from api.actor.actor_base import Actor
from api.executor_api import ExecutorAPI
from utils.loggers import get_server_logger
logger = get_server_logger()


class InnerTree(py_trees.behaviour.Behaviour):
    def __init__(self, criterion: 'Criterion'):
        self.__criterion = criterion
        super(InnerTree, self).__init__(self.__criterion.name)

    @property
    def criterion(self):
        return self.__criterion

    def initialise(self):
        self.__criterion.initialise()

    def update(self):
        return self.__criterion.update()


class Criterion:

    """
    评价基类实现

    重要参数定义
    输入：
    - name: 评价名称（不做去重校验）
    - actor: 可以绑定具体的carla actor进行操作（对于了解carlaAPi的人可以使用）
    - optional: True：不影响最终评价，只作为参考值。False：影响最终评价
    - terminate on failure: 中断仿真，True:在update的过程中如果发现评价为failure，会直接中断仿真
    - ros_node: ros节点，可用于订阅消息等
    - api: 用于获取算法产物等
    输出：
    - test_status: 评价状态 "INIT", "RUNNING", "SUCCESS", "ACCEPTABLE" "INVALID" or "FAILURE"
    - success_value: 成功的期望值
    - acceptable_value: 结果并不意味着失败，但还不足以成功
    - actual_value: 实际值
    - units: 评价的单位，用于展示
    - details: 数据详情的展示TODO待与平台确认展示格式
    - events: 事件，用于记录发生了什么
    """

    def __init__(self,
                 name: str,
                 actor: Optional[Actor] = None,
                 node=None,
                 optional: bool = False,
                 api: Optional[ExecutorAPI] = None,
                 terminate_on_failure: bool = False,
                 **kwargs):
        """
        建议自定义时别改造__init__（），不容易定位问题
        """
        self.name = name
        self.actor = actor
        self.optional = optional
        self._terminate_on_failure = terminate_on_failure
        self.node = node
        self.api = api

        # Attributes to compare the current state (actual_value), with the expected ones
        self.test_status: str = "INIT"  # Either "INIT", "RUNNING", "SUCCESS", "INVALID", "ACCEPTABLE" or "FAILURE"
        self.success_value: Union[float, int] = 0
        self.acceptable_value: Union[float, int] = 0
        self.actual_value: Union[float, int] = 0
        self.units = "times"

        # will display by json to platform
        self.details = []
        self.events = []  # List of events (i.e collision, sidewalk invasion...)

        self.__tree = InnerTree(self)
        self.status = self.__tree.status
        self.logger = logger

    @property
    def tree(self):
        return self.__tree

    def params_check(self) -> Tuple[bool, str]:
        """
        检查参数是否合法
        先于initialise调用
        """
        return True, ""

    def initialise(self):
        """
        用于初始化操作，可创建一些订阅等
        """
        self.logger.debug("%s.initialise()" % self.__class__.__name__)

    def update(self):
        """
        用于更新中间结果， 会循环调用，调用频率会根据所有评价的update计算复杂度决定
        """
        new_status = py_trees.common.Status.RUNNING
        return new_status

    def terminate(self, new_status):
        """
        结束时的回调，需把评价结果赋值，离线的计算可以放在这里（考虑多进程加速）
        """
        if self.test_status in ('RUNNING', 'INIT'):
            self.test_status = "SUCCESS"

        self.logger.debug("%s.terminate()[%s->%s]" % (self.__class__.__name__, self.status, new_status))
