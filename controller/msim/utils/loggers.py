import os
import logging
import threading
import config.myglobal as myglobal

TASK_LOG_DIR = "/home/<USER>/workspace/{task_id}/logs/{client_type}"
TASKS_LOG_PATH = "/home/<USER>/workspace/logs"

logger_map = {}
logger_map_lock = threading.Lock()

def get_task_logger(file_name=None):
    """
    获取任务日志记录器（全局唯一，按task_id+client_type+file_name隔离）
    """
    task_id = myglobal.PROCESSING_CASE_ID
    client_type = os.environ.get("CLIENT_TYPE", "unknown")
    if not task_id:
        raise ValueError("task_id is not set")

    logger_key = f"{task_id}_{client_type}_{file_name or 'default'}"

    with logger_map_lock:
        if logger_key in logger_map:
            return logger_map[logger_key]

        # 日志目录
        log_dir = TASK_LOG_DIR.format(task_id=task_id, client_type=client_type)
        os.makedirs(log_dir, exist_ok=True)
        log_file = f"{file_name or client_type}.log"
        log_path = os.path.join(log_dir, log_file)

        # 创建logger
        logger = logging.getLogger(logger_key)
        logger.setLevel(logging.INFO)
        if not getattr(logger, "_inited", False):
            fmt = ('[%(levelname)s][%(asctime)s][pid-%(process)d][%(name)s-'
                   '%(threadName)s][%(filename)s-%(funcName)s-%(lineno)d]: %(message)s')
            file_handler = logging.FileHandler(log_path, mode='a')
            file_handler.setLevel(logging.INFO)
            file_handler.setFormatter(logging.Formatter(fmt))
            logger.addHandler(file_handler)
            logger._inited = True
        logger_map[logger_key] = logger
        return logger

def clean_task_loggers(task_id):
    """
    清理指定task_id下所有logger和handler
    """
    with logger_map_lock:
        keys_to_remove = [k for k in logger_map if k.startswith(f"{task_id}_")]
        for k in keys_to_remove:
            logger = logger_map.pop(k)
            # 关闭所有handler
            if hasattr(logger, 'handlers'):
                for h in logger.handlers:
                    try:
                        h.close()
                    except Exception:
                        pass
                logger.handlers.clear()

# 服务器和客户端logger单例

def get_server_logger():
    _logger = logging.getLogger("server")
    if not getattr(_logger, "_inited", False):
        _logger.setLevel(logging.INFO)
        fmt = ('[%(levelname)s][%(asctime)s][pid-%(process)d][%(name)s-'
               '%(threadName)s][%(filename)s-%(funcName)s-%(lineno)d]: %(message)s')
        file_handler = logging.FileHandler(os.path.join(TASKS_LOG_PATH, 'server.log'), mode='a')
        file_handler.setLevel(logging.INFO)
        file_handler.setFormatter(logging.Formatter(fmt))
        _logger.addHandler(file_handler)
        _logger._inited = True
    return _logger

def get_client_logger():
    client_type = os.environ.get("CLIENT_TYPE", "unknown")
    _logger = logging.getLogger(f"client_{client_type}")
    if not getattr(_logger, "_inited", False):
        _logger.setLevel(logging.INFO)
        fmt = ('[%(levelname)s][%(asctime)s][pid-%(process)d][%(name)s-'
               '%(threadName)s][%(filename)s-%(funcName)s-%(lineno)d]: %(message)s')
        file_handler = logging.FileHandler(os.path.join(TASKS_LOG_PATH, f'client_{client_type}.log'), mode='a')
        file_handler.setLevel(logging.INFO)
        file_handler.setFormatter(logging.Formatter(fmt))
        _logger.addHandler(file_handler)
        _logger._inited = True
    return _logger

class LoggerProxy:
    """
    logger代理对象，支持 logger = LoggerProxy("aaa")，logger.info(...) 自动写入aaa.log
    """
    def __init__(self, file_name=None):
        self._file_name = file_name

    def __getattr__(self, name):
        return getattr(get_task_logger(self._file_name), name)

