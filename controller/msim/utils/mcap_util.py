import json
import os
import uuid
from typing import Dict, <PERSON>, <PERSON>, Tu<PERSON>, Optional

from mcap.well_known import SchemaEncoding

from utils.loggers import get_server_logger
logger = get_server_logger()
from utils.path import get_record_description_path, get_record_descriptions_path


# TODO: need move this  to grpc api not by file
def write_mcap_description_file(task_id: int, mcap_description: Dict[str, List[Any]]) -> None:
    """
    Write the mcap description to the file.
    """
    record_description_path = get_record_description_path(task_id)
    with open(record_description_path, 'w', encoding='utf-8') as fp1:
        json.dump(mcap_description, fp1, indent=4)


def check_mcap(mcap_path: str) -> bool:
    from utils.mcap_tools.reader import get_summary
    try:
        get_summary(mcap_path)
        return True
    except Exception as e:
        return False


def write_single_mcap_description_file(task_id: int, mcap_description: Dict[str, List[Any]]) -> None:
    random_uuid = uuid.uuid4()
    record_description_path = os.path.join(get_record_descriptions_path(task_id), str(random_uuid) + '.json')
    with open(record_description_path, 'w', encoding='utf-8') as fp1:
        json.dump(mcap_description, fp1, indent=4)


def merge_mcap_description_files(task_id: int) -> None:
    """
    Merge the mcap description files.
    """
    record_descriptions_path = get_record_descriptions_path(task_id)
    mcap_description = {}
    for dir_path, _, file_names in os.walk(record_descriptions_path):
        for file_name in file_names:
            file_path = os.path.join(dir_path, file_name)
            with open(file_path, 'r', encoding='utf-8') as fp:
                json_content = json.load(fp)

            for key, value in json_content.items():
                if key not in mcap_description:
                    mcap_description[key] = value
                else:
                    mcap_description[key].extend(value)
    write_mcap_description_file(task_id, mcap_description)


def record_pdc_takeover_time(task_id: int, mcap_file: str) -> None:
    """
    Record the pdc takeover time.
    """
    try:
        from mcap_ros2.reader import read_ros2_messages
        timestamps = {
            "timestamps": []
        }
        for msg in read_ros2_messages(mcap_file, ["/logsim/timestamp_signal"]):
            log_time = msg.log_time_ns
            timestamps["timestamps"].append(
                {
                    "type": "info",
                    "messages": "pdc takeover",
                    "timestamps": {
                        "sec": log_time // 10 ** 9,
                        "nanosec": log_time % 10 ** 9
                    }
                }
            )
        if timestamps["timestamps"]:
            write_single_mcap_description_file(task_id, timestamps)
    except Exception as e:
        logger.error(f"Record pdc takeover time failed: {e}")

    merge_mcap_description_files(task_id)


def recover_sim_time(source_mcap: str, target_mcap: str = "") -> Tuple[bool, str]:
    # void import error in different containers
    try:
        from utils.mcap_tools.reader import get_summary, read_mcap_messages
        from utils.mcap_tools.writer import Writer
        from utils.mcap_tools.decoder import RosDecoder
        from utils.mcap_tools.reader import parse_pb_from_schema
    except ImportError:
        return False, "Import error, please check the mcap_tools package."

    target_mcap = target_mcap or source_mcap.replace(".mcap", "_sim_time.mcap")
    summary = get_summary(source_mcap)
    # check topic and hz
    topics = {item.topic: item for item in summary.channels.values()}
    if "/clock" not in topics:
        return False, "No clock topic found in the mcap file."

    clock_msg_cnt = summary.statistics.channel_message_counts.get(topics["/clock"].id, 0)
    duration = (summary.statistics.message_end_time - summary.statistics.message_start_time) // 10 ** 9
    print(f"{source_mcap} duration: {duration}s, start: {summary.statistics.message_start_time}, "
          f"end: {summary.statistics.message_end_time}")
    # frequency default below 100hz
    if int(clock_msg_cnt / duration) > 100:
        return False, "Clock topic frequency is too high, please check the mcap file."

    writer = None
    ros_decoder = RosDecoder()
    clock_pb_class = None

    def clock_decoder(c_msg):
        if c_msg.schema.encoding == SchemaEncoding.Protobuf:
            nonlocal clock_pb_class
            if clock_pb_class is None:
                clock_pb_class = parse_pb_from_schema(c_msg.schema)
            if clock_pb_class:
                _clock = clock_pb_class()
                _clock.ParseFromString(c_msg.raw_msg)
                return _clock.clock

        else:
            _clock = ros_decoder.decode(c_msg.schema, msg.message)
            return _clock.clock.sec * (10 ** 9) + _clock.clock.nanosec


    try:
        writer = Writer(target_mcap)

        for channel in summary.channels.values():
            schema = summary.schemas[channel.schema_id]
            writer.register_channel(
                channel.topic, channel.message_encoding,
                writer.register_schema(schema.name, schema.encoding, schema.data), channel.metadata
            )
        last_time: Optional[Tuple[int, int]] = None
        msg_stack = []
        for msg in read_mcap_messages(source_mcap):

            if msg.topic == "/clock":
                clock = clock_decoder(msg)
                now_time = (clock, msg.log_time_ns)
                if last_time:
                    while msg_stack:
                        w_msg = msg_stack.pop()
                        last_sim_time, last_sys_time = last_time
                        now_sim_time, now_sys_time = now_time
                        log_time = int((w_msg.log_time_ns - last_sys_time) / (now_sys_time - last_sys_time) * (now_sim_time - last_sim_time)) + last_sim_time
                        writer.write_message(w_msg.topic, w_msg.raw_msg, log_time, raw=True)
                last_time = now_time

            if last_time:
                msg_stack.append(msg)
            else:
                continue

        writer.finish()
        writer = None
        return True, ""
    except Exception as e:
        import traceback
        traceback.print_exc()
        return False, str(e)
    finally:
        if writer:
            writer.finish()
