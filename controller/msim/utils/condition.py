#  Copyright (C) Mogo.ai Information and Technology Co.Ltd 
#
#  All rights reserved.
#
#  This document contains proprietary information belonging to Mogo.ai Co.Ltd.
#  Passing on and copying of this document, and communication of its contents
#  is not permitted without prior written authorization.
#
#
#  <AUTHOR>
#  @date 10/30/23, 1:51 PM
from typing import Dict

from data.task_data import TaskData
from utils.exception import UsimException


def eval_condition(task_data: TaskData, cond_exp: str) -> bool:
    try:
        if not cond_exp:
            return True
        res = LogicExpressionParser(cond_exp, task_data).parse()
    except Exception as e:
        raise UsimException(40003, cond_exp, str(e))
    return res


class LogicExpressionParser:
    def __init__(self, expression, task_data: TaskData):
        self.expression = expression
        self._conditions = self._generate(task_data)
        self.index = 0

    @staticmethod
    def _generate(task_data: TaskData) -> Dict:
        """
        generate the condition items
        for example:
        {
            "case_type": {
                "worldsim": True,
                "citysim": False
            }
        }
        """
        return {
            "case_type": {
                task_data.case.type: True
            },
            "run_type": {
                task_data.runtime.run_type: True
            },
            "project_type": {
                task_data.runtime.project_module: True
            }
        }

    def parse(self):
        return self.parse_expression()

    def parse_expression(self):
        left_operand = self.parse_operand()

        while self.index < len(self.expression):
            operator = self.expression[self.index:self.index+2]
            if operator == "&&":
                self.index += 2
                right_operand = self.parse_operand()
                left_operand = left_operand and right_operand
            elif operator == "||":
                self.index += 2
                right_operand = self.parse_operand()
                left_operand = left_operand or right_operand
            else:
                break

        return left_operand

    def parse_operand(self):
        if self.expression[self.index] == "!":
            self.index += 1
            operand = self.parse_operand()
            return not operand
        elif self.expression[self.index] == "(":
            self.index += 1
            operand = self.parse_expression()
            if self.expression[self.index] != ")":
                raise SyntaxError("Missing closing parenthesis")
            self.index += 1
            return operand
        else:
            return self.parse_semantics()

    def parse_semantics(self):

        def _eval(exp):
            keys = exp.split('.')
            next_cond = self._conditions
            for key in keys:
                next_cond = next_cond.get(key, {})
            return bool(next_cond)

        start = self.index
        while self.index < len(self.expression) and self.expression[self.index] not in ["&&", "||", "!", "(", ")"]:
            self.index += 1
        return _eval(self.expression[start:self.index])
