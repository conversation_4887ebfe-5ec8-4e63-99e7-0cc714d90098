#!/usr/bin/env python3
# coding=utf-8

import os
import docker
import shutil
import subprocess
from abc import ABC, abstractmethod
from typing import Optional, Union, Dict, Any

from utils.loggers import LoggerProxy

logger = LoggerProxy("player")
from utils.wraps import retry
from utils.path import get_share_path


class ContainerOperator(ABC):
    """容器操作抽象接口"""

    @abstractmethod
    def copy_from(
        self, container_name: str, source_path: str, target_path: str
    ) -> bool:
        """从容器中拷贝文件或目录到指定路径"""
        pass

    @abstractmethod
    def exists(self, container_name: str) -> bool:
        """检查容器是否存在"""
        pass


class DockerContainerOperator(ContainerOperator):
    """Docker容器操作实现类"""

    def __init__(self):
        """初始化Docker客户端"""
        self.client = docker.from_env()

    def exists(self, container_name: str) -> bool:
        """
        检查容器是否存在

        参数:
            container_name (str): 容器名称

        返回:
            bool: 容器是否存在
        """
        try:
            self.client.containers.get(container_name)
            return True
        except docker.errors.NotFound:
            return False

    def copy_from(
        self, container_name: str, source_path: str, target_path: str
    ) -> bool:
        """
        从容器中拷贝文件或目录到指定路径

        参数:
            container_name (str): 容器名称
            source_path (str): 容器内需要拷贝的文件或目录路径
            target_path (str): 拷贝到的目标路径

        返回:
            bool: 拷贝是否成功
        """
        if not self.exists(container_name):
            logger.error(f"容器 {container_name} 不存在")
            return False

        try:
            # 确保目标目录存在
            os.makedirs(os.path.dirname(target_path), exist_ok=True)

            # 使用docker cp命令直接拷贝
            cmd = f"docker cp {container_name}:{source_path} {target_path}"
            result = subprocess.run(
                cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE
            )

            if result.returncode != 0:
                logger.error(f"从容器拷贝文件失败: {result.stderr.decode('utf-8')}")
                return False

            logger.info(
                f"成功从容器 {container_name} 拷贝 {source_path} 到 {target_path}"
            )
            return True

        except Exception as e:
            logger.error(f"从容器拷贝文件失败: {str(e)}")
            return False


# 创建默认的容器操作实例
_default_container_operator = DockerContainerOperator()


@retry(tries=3)
def copy_from_container(
    container_name: str, source_path: str, target_path: str = get_share_path()
) -> bool:
    """
    从容器中拷贝文件或目录到指定路径

    参数:
        container_name (str): 容器名称
        source_path (str): 容器内需要拷贝的文件或目录路径
        target_path (str): 拷贝到的目标路径，默认为共享路径

    返回:
        bool: 拷贝是否成功
    """
    return _default_container_operator.copy_from(
        container_name, source_path, target_path
    )
