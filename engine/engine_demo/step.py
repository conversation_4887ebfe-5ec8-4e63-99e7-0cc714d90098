from utils.path import get_task_workspace_path, get_task_casefile_path, get_task_map_path, get_task_vehicle_path, get_task_log_path, get_task_result_path, get_task_record_path
from common_steps.stepbase import EngineStepBase
from utils.loggers import LoggerProxy
logger = LoggerProxy() 
from std_msgs.msg import String

import rospy

class Step(EngineStepBase):   
    def init(self) -> bool:
        logger.info("engine_demo engine init")
        logger.info(f"case_id: {self.case_id}")
        logger.info(f"测试 logs 打印------------------------------")
        get_task_workspace_path(self.case_id)
        
        # 打印ros节点的信息
        logger.info(f"ROS节点信息: {rospy.get_name()}")
        logger.info(f"ROS节点状态: {rospy.core.is_initialized()}")            
        logger.info("ROS节点已初始化，可以正常使用ROS功能")
        logger.info("engine_demo engine init")

        return True
    
    def run(self) -> bool:
        i = 0
        # 循环发布一个话题
        while i < 50:
            pub = rospy.Publisher('test_topic', String, queue_size=10)
            pub.publish("Hello, ROS!{}".format(i))
            rospy.sleep(1)
            i += 1
        logger.info("engine_demo engine run")
        return True
    
    def end(self) -> bool:
        logger.info("engine_demo engine end")
        return True
    
    def clean(self) -> bool:
        logger.info("engine_demo engine clean")
        return True

