# FlowControlPlayer 配置示例文件

# 超时设置（秒）
timeout: 0.5
timeout_cnt_threshold: 5000  # 超时次数阈值，超过此阈值将退出播包程序

# 主话题设置
main_topic:
  name: "/perception/fusion/obstacles"         # 原始话题名
  type: "autopilot_msgs/BinaryData"   # 消息类型

# 需要发布的话题
pub_topics:
  - name: "/chassis/command"
    type: "autopilot_msgs/BinaryData"
  - name: "/chassis/vehicle_state"
    type: "autopilot_msgs/BinaryData"
  - name: "/hadmap/objects"
    type: "autopilot_msgs/BinaryData"
  - name: "/hadmap/path_info_msg"
    type: "autopilot_msgs/BinaryData"
  - name: "/hadmap/speed_limit"
    type: "autopilot_msgs/BinaryData"
  - name: "/hadmap/stop_line"
    type: "autopilot_msgs/BinaryData"
  - name: "/hadmap_engine/map_msg"
    type: "autopilot_msgs/BinaryData"
  - name: "/localization/global"
    type: "autopilot_msgs/BinaryData"
  - name: "/localization/global"
    type: "autopilot_msgs/BinaryData"
    remap: "/localization/global_bag"
  - name: "/perception/camera/trfclts_state"
    type: "std_msgs/String"
  - name: "/prediction/PredictionObstacles"
    type: "autopilot_msgs/BinaryData"
  - name: "/perception/fusion/obstacles"
    type: "autopilot_msgs/BinaryData"
  - name: "/perception/fusion/obstacles"
    type: "autopilot_msgs/BinaryData"
    remap: "/perception/fusion/obstacles_bag"
  - name: "/planning/global_trajectory"
    type: "autopilot_msgs/BinaryData"
  # - name: "/planning/cmd"
  #   type: "autopilot_msgs/BinaryData"
  - name: "/planning/trajectory"
    type: "autopilot_msgs/BinaryData"
    remap: "/planning/trajectory_bag"
  # - name: "/tf"
  #   type: "tf2_msgs/TFMessage"
  - name: "/perception/fusion/fusion_viz"
    type: "visualization_msgs/MarkerArray"
  # - name: "/perception/radar/viz_obstacle"
  #   type: "visualization_msgs/MarkerArray"


# 需要等待反馈的话题
feedback_topics:
  - name: "/lgsvl_pdp_state"
    type: "lgsvl_msgs/PDPMoveArray"
  - name: "/lgsvl_trajector"
    type: "lgsvl_msgs/RefTrajectory"
  - name: "/lgsvl_pdp_obstacle"
    type: "lgsvl_msgs/PDPObstacleArray"
  - name: "/planning/trajectory"
    type: "autopilot_msgs/BinaryData"
 
record_all: true

# 需要录制的话题
record_topics:
  - name: "/clock"
    type: "rosgraph_msgs/Clock"
  - name: "/prediction/PredictionObstacles"
    type: "autopilot_msgs/BinaryData"
  - name: "/planning/trajectory"
    type: "autopilot_msgs/BinaryData"
  - name: "/planning/trajectory_bag"
    type: "autopilot_msgs/BinaryData"
  - name: "/chassis/vehicle_state"
    type: "autopilot_msgs/BinaryData"
  - name: "/perception/fusion/obstacles"
    type: "autopilot_msgs/BinaryData"
  - name: "/perception/fusion/obstacles_bag"
    type: "autopilot_msgs/BinaryData"
  - name: "/lgsvl_trajector"
    type: "lgsvl_msgs/RefTrajectory"
  - name: "/lgsvl_pdp_state"
    type: "lgsvl_msgs/PDPMoveArray"
  - name: "/planning/global_trajectory"
    type: "autopilot_msgs/BinaryData"
  - name: "/localization/global"
    type: "autopilot_msgs/BinaryData"
  - name: "/localization/global_bag"
    type: "autopilot_msgs/BinaryData"
  - name: "/chassis/chassis2025"
    type: "autopilot_msgs/BinaryData"
  - name: "/EgoState"
    type: "lgsvl_msgs/CanBusData"
  - name: "/odom"
    type: "nav_msgs/Odometry"
  - name: "/imu"
    type: "sensor_msgs/Imu"
  - name: "/autopilot/AutoPilotCmd"
    type: "std_msgs/Int32"
  - name: "/fsm/fsm_state"
    type: "autopilot_msgs/BinaryData"
  - name: "/perception/camera/trfclts_state"
    type: "std_msgs/String"
  - name: "/lgsvl_pdp_signal"
    type: "lgsvl_msgs/SignalArray"
  - name: "/lgsvl_pdp_control"
    type: "lgsvl_msgs/PDPControl"
  - name: "/planning/autopilot_model_rviz"
    type: "visualization_msgs/Marker"
  - name: "/planning/decision_state_rviz"
    type: "visualization_msgs/Marker"
  - name: "/planning/driving_area"
    type: "visualization_msgs/MarkerArray"
  - name: "/planning/leading_obs_rviz"
    type: "visualization_msgs/Marker"
  - name: "/planning/markers"
    type: "visualization_msgs/MarkerArray"
  - name: "/planning/obs_viz"
    type: "visualization_msgs/MarkerArray"
  - name: "/planning/predict_danger_obs"
    type: "visualization_msgs/MarkerArray"
  - name: "/perception/fusion/fusion_viz"
    type: "visualization_msgs/MarkerArray"
  - name: "/perception/radar/viz_obstacle"
    type: "visualization_msgs/MarkerArray"
  - name: "/tf"
    type: "tf2_msgs/TFMessage"
  - name: "/EgoState"
    type: "lgsvl_msgs/CanBusData"
  - name: "/odom"
    type: "nav_msgs/Odometry"
  - name: "/imu"
    type: "sensor_msgs/Imu"
  - name: "/lgsvl_pdp_obstacle"
    type: "lgsvl_msgs/PDPObstacleArray"
  - name: "/lgsvl/predict_traj"
    type: "lgsvl_msgs/PredictTrajectory"


# 屏蔽配置
shield_config:
  time_point_ns: 1747970483000000000  # 开始屏蔽的纳秒级时间戳
  topics:
    - /chassis/vehicle_state
    - /localization/global
    - /planning/trajectory
    - /perception/fusion/obstacles
    - /perception/fusion/fusion_viz
    - /hadmap/objects
    - /hadmap/path_info_msg
    - /hadmap/speed_limit
    - /hadmap/stop_line
    - /hadmap_engine/map_msg
