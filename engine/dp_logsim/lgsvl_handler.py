#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from api.lgsvl_client.utils.lgsvl_controller import LgsvlController
from api.lgsvl_client.lgsvl_connection import SimConnection
from api.lgsvl_client.utils.pub_traj import TrajectoryProcessor
from api.lgsvl_client.utils.pub_obstacle import ObstacleProcessor
from api.lgsvl_client.utils.pub_localization import create_localization_publisher
from api.lgsvl_client.utils.pub_loc import create_localization_processor
from api.lgsvl_client.utils.pub_traffic_light import TrafficLightProcessor
from api.lgsvl_client.utils.pub_prediction import create_prediction_publisher
from api.lgsvl_client.utils.algo_obstacle_trans import ObstacleCoordinateTransformer
from api.lgsvl_client.routing_request import lgsvlRoutingRequest
from utils.loggers import LoggerProxy
from utils.path import get_task_record_path, get_tasks_info
from typing import Optional
import json
import os


# 使用完整的ZONES配置，包含区域原点坐标
ZONES = [
    {
        "Name": "environment_ShunYi",
        "UtmZoneId": 50,
        "Origin_East": 475817.965,
        "Origin_North": 4446671.6295,
    },
    {
        "Name": "environment_GuoZhan", 
        "UtmZoneId": 50,
        "Origin_East": 459756.7004,
        "Origin_North": 4432502.0874,
    },
    {
        "Name": "environment_YiZhuang",
        "UtmZoneId": 50,
        "Origin_East": 458897.87,
        "Origin_North": 4402632.41,
    },
    {
        "Name": "environment_DaXingLicensePlate",
        "UtmZoneId": 50,
        "Origin_East": 453375.8214,
        "Origin_North": 4398686.6208,
    },
    {
        "Name": "environment_TongShunLu",
        "UtmZoneId": 50,
        "Origin_East": 470530,
        "Origin_North": 4446630,
    },
    {
        "Name": "environment_DaYunHeSenLinGongYuan",
        "UtmZoneId": 50,
        "Origin_East": 475000,
        "Origin_North": 4416400,
    },
    {
        "Name": "environment_ShangHai",
        "UtmZoneId": 51,
        "Origin_East": 343152.367,
        "Origin_North": 3455742.062,
    },
    {
        "Name": "environment_HuZhou",
        "UtmZoneId": 51,
        "Origin_East": 213642.9192,
        "Origin_North": 3379657.2215,
    },
    {
        "Name": "environment_HengYang",
        "UtmZoneId": 49,
        "Origin_East": 655369.5100,
        "Origin_North": 2965689.3947,
    },
    {
        "Name": "environment_HengYang1",
        "UtmZoneId": 49,
        "Origin_East": 655936.4148,
        "Origin_North": 2970977.9668,
    },
    {
        "Name": "environment_HengYang2",
        "UtmZoneId": 49,
        "Origin_East": 661742.2959,
        "Origin_North": 2960092.8443,
    },
    {
        "Name": "environment_HengYang3",
        "UtmZoneId": 49,
        "Origin_East": 663487.7356,
        "Origin_North": 2975315.4375,
    },
    {
        "Name": "environment_JiaXing",
        "UtmZoneId": 51,
        "Origin_East": 264963.55,
        "Origin_North": 3395912.6,
    },
    {
        "Name": "environment_Tianjin",
        "UtmZoneId": 50,
        "Origin_East": 504211.6185,
        "Origin_North": 4322636.6207,
    },
    {
        "Name": "environment_ErHai",
        "UtmZoneId": 47,
        "Origin_East": 622841.1852,
        "Origin_North": 2834412.5557,
    },
    {
        "Name": "environment_YanTai",
        "UtmZoneId": 51,
        "Origin_East": 376899.0475,
        "Origin_North": 4145850.2253,
    },
]

logger = LoggerProxy("lgsvl")
import lgsvl
import subprocess
import os
import time
import math


class LgsvlHandler:
    """处理LGSVL模拟器启动和关闭"""

    def __init__(
        self,
        config: dict,
        lgsvl_root: str,
        case_id: str,
        case_path: Optional[str] = None,
    ):
        self.lgsvl_controller = LgsvlController(
            lgsvl_root, case_id
        )  # 初始化时传入case_id
        self.connection = None  # 新增：用于保存SimConnection实例
        self.config = config  # 存储配置
        self.component_name = "LgsvlHandler"  # 添加组件名称
        self.initialized_successfully = False  # 添加初始化状态
        self.traj_pub = None  # 添加轨迹处理器引用
        self.obstacle_processor = None  # 添加障碍物处理器引用
        self.loc_processor = None  # 添加定位处理器引用
        self.traffic_light_processor = None  # 添加交通灯处理器引用
        self.obstacle_coord_transformer = None  # 添加障碍物坐标变换器引用
        self.localization_publisher = None  # 添加定位数据发布器引用
        self.prediction_publisher = None  # 添加预测轨迹发布器引用
        self.lgsvl_root = lgsvl_root
        self.rosbridge_websocket_process = None
        self.rosbridge_log_file = None
        self.case_id = case_id
        self.case_path = case_path
        self.record_started = False  # 添加录屏状态标志

    def _load_vehicle_from_tasks_info(self) -> str:
        """
        从 tasks_info.json 文件中读取 vehicle 字段值作为 agent_name
        
        返回:
            str: vehicle 字段的值，如果读取失败则返回默认值 "JL"
        """
        try:
            tasks_info_path = get_tasks_info()
            logger.info(f"[{self.component_name}] 尝试从 {tasks_info_path} 读取 vehicle 字段")

            if not os.path.exists(tasks_info_path):
                logger.warning(f"[{self.component_name}] tasks_info.json 文件不存在: {tasks_info_path}")
                return "JL"

            with open(tasks_info_path, "r", encoding="utf-8") as f:
                tasks_data = json.load(f)

            # 首先尝试读取根级别的 vehicle 字段
            vehicle_value = tasks_data.get("vehicle")
            if vehicle_value:
                logger.info(f"[{self.component_name}] 从根级别读取到 vehicle: {vehicle_value}")
                return str(vehicle_value)

            # 如果没有 vehicle 字段，尝试从 caseList 中的第一个 case 的 vehicle_config 读取
            case_list = tasks_data.get("caseList", [])
            if case_list and isinstance(case_list, list) and len(case_list) > 0:
                first_case = case_list[0]
                vehicle_config = first_case.get("vehicle_config")
                if vehicle_config:
                    logger.info(f"[{self.component_name}] 从第一个case的vehicle_config读取到: {vehicle_config}")
                    return str(vehicle_config)

            # 如果都没有找到，使用默认值
            logger.warning(f"[{self.component_name}] 未找到 vehicle 或 vehicle_config 字段，使用默认值 'JL'")
            return "JL"

        except json.JSONDecodeError as e:
            logger.error(f"[{self.component_name}] 解析 tasks_info.json 文件失败: {e}")
            return "JL"
        except Exception as e:
            logger.error(f"[{self.component_name}] 读取 tasks_info.json 时发生错误: {e}", exc_info=True)
            return "JL"

    def _get_zone_info_from_scene(self, scene: str) -> dict:
        """
        根据场景名称获取对应的区域信息

        参数:
            scene (str): 场景名称，如"environment_GuoZhan"

        返回:
            dict: 区域信息，包含UtmZoneId、Origin_East、Origin_North
        """
        for zone in ZONES:
            if zone.get("Name") == scene:
                return zone

        logger.warning(
            f"[{self.component_name}] 未找到场景 '{scene}' 对应的区域信息，使用默认值"
        )
        return {
            "Name": scene,
            "UtmZoneId": 50,
            "Origin_East": 0.0,
            "Origin_North": 0.0,
        }

    def _send_routing_request(
        self, ego_initial: dict, ego_dest: dict, scene: str
    ) -> bool:
        """
        发送路径规划请求，将Unity格式坐标转换为UTM格式

        参数:
            ego_initial (dict): 起点信息，包含position和rotation
            ego_dest (dict): 终点信息，包含x、y、z坐标
            scene (str): 场景名称，用于获取UTM区域ID

        返回:
            bool: 路径规划请求是否发送成功
        """
        try:
            # 提取Unity格式坐标
            ego_initial_pos = ego_initial.get("position", {})
            ego_initial_rot = ego_initial.get("rotation", {})

            # 从config获取起点和终点的Unity格式坐标
            unity_start_x = ego_initial_pos.get("x", 0.0)  # Unity的x坐标
            unity_start_z = ego_initial_pos.get("z", 0.0)  # Unity的z坐标
            unity_start_yaw = ego_initial_rot.get("y", 0.0)  # Unity的y轴旋转角

            unity_dest_x = ego_dest.get("x", 0.0)  # Unity的x坐标
            unity_dest_z = ego_dest.get("z", 0.0)  # Unity的z坐标

            # 获取区域信息（包含原点坐标）
            zone_info = self._get_zone_info_from_scene(scene)
            origin_east = zone_info.get("Origin_East", 0.0)
            origin_north = zone_info.get("Origin_North", 0.0)
            utm_zone = zone_info.get("UtmZoneId", 50)
            
            # 完整的坐标转换逻辑：
            # extract_bag.py中的转换：
            # 1. UTM坐标 -> 相对坐标: adjusted_x = raw_x - zone["Origin_East"]
            # 2. 相对坐标 -> Unity格式: unity_x = -adjusted_y, unity_z = adjusted_x
            # 
            # 反向转换：
            # 1. Unity格式 -> 相对坐标: relative_x = unity_z, relative_y = -unity_x
            # 2. 相对坐标 -> UTM坐标: utm_x = relative_x + zone["Origin_East"]
            
            # 步骤1：Unity格式 -> 相对坐标
            relative_start_x = unity_start_z     # Unity的z -> 相对坐标的x
            relative_start_y = -unity_start_x    # Unity的x取负值 -> 相对坐标的y
            relative_dest_x = unity_dest_z       # Unity的z -> 相对坐标的x
            relative_dest_y = -unity_dest_x      # Unity的x取负值 -> 相对坐标的y
            
            # 步骤2：相对坐标 -> UTM坐标（关键的加法操作）
            utm_start_x = relative_start_x + origin_east   # 加上区域原点的East坐标
            utm_start_y = relative_start_y + origin_north  # 加上区域原点的North坐标
            utm_dest_x = relative_dest_x + origin_east     # 加上区域原点的East坐标  
            utm_dest_y = relative_dest_y + origin_north    # 加上区域原点的North坐标

            # 角度转换：Unity的y轴旋转角转换为UTM的yaw角
            # extract_bag.py中：start_yaw_final = 360 - start_yaw_deg
            # 反向转换：utm_yaw_deg = 360 - unity_start_yaw
            utm_start_yaw_deg = 360.0 - unity_start_yaw if unity_start_yaw != 0 else 0.0
            utm_start_yaw_rad = math.radians(utm_start_yaw_deg)


            logger.info(f"[{self.component_name}] 坐标转换结果:")
            logger.info(f"  场景: {scene}, UTM区域ID: {utm_zone}")
            logger.info(f"  区域原点: East={origin_east}, North={origin_north}")
            logger.info(f"  Unity起点: x={unity_start_x}, z={unity_start_z}, yaw={unity_start_yaw}")
            logger.info(f"  相对起点: x={relative_start_x}, y={relative_start_y}")
            logger.info(f"  UTM起点: x={utm_start_x}, y={utm_start_y}, yaw={utm_start_yaw_deg}°")
            logger.info(f"  Unity终点: x={unity_dest_x}, z={unity_dest_z}")
            logger.info(f"  相对终点: x={relative_dest_x}, y={relative_dest_y}")
            logger.info(f"  UTM终点: x={utm_dest_x}, y={utm_dest_y}")

            # 创建并配置路径规划请求
            routing_request = lgsvlRoutingRequest(
                start_x=utm_start_x,
                start_y=utm_start_y,
                start_yaw=utm_start_yaw_rad,
                dest_x=utm_dest_x,
                dest_y=utm_dest_y,
                utm_zone=utm_zone,
            )

            # 发送路径规划请求
            logger.info(f"[{self.component_name}] 发送路径规划请求...")
            routing_request.send_routing_request()

            logger.info(f"[{self.component_name}] 路径规划请求发送完成")
            return True

        except Exception as e:
            logger.error(
                f"[{self.component_name}] 发送路径规划请求失败: {e}", exc_info=True
            )
            return False

    def setup(self) -> bool:
        """
        初始化仿真环境，包括SimConnection、场景、车辆、环境等。
        返回:
            bool: 初始化是否成功
        """
        logger.info(f"[{self.component_name}] 开始设置...")

        # 获取屏蔽时间
        shield_config = self.config.get("shield_config", {})
        time_point_ns = shield_config.get("time_point_ns")

        if time_point_ns is not None:
            logger.info(f"[{self.component_name}] 使用屏蔽时间: {time_point_ns}ns")
        else:
            logger.warning(f"[{self.component_name}] 未配置屏蔽时间，将立即发布轨迹")

        # 启动轨迹转换（从bag的格式到lgsvl支持的格式）
        self.traj_pub = TrajectoryProcessor(shield_time_ns=time_point_ns)

        # 启动障碍物数据处理器（从感知数据到PDPObstacleArray消息的转换）
        self.obstacle_processor = ObstacleProcessor()
        logger.info(f"[{self.component_name}] 障碍物处理器已启动，无屏蔽时间限制")

        # 启动定位数据处理器（从定位数据到PDPControl消息的转换）
        self.loc_processor = create_localization_processor(shield_time_ns=time_point_ns)
        logger.info(
            f"[{self.component_name}] 定位处理器已启动，屏蔽时间: {time_point_ns}ns"
        )

        # 启动交通灯数据处理器（从感知交通灯数据到LGSVL信号的转换）
        self.traffic_light_processor = TrafficLightProcessor()
        logger.info(f"[{self.component_name}] 交通灯处理器已启动")

        # 启动障碍物坐标变换器（从bag坐标系转换到当前坐标系）
        self.obstacle_coord_transformer = ObstacleCoordinateTransformer(
            shield_time_ns=time_point_ns
        )
        logger.info(
            f"[{self.component_name}] 障碍物坐标变换器已启动，屏蔽时间: {time_point_ns}ns"
        )

        # 启动localization转换（从lgsvl的格式到算法节点需要的定位等话题的格式）
        self.localization_publisher = create_localization_publisher()
        self.localization_publisher.start()

        # 启动预测轨迹发布器（从预测系统数据到LGSVL预测轨迹格式的转换）
        self.prediction_publisher = create_prediction_publisher()
        self.prediction_publisher.start()
        logger.info(f"[{self.component_name}] 预测轨迹发布器已启动")

        # 使用subprocess启动rosbridge_websocket
        task_record_path = get_task_record_path(self.case_id)
        rosbridge_log_path = os.path.join(task_record_path, "rosbridge.log")
        self.rosbridge_log_file = open(rosbridge_log_path, "w")

        # 设置roslaunch所需的环境变量
        ros_env = os.environ.copy()
        ros_package_path = ros_env.get("ROS_PACKAGE_PATH", "")
        ros_package_path = "/home/<USER>/workspace/mount/dist-packages" + (
            ":" + ros_package_path if ros_package_path else ""
        )
        ros_package_path = "/opt/msim/api/lgsvl_client" + (
            ":" + ros_package_path if ros_package_path else ""
        )
        ros_env["ROS_PACKAGE_PATH"] = ros_package_path

        python_path = ros_env.get("PYTHONPATH", "")
        python_path = "/opt/msim/" + (":" + python_path if python_path else "")
        python_path = "/opt/msim/api/lgsvl_client" + (
            ":" + python_path if python_path else ""
        )
        python_path = "/home/<USER>/workspace/mount/dist-packages" + (
            ":" + python_path if python_path else ""
        )
        python_path = "/home/<USER>/workspace/mount/dist-packages/common" + (
            ":" + python_path if python_path else ""
        )
        ros_env["PYTHONPATH"] = python_path

        self.rosbridge_websocket_process = subprocess.Popen(
            [
                "roslaunch",
                "/opt/msim/api/lgsvl_client/launch/rosbridge_websocket.launch",
            ],
            stdout=self.rosbridge_log_file,
            stderr=subprocess.STDOUT,
            env=ros_env,
        )
        logger.info(
            f"[{self.component_name}] rosbridge_websocket 已启动，日志输出到: {rosbridge_log_path}"
        )

        # 获取当前文件的父目录
        parent_dir = os.path.dirname(os.path.realpath(__file__))
        # 获取lgsvl_root的父目录
        lgsvl_root_parent_dir = os.path.dirname(self.lgsvl_root)
        # 获取lgsvl_root的父目录的父目录
        lgsvl_root_parent_parent_dir = os.path.dirname(lgsvl_root_parent_dir)

        # 读取仿真相关配置
        host = self.config.get("host", "localhost")
        port = self.config.get("port", 9090)
        ext_case_id = self.config.get("ext_case_id", "")
        ego_dest = self.config.get("ego_dest", {})
        traj_file = self.config.get("traj_file", "traj.csv")
        scene = self.config.get("scene", "environment_ShunYi")
        agent_name = self._load_vehicle_from_tasks_info()
        ego_initial = self.config.get("ego_initial", {})

        logger.info(f"[{self.component_name}] LGSVL配置加载结果:")
        logger.info(f"  - host: {host}")
        logger.info(f"  - port: {port}")
        logger.info(f"  - ext_case_id: {ext_case_id}")
        logger.info(f"  - scene: {scene}")
        logger.info(f"  - agent_name: {agent_name}")
        logger.info(f"  - ego_dest: {ego_dest}")
        logger.info(f"  - ego_initial: {ego_initial}")

        ego_initial_pos = ego_initial.get("position", {})
        ego_initial_rot = ego_initial.get("rotation", {})

        # 发送路径规划请求
        routing_success = self._send_routing_request(ego_initial, ego_dest, scene)
        if not routing_success:
            logger.warning(
                f"[{self.component_name}] 路径规划请求发送失败，继续进行仿真设置"
            )

        # 初始化SimConnection - 先启动LGSVL，再进行连接重试
        logger.info(
            f"[{self.component_name}] 开始连接LGSVL模拟器，将进行最多10次重试，每次30秒..."
        )

        # 创建SimConnection实例（只启动一次LGSVL）
        self.connection = SimConnection(
            seconds=30,
            scene=scene,
            host=host,
            lgsvl_root=self.lgsvl_root,
            auto_start_lgsvl=True,
            case_id=self.case_id,
        )

        success = False
        sim_connection = None
        for retry_count in range(1, 11):
            try:
                logger.info(
                    f"[{self.component_name}] 第{retry_count}/10次连接尝试（30秒超时）..."
                )
                # 只有第一次尝试时启动LGSVL，后续重试只进行连接
                if retry_count == 1:
                    self.connection.__enter__()
                else:
                    # 后续重试时，创建新连接但不启动LGSVL进程
                    old_connection = self.connection
                    self.connection = SimConnection(
                        seconds=30,
                        scene=scene,
                        host=host,
                        lgsvl_root=self.lgsvl_root,
                        auto_start_lgsvl=False,
                        case_id=self.case_id,
                    )
                    self.connection.__enter__()

                sim_connection = self.connection
                logger.info(f"[{self.component_name}] 第{retry_count}次连接成功！")
                success = True
                break
            except ConnectionError as retry_e:
                logger.warning(
                    f"[{self.component_name}] 第{retry_count}次连接失败: {retry_e}"
                )
                # 清理失败的连接，但不关闭LGSVL进程
                try:
                    if hasattr(self.connection, "sim") and self.connection.sim:
                        self.connection.sim = None
                except Exception as cleanup_e:
                    logger.warning(
                        f"[{self.component_name}] 清理连接时出错: {cleanup_e}"
                    )

                if retry_count < 10:
                    logger.info(f"[{self.component_name}] 等待2秒后进行下一次重试...")
                    time.sleep(2)
                continue
            except Exception as retry_e:
                logger.error(
                    f"[{self.component_name}] 第{retry_count}次连接出现异常: {retry_e}"
                )
                # 清理失败的连接，但不关闭LGSVL进程
                try:
                    if hasattr(self.connection, "sim") and self.connection.sim:
                        self.connection.sim = None
                except Exception as cleanup_e:
                    logger.warning(
                        f"[{self.component_name}] 清理连接时出错: {cleanup_e}"
                    )

                if retry_count < 10:
                    time.sleep(2)
                continue

        if not success:
            logger.error(
                f"[{self.component_name}] 所有连接尝试均失败，无法连接LGSVL模拟器"
            )
            return False

        try:
            # 表明是PDP
            sim_connection.exec_ext_cmd("EXT_CTR:{}".format("PDP"), lgsvl.ObjectState())

            # 自车信息
            ego_state = lgsvl.AgentState()
            ego_state.transform.position = lgsvl.Vector(
                ego_initial_pos.get("x", 0.0),
                ego_initial_pos.get("y", 0.0),
                ego_initial_pos.get("z", 0.0),
            )
            ego_state.transform.rotation = lgsvl.Vector(
                ego_initial_rot.get("x", 0.0),
                ego_initial_rot.get("y", 0.0),
                ego_initial_rot.get("z", 0.0),
            )

            ego = sim_connection.sim.add_agent(
                agent_name, lgsvl.AgentType.EGO, ego_state
            )
            ego.connect_bridge(host, port)
            # 环境
            sim_connection.exec_ext_cmd(
                "EXT_ENV:{}".format("prod"), lgsvl.ObjectState()
            )
            # 场景
            sim_connection.exec_ext_cmd(
                "EXT_CASE_ID:{}".format(ext_case_id), lgsvl.ObjectState()
            )
            # 终点
            ego_dest_state = lgsvl.ObjectState()
            ego_dest_state.transform.position = lgsvl.Vector(
                ego_dest.get("x", 0), ego_dest.get("y", 0), ego_dest.get("z", 0)
            )
            sim_connection.exec_ext_cmd("EXT_DEST_POS", ego_dest_state)

            # 轨迹 - 使用case目录下的traj.csv文件
            if not self.case_path:
                logger.error(
                    f"[{self.component_name}] case_path未提供，无法查找轨迹文件"
                )
                return False

            # 在case目录中递归查找traj.csv文件
            found_files = []
            for root, dirs, files in os.walk(self.case_path):
                if traj_file in files:
                    found_files.append(os.path.join(root, traj_file))

            if not found_files:
                logger.error(
                    f"[{self.component_name}] 在case目录 {self.case_path} 中未找到 {traj_file} 文件"
                )
                return False

            map_file = found_files[0]
            if len(found_files) > 1:
                logger.warning(
                    f"[{self.component_name}] 找到多个 {traj_file} 文件，使用第一个: {map_file}"
                )

            with open(map_file, "r") as f:
                traj_data = f.readlines()
            sim_connection.exec_trajectoy_cmd(traj_data)

            self.initialized_successfully = True
            logger.info(f"[{self.component_name}] 设置成功完成。")

            # 开始录屏
            try:
                record_save_path = os.path.join(
                    get_task_record_path(self.case_id), "lgsvl_record.mp4"
                )
                self.lgsvl_controller.record_start(record_save_path)
                self.record_started = True
                logger.info(
                    f"[{self.component_name}] 录屏已开始，保存路径: {record_save_path}"
                )
            except Exception as e:
                logger.error(
                    f"[{self.component_name}] 开始录屏时出错: {e}", exc_info=True
                )

            return True
        except Exception as e:
            logger.error(
                f"[{self.component_name}] setup 初始化仿真环境异常: {e}", exc_info=True
            )
            return False

    def cleanup(self) -> bool:
        logger.info(f"[{self.component_name}] 开始清理...")
        cleanup_success = True

        # 确保录屏已停止
        if self.record_started:
            try:
                self.lgsvl_controller.record_stop()
                self.record_started = False
                logger.info(f"[{self.component_name}] 录屏已停止。")
            except Exception as e:
                logger.error(
                    f"[{self.component_name}] 停止录屏时出错: {e}", exc_info=True
                )
                cleanup_success = False

        # 关闭traj_pub
        if self.traj_pub:
            try:
                self.traj_pub.set_stop(True)
            except Exception as e:
                logger.error(f"[{self.component_name}] 停止traj_pub时出错: {e}")
                cleanup_success = False

        # 关闭loc_processor
        if self.loc_processor:
            try:
                self.loc_processor.set_stop(True)
                logger.info(f"[{self.component_name}] 定位处理器已停止")
            except Exception as e:
                logger.error(f"[{self.component_name}] 停止定位处理器时出错: {e}")
                cleanup_success = False

        # 关闭obstacle_processor
        if self.obstacle_processor:
            try:
                self.obstacle_processor.set_stop(True)
                logger.info(f"[{self.component_name}] 障碍物处理器已停止")
            except Exception as e:
                logger.error(f"[{self.component_name}] 停止障碍物处理器时出错: {e}")
                cleanup_success = False

        # 关闭obstacle_coord_transformer
        if self.obstacle_coord_transformer:
            try:
                self.obstacle_coord_transformer.set_stop(True)
                logger.info(f"[{self.component_name}] 障碍物坐标变换器已停止")
            except Exception as e:
                logger.error(f"[{self.component_name}] 停止障碍物坐标变换器时出错: {e}")
                cleanup_success = False

        # 关闭traffic_light_processor
        if self.traffic_light_processor:
            try:
                self.traffic_light_processor.set_stop(True)
                logger.info(f"[{self.component_name}] 交通灯处理器已停止")
            except Exception as e:
                logger.error(f"[{self.component_name}] 停止交通灯处理器时出错: {e}")
                cleanup_success = False

        # 清理localization数据发布器
        if self.localization_publisher:
            try:
                self.localization_publisher.stop()
                logger.info(f"[{self.component_name}] localization数据发布器已停止")
            except Exception as e:
                logger.error(
                    f"[{self.component_name}] 停止localization数据发布器时出错: {e}"
                )
                cleanup_success = False

        # 关闭预测轨迹发布器
        if self.prediction_publisher:
            try:
                self.prediction_publisher.set_stop(True)
                logger.info(f"[{self.component_name}] 预测轨迹发布器已停止")
            except Exception as e:
                logger.error(f"[{self.component_name}] 停止预测轨迹发布器时出错: {e}")
                cleanup_success = False

        # 关闭SimConnection - 添加超时处理
        if self.connection is not None:
            try:
                logger.info(f"[{self.component_name}] 开始关闭SimConnection...")

                # 使用线程和超时来处理SimConnection的清理
                import threading

                connection_cleanup_exception = [None]

                def cleanup_connection():
                    try:
                        self.connection.__exit__(None, None, None)
                        logger.info(f"[{self.component_name}] SimConnection关闭成功")
                    except Exception as e:
                        connection_cleanup_exception[0] = e

                cleanup_thread = threading.Thread(
                    target=cleanup_connection, daemon=True
                )
                cleanup_thread.start()
                cleanup_thread.join(timeout=30)  # 30秒超时

                if cleanup_thread.is_alive():
                    logger.error(
                        f"[{self.component_name}] SimConnection关闭超时（30秒），强制跳过"
                    )
                    cleanup_success = False
                elif connection_cleanup_exception[0] is not None:
                    logger.error(
                        f"[{self.component_name}] 关闭SimConnection时出错: {connection_cleanup_exception[0]}",
                        exc_info=True,
                    )
                    cleanup_success = False

            except Exception as e:
                logger.error(
                    f"[{self.component_name}] SimConnection清理过程中发生意外错误: {e}",
                    exc_info=True,
                )
                cleanup_success = False
            finally:
                # 无论如何都要清空连接引用
                self.connection = None

        # 关闭rosbridge_websocket
        if self.rosbridge_websocket_process:
            try:
                self.rosbridge_websocket_process.terminate()
                self.rosbridge_websocket_process.wait(timeout=5)
                logger.info(
                    f"[{self.component_name}] rosbridge_websocket 进程已成功终止。"
                )
            except subprocess.TimeoutExpired:
                logger.warning(
                    f"[{self.component_name}] rosbridge_websocket 进程终止超时，强制关闭。"
                )
                try:
                    self.rosbridge_websocket_process.kill()
                except Exception as kill_e:
                    logger.error(
                        f"[{self.component_name}] 强制关闭rosbridge_websocket失败: {kill_e}"
                    )
                cleanup_success = False
            except Exception as e:
                logger.error(
                    f"[{self.component_name}] 关闭 rosbridge_websocket 进程时出错: {e}",
                    exc_info=True,
                )
                cleanup_success = False
            finally:
                self.rosbridge_websocket_process = None

        if self.rosbridge_log_file:
            try:
                self.rosbridge_log_file.close()
                self.rosbridge_log_file = None
            except Exception as e:
                logger.error(
                    f"[{self.component_name}] 关闭rosbridge日志文件时出错: {e}"
                )
                cleanup_success = False

        self.initialized_successfully = False

        if cleanup_success:
            logger.info(f"[{self.component_name}] 清理完成。")
        else:
            logger.warning(
                f"[{self.component_name}] 清理过程中遇到问题，但已尽力完成。"
            )

        return cleanup_success

    def lgsvl_run(self):
        """
        lgsvl开始执行任务
        """
        logger.info(f"[{self.component_name}] 开始执行任务...")

        # 执行仿真
        sim_connection = self.connection
        if sim_connection is not None:
            sim_connection.sim.run(1000, 1)
