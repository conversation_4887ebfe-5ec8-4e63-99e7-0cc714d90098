#!/usr/bin/python3
# -*- coding: utf-8 -*-
import rospy
from math import pi
from lgsvl_msgs.msg import CanBusData
from nav_msgs.msg import Odometry
from sensor_msgs.msg import Imu
from autopilot_msgs.msg import BinaryData
from common.localization_pb2 import Localization
from common.chassis2025_pb2 import ChassisStates

# 全局状态变量
longitude = 0.0
latitude = 0.0
longitude_speed = 0.0
utm_zone_code = None
ego_acceleration_x = 0.0
ego_angular_z = 0.0
ego_odom_yaw = 0.0
ego_odom_roll = 0.0
ego_odom_pitch = 0.0
maximum_steering = 500.0  # 需根据实际车型调整

# 发布者
chassis25_pub = rospy.Publisher("/chassis/chassis2025", BinaryData, queue_size=1)
ego_loc_pub = rospy.Publisher("/localization/global", BinaryData, queue_size=1)


def on_ego_state(msg):
    """处理/EgoState，发布/chassis/chassis2025"""
    global longitude, latitude, longitude_speed, utm_zone_code
    longitude = msg.gps_longitude
    latitude = msg.gps_latitude
    longitude_speed = msg.speed_mps
    if utm_zone_code is None:
        utm_zone_code = int(longitude / 6) + 31

    # 构造并发布Chassis2025消息
    chassis25_msg = ChassisStates()

    # 正确设置header时间戳 (sec和nsec分开)
    now = rospy.Time.now()
    chassis25_msg.header.stamp.sec = now.secs
    chassis25_msg.header.stamp.nsec = now.nsecs

    chassis25_msg.steer_system_states.steer_wheel_angle = (
        msg.steer_pct * maximum_steering
    )
    chassis25_msg.vehicle_motion_states.speed = msg.speed_mps
    chassis25_msg.vehicle_motion_states.acceleration = ego_acceleration_x
    chassis25_msg.drive_system_states.throttle = msg.throttle_pct
    chassis25_msg.brake_system_states.brake_pedal_position = msg.brake_pct

    chassis25_data = chassis25_msg.SerializeToString()
    binary_msg = BinaryData(data=chassis25_data, name="chassis25.ChassisStates")
    chassis25_pub.publish(binary_msg)


def on_imu(msg):
    """更新IMU数据"""
    global ego_acceleration_x, ego_angular_z
    ego_acceleration_x = msg.linear_acceleration.x
    ego_angular_z = msg.angular_velocity.z


def on_gps_odom(msg):
    """处理/odom，发布/localization/global（依赖UTM区域和IMU数据）"""
    global ego_odom_roll, ego_odom_pitch, ego_odom_yaw
    if utm_zone_code is None:
        return  # 必须等待/EgoState初始化UTM区域

    ego_odom_roll = msg.pose.pose.orientation.x
    ego_odom_pitch = msg.pose.pose.orientation.y
    ego_odom_yaw = msg.pose.pose.orientation.z

    # 构造并发布Localization消息
    loc_msg = Localization()

    # 正确设置header时间戳 (sec和nsec分开)
    now = rospy.Time.now()
    loc_msg.header.stamp.sec = now.secs
    loc_msg.header.stamp.nsec = now.nsecs

    loc_msg.position.x = msg.pose.pose.position.x  # UTM X
    loc_msg.position.y = msg.pose.pose.position.y  # UTM Y
    loc_msg.longitude = longitude
    loc_msg.latitude = latitude
    loc_msg.yaw = ego_odom_yaw % (2 * pi)
    loc_msg.roll = ego_odom_roll % (2 * pi)
    loc_msg.pitch = ego_odom_pitch % (2 * pi)
    loc_msg.longitudinal_v = longitude_speed
    loc_msg.longitudinal_a = ego_acceleration_x
    loc_msg.yaw_v = ego_angular_z
    loc_msg.utm_zone = utm_zone_code

    loc_data = loc_msg.SerializeToString()
    binary_msg = BinaryData(data=loc_data, name="localization.Localization")
    ego_loc_pub.publish(binary_msg)


def lgsvl_callback():
    rospy.Subscriber("/EgoState", CanBusData, on_ego_state)
    rospy.Subscriber("/odom", Odometry, on_gps_odom, queue_size=1, tcp_nodelay=True)
    rospy.Subscriber("/imu", Imu, on_imu)


if __name__ == "__main__":
    rospy.init_node("sensor_bridge")
    # 订阅必要话题
    rospy.Subscriber("/EgoState", CanBusData, on_ego_state)
    rospy.Subscriber("/odom", Odometry, on_gps_odom, queue_size=1, tcp_nodelay=True)
    rospy.Subscriber("/imu", Imu, on_imu)
    rospy.spin()
