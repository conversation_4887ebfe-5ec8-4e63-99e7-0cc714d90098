#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from base_handler import BaseHandler

import time

from api.player.tools.flow_control_player import FlowControlPlayer


class RosbagHandler(BaseHandler):
    """处理播包、录包、以及在特定时间点切换话题发送方式。"""

    def __init__(
        self,
        config,
        logger_instance=None,
        flow_control_config_path="",
        bag_path="",
        clock_freq=0.0,
        remap=None,
        start_time=0.0,
        end_time=0.0,
        bag_record_path=None,
    ):
        super().__init__(
            component_name="播包服务",
            config=config,
            logger_instance=logger_instance,
        )
        self._simulation_running = False
        self._takeover = False
        self._manual_publishing_active = False
        self._current_sim_time = 0.0

        # 存储 FlowControlPlayer 参数
        self.flow_control_config_path = flow_control_config_path
        self.bag_path = bag_path
        self.clock_freq = clock_freq
        self.remap = remap if remap is not None else []
        self.start_time = start_time
        self.end_time = end_time
        self.bag_record_path = bag_record_path

        self.player = None
        self.initialized_successfully = False

    def setup(self) -> bool:
        self.logger.info(f"[{self.component_name}] 开始设置 (准备播包和录包节点)...")
        self.logger.info(
            f"flow_control_config_path: {self.flow_control_config_path}, bag_path: {self.bag_path}"
        )

        if not self.flow_control_config_path or not self.bag_path:
            self.logger.warning(
                f"[{self.component_name}] 未提供 flow_control_config_path 或 bag_path, 无法启动播放器。"
            )
            self.initialized_successfully = False
            return False

        try:
            # 2. 实例化 FlowControlPlayer
            self.logger.info(f"[{self.component_name}] 正在实例化 FlowControlPlayer...")
            # FlowControlPlayer 的构造函数需要 ros_version 参数，这里我们假设是 ros1
            # 因为 flow_control_player_cli.py 默认也是处理 ROS1 的场景，除非 ROS2 环境被明确检测到
            # FlowControlPlayer 内部有自动检测机制，但显式传递更安全
            # 根据原 cli 逻辑，start-roscore 意味着是ROS1环境
            self.player = FlowControlPlayer(
                config_path=self.flow_control_config_path,
                bag_path=self.bag_path,
                clock_freq=self.clock_freq,
                remap=self.remap,
                start_time=self.start_time,
                end_time=self.end_time,
                record_path=self.bag_record_path,
                ros_version="ros1",  # 显式指定为ros1，因为我们启动了roscore
                block_on_enter=False,  # 非阻塞方式，因为我们将在start_playback中控制
                shield_time_ns=self.config.get("shield_time_ns"),
            )
            self.logger.info(f"[{self.component_name}] FlowControlPlayer 实例化成功。")
            self.initialized_successfully = True
            return True

        except Exception as e:
            self.logger.error(
                f"[{self.component_name}] 设置播包服务时发生错误: {e}",
                exc_info=True,
            )
            self.initialized_successfully = False
            return False

    def start_playback(self) -> bool:
        """启动 FlowControlPlayer 进行播包。"""
        if not self.initialized_successfully or not self.player:
            self.logger.error(
                f"[{self.component_name}] 播包服务未成功初始化，无法启动播放。"
            )
            return False

        if self._simulation_running:
            self.logger.warning(f"[{self.component_name}] 播包已在运行中。")
            return True

        try:
            self.logger.info(
                f"[{self.component_name}] 尝试启动 FlowControlPlayer 播放..."
            )
            # FlowControlPlayer.start() 在非阻塞模式下会返回一个线程对象，或者None
            # 我们需要确保播放已经开始。block=False 使其在后台线程播放
            self.player.start(block=False)
            self._simulation_running = True
            try:
                while not self.player._shutdown.is_set():
                    time.sleep(0.1)
            except KeyboardInterrupt:
                self.logger.info("用户终止播放")
            if not self.player._shutdown.is_set():
                self.player.stop()
            self.logger.info(f"[{self.component_name}] FlowControlPlayer 已请求启动。")
            # 可以在这里添加一个短暂的延时或状态检查，以确认播放确实开始
            # time.sleep(1) # 例如，等待1秒
            # if not self.player._play_thread or not self.player._play_thread.is_alive():
            #     self.logger.error(f"[{self.component_name}] FlowControlPlayer 播放线程未能成功启动。")
            #     self._simulation_running = False
            #     return False
            return True
        except Exception as e:
            self.logger.error(
                f"[{self.component_name}] 启动 FlowControlPlayer 播放时发生错误: {e}",
                exc_info=True,
            )
            if not self.player._shutdown.is_set():
                self.player.stop()
            self._simulation_running = False
            return False

    def stop_simulation_tasks(
        self,
    ):  # 新增一个方法用于明确停止模拟任务，适配原有 clean 调用
        """停止所有由 RosbagHandler 管理的模拟相关任务。"""
        self.logger.info(f"[{self.component_name}] 请求停止模拟任务...")
        if self.player:
            self.logger.info(f"[{self.component_name}] 正在停止 FlowControlPlayer...")
            self.player.stop()
            self.logger.info(f"[{self.component_name}] FlowControlPlayer 已停止。")
        self._simulation_running = False

    def cleanup(self) -> bool:
        self.logger.info(f"[{self.component_name}] 开始清理...")
        self.stop_simulation_tasks()  # 确保所有运行时任务被要求停止

        all_cleaned = True
        try:
            self.player = None  # 清理播放器实例

        except Exception as e:
            self.logger.error(
                f"[{self.component_name}] 清理过程中发生异常: {e}",
                exc_info=True,
            )
            all_cleaned = False

        if all_cleaned:
            self.logger.info(f"[{self.component_name}] 清理成功完成。")
        else:
            self.logger.warning(f"[{self.component_name}] 清理时遇到问题。")

        self.initialized_successfully = False
        self._simulation_running = False  # 确保状态被重置
        return all_cleaned
