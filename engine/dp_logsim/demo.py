import sys

import os

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "../../")))
print(sys.path)

from api.lgsvl_client.lgsvl_connection import SimConnection
import lgsvl


with SimConnection(
    seconds=30, scene="environment_ShunYi", host="127.0.0.1"
) as sim_connection:
    # 表明是PDP
    sim_connection.exec_ext_cmd("EXT_CTR:{}".format("PDP"), lgsvl.ObjectState())
    # 自车信息
    ego_state = lgsvl.AgentState()
    ego_state.transform.position = lgsvl.Vector(
        663.708940,
        0.0,
        -102.745669,
    )
    ego_state.transform.rotation = lgsvl.Vector(
        0.0,
        216.95,
        0.0,
    )
    ego = sim_connection.sim.add_agent("JL", lgsvl.AgentType.EGO, ego_state)
    print("连接rosbridge")
    ego.connect_bridge("127.0.0.1", 9090)
    # 环境
    sim_connection.exec_ext_cmd("EXT_ENV:{}".format("prod"), lgsvl.ObjectState())
    # 场景
    sim_connection.exec_ext_cmd(
        "EXT_CASE_ID:{}".format("LBP0000000200_7"),
        lgsvl.ObjectState(),
    )
    # 终点
    ego_dest_state = lgsvl.ObjectState()
    ego_dest_state.transform.position = lgsvl.Vector(
        252.992784,
        0,
        -101.390760,
    )
    sim_connection.exec_ext_cmd("EXT_DEST_POS", ego_dest_state)

    # 轨迹
    traj_data = None
    map_file = "/home/<USER>/workspace/code/engine/pdp_logsim/traj.csv"
    with open(map_file, "r") as f:
        traj_data = f.readlines()

    sim_connection.exec_trajectoy_cmd(traj_data)
    sim_connection.sim.run(1000, 1)

    import time

    time.sleep(40)
    sim_connection.exec_ext_cmd("EXT_API_MODE", lgsvl.ObjectState(), 2)
