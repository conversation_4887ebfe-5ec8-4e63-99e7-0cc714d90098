map:
  # local_planning: 'source /autocar-code/install/setup.bash && roslaunch local_planning_260 local_planning_260.launch'
  local_planning: 'source /autocar-code/install/setup.bash && rosparam set use_sim_time true && roslaunch local_planning local_planning.launch'
  # ai_planning: 'source /autocar-code/install/setup.bash && roslaunch ai_planning ai_planning.launch'
  # post_decision_pu: 'source /autocar-code/install/setup.bash && roslaunch post_decision_pu post_decision_pu.launch'
  # prediction: 'export DEVICE_PLATFORM=x && source /autocar-code/install/setup.bash && rosparam set use_sim_time true && roslaunch prediction prediction.launch'
  hadmap_server: 'source /autocar-code/install/setup.bash && rosparam set use_sim_time true && rosparam set db_file /home/<USER>/data/vehicle_monitor/hadmap_data/stop.txt && rosparam set traj_file /home/<USER>/data/vehicle_monitor/hadmap_data/traj.csv && roslaunch hadmap hadmap.launch'
  hadmap_engine: 'source /autocar-code/install/setup.bash && rosparam set use_sim_time true  && rosparam set /map/ready 1 && roslaunch hadmap_engine hadmap_engine.launch'
lgsvl:
  host: "localhost"
  port: 9090
  ext_case_id: "LBP0000000200_7"
  ego_dest:
    x: -5947.867448
    y: 0
    z: 900.735198
  traj_file: "traj.csv"
  scene: "environment_GuoZhan"
  ego_initial:
    position:
      x: -5637.621091
      y: 0.0
      z: 917.496757
    rotation:
      x: 0.0
      y: -93.12
      z: 0.0
rosbag:
  flow_control_config_path: "play_PDP.yaml"
  start_time: 0.0
  end_time: 0.0
  takeover: 0.0
  event: 0.0