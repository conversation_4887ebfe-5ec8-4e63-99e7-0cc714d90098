import json
from pathlib import Path
import os
from typing import Optional
import sys
import yaml
import rospy
from utils.loggers import LoggerProxy
from common_steps.stepbase import EngineStepBase
import threading
from api.pdpc_common.engine_config import EngineSimulationConfig
from api.pdpc_common.map_handler import MapHandler

from utils.path import (
    get_task_casefile_path,
    get_task_record_path,
    get_mount_path,
    get_tasks_info,
)

# 添加lgsvl_msg路径
sys.path = ["/opt/msim/api/lgsvl_client"] + sys.path

logger = LoggerProxy()

# 添加Proto路径
proto_path = os.path.join(get_mount_path(), "dist-packages")

if os.path.exists(proto_path):
    paths_to_add = [proto_path]
    for entry in os.scandir(proto_path):
        if entry.is_dir():
            paths_to_add.append(entry.path)
    sys.path = paths_to_add + sys.path

try:
    from common.object_pb2 import TrackedObjects
except Exception as e:
    logger.info(f"发生错误：{e}")

logger.info(f"当前python路径：{sys.path}")

from api.pdpc_common.lgsvl_handler import LgsvlHandler
from api.pdpc_common.rosbag_handler import RosbagHandler
from api.process.process_manager import ProcessManager


class Step(EngineStepBase):
    def _initialize_component(self, component_name: str, handler) -> bool:
        """辅助方法：初始化单个组件并记录。"""
        if not handler:
            logger.error(f"尝试初始化一个未实例化的组件处理器: {component_name}")
            return False
        try:
            logger.info(f"开始初始化 {component_name}...")
            if not handler.setup():
                logger.error(f"{component_name} 初始化 (setup) 失败。")
                return False
            self.initialized_components.append(handler)
            logger.info(f"{component_name} 初始化 (setup) 成功。")
            return True
        except Exception as e:
            logger.error(
                f"{component_name} 初始化 (setup) 过程中发生异常: {e}",
                exc_info=True,
            )
            return False

    def init(self) -> bool:
        logger.info(f"dp_logsim engine init 开始")

        # 从 tasks_info.json 文件中读取 manual 字段
        self.local = self._load_local_from_tasks_info()
        logger.info(f"从 tasks_info.json 中读取到 manual 值: {self.local}")

        self.sim_config = None
        self.map_handler = None
        self.lgsvl_handler = None
        self.rosbag_handler = None
        self.initialized_components = []
        self.process_manager = None
        self.lgsvl_thread = None
        try:
            # 根据本地模式判断是否获取容器ID
            if not self.local:
                self.container_id = self.get_algo_container_id()
                logger.info(f"容器模式 - algo_container_id: {self.container_id}")
            else:
                self.container_id = None
                logger.info("本地模式 - 跳过容器ID获取")

            self.case_id = self.get_case_id()
            self.case_path = get_task_casefile_path(self.case_id)
            logger.info(f"case_id: {self.case_id}")
            logger.info(f"case_path: {self.case_path}")
        except Exception as e:
            self.container_id = None
            self.case_id = None
            logger.warning(f"无容器ID或case_id！")

        try:
            logger.info("EngineStep: 开始初始化配置和处理器...")
            self.sim_config = EngineSimulationConfig()  # 实例化配置加载器

            # 实例化 ProcessManager
            self.process_manager = ProcessManager()
            logger.info("EngineStep: ProcessManager 实例化完成。")

            rosbag_specific_config = self.sim_config.get_rosbag_config()
            lgsvl_config = self.sim_config.get_lgsvl_config()

            # 使用case路径下的case_info.json更新配置
            case_info_path = get_case_info_file_path(self.case_path)
            if case_info_path:
                update_config(rosbag_specific_config, lgsvl_config, case_info_path)
            else:
                logger.warning(f"未找到case_info.json文件，将使用默认配置")

            flow_control_path = rosbag_specific_config.get(
                "flow_control_config_path", ""
            )
            # 如果flow_control_path是相对路径，则转换为绝对路径
            parent_dir = os.path.dirname(os.path.realpath(__file__))
            flow_control_path = os.path.join(parent_dir, flow_control_path)

            logger.info(f"flow_control_path使用：{flow_control_path}")

            actual_bag_path = get_bag_file_path(self.case_path)
            actual_start_time = rosbag_specific_config.get("start_time", 0.0)
            actual_end_time = rosbag_specific_config.get("end_time", 0.0)
            actual_record_path = os.path.join(
                get_task_record_path(self.case_id), "output.bag"
            )

            # 实例化各个处理器，并注入配置、logger 和 process_manager
            # MapHandler 仅在非本地模式下实例化
            if not self.local:
                self.map_handler = MapHandler(
                    config=self.sim_config.get_map_config(),
                    logger_instance=logger,
                    container_id=self.container_id,  # 传递获取到的容器ID
                    case_id=self.case_id,
                    case_path=self.case_path,  # 传递case路径用于查找hadmap数据库文件
                )
            else:
                logger.info("本地模式：跳过MapHandler实例化")

            # 读取shield_config从流程控制配置文件
            shield_config = load_shield_config_from_flow_control_file(flow_control_path)

            # 如果 update_config 已生成屏蔽时间参数，则优先使用该值覆盖 shield_config
            shield_time_ns = rosbag_specific_config.get("shield_time_ns")
            if shield_time_ns is not None:
                if not shield_config:
                    shield_config = {}
                shield_config["time_point_ns"] = shield_time_ns

            # 将shield_config合并到LGSVL配置中
            if shield_config:
                lgsvl_config["shield_config"] = shield_config

            # 实例化LGSVL处理器
            self.lgsvl_handler = LgsvlHandler(
                config=lgsvl_config,
                lgsvl_root="/opt/sim_lgsvl",
                case_id=self.case_id,
                case_path=self.case_path,  # 传递case路径用于查找traj.csv文件
            )

            # 实例化Rosbag处理器
            self.rosbag_handler = RosbagHandler(
                config=rosbag_specific_config,
                logger_instance=logger,
                flow_control_config_path=flow_control_path,
                bag_path=actual_bag_path,
                clock_freq=100,
                remap=[],
                start_time=actual_start_time,
                end_time=actual_end_time,
                bag_record_path=actual_record_path,
            )
            logger.info("EngineStep: 配置和处理器初始化完成。")
        except Exception as e:
            logger.error(f"EngineStep 在构造过程中发生严重错误: {e}", exc_info=True)

            self.construction_failed = True

        if hasattr(self, "construction_failed") and self.construction_failed:
            logger.error("EngineStep 构造失败，无法执行 init。")
            return False

        self.initialized_components.clear()

        # 0. MAP相关 (仅在非本地模式下初始化)
        if not self.local:
            if not self._initialize_component("MAP服务", self.map_handler):
                self.clean()
                logger.error(f"MAP服务初始化失败")
                return False
        else:
            logger.info("本地模式：跳过MAP服务初始化")

        # 1. LGSVL相关
        if not self._initialize_component("LGSVL服务", self.lgsvl_handler):
            self.clean()
            logger.error(f"LGSVL服务初始化失败")
            return False

        # 2. 播包相关 (准备阶段)
        if not self._initialize_component("播包服务", self.rosbag_handler):
            self.clean()
            logger.error(f"播包服务初始化失败")
            return False

        logger.info(f"dp_logsim engine init 全部成功完成")
        return True

    def run(self) -> bool:
        logger.info(f"dp_logsim engine run 开始")
        if hasattr(self, "construction_failed") and self.construction_failed:
            logger.error("EngineStep 构造失败，无法执行 run。")
            return False
        if not (self.rosbag_handler and self.rosbag_handler.initialized_successfully):
            logger.error("RosbagHandler 未成功初始化，无法执行 run。")
            return False
        if not (self.lgsvl_handler and self.lgsvl_handler.initialized_successfully):
            logger.error("LgsvlHandler 未成功初始化，无法执行 run。")
            # return False

        try:
            # 启动LGSVL仿真任务（并行线程）
            logger.info(f"准备启动LGSVL仿真线程...")
            self.lgsvl_thread = threading.Thread(
                target=self.lgsvl_handler.lgsvl_run, daemon=True
            )
            self.lgsvl_thread.start()
            logger.info(f"LGSVL仿真线程已启动")

            logger.info(f"准备启动播包进程...")
            if not self.rosbag_handler.start_playback():
                logger.error(f"启动播包进程失败。")
                return False
            logger.info(f"播包完毕。")

            # 检查lgsvl线程是否还在运行
            if self.lgsvl_thread.is_alive():
                logger.info("等待LGSVL仿真线程完成...")
                self.lgsvl_thread.join(timeout=5)
                if self.lgsvl_thread.is_alive():
                    logger.warning("LGSVL仿真线程未在规定时间内完成")
                else:
                    logger.info("LGSVL仿真线程已正常结束。")
            else:
                logger.info("LGSVL仿真线程已正常退出。")

            return True

        except Exception as e:
            logger.error(
                f"dp_logsim engine run 过程中发生异常: {e}",
                exc_info=True,
            )
            if self.rosbag_handler:
                self.rosbag_handler.stop_simulation_tasks()  # 尝试停止
            # 等待LGSVL线程结束
            if (
                hasattr(self, "lgsvl_thread")
                and self.lgsvl_thread
                and self.lgsvl_thread.is_alive()
            ):
                logger.warning("异常情况下等待LGSVL线程结束...")
                self.lgsvl_thread.join(timeout=5)
                if self.lgsvl_thread.is_alive():
                    logger.warning(
                        "LGSVL线程未在规定时间内结束（线程将作为daemon自动清理）"
                    )
            return False

    def end(self) -> bool:
        logger.info(f"dp_logsim engine end 开始")
        if hasattr(self, "construction_failed") and self.construction_failed:
            logger.error("EngineStep 构造失败，跳过 end。")
            return True  # 通常end和clean即使构造失败也应尝试执行或安全退出

        logger.info(f"dp_logsim engine end 完成")
        return True

    def clean(self) -> bool:
        logger.info(f"dp_logsim engine clean 开始")
        final_success = True

        # 添加调试信息：打印 initialized_components 的内容
        logger.info(
            f"[DEBUG] initialized_components 包含 {len(self.initialized_components)} 个组件:"
        )
        for i, component in enumerate(self.initialized_components):
            component_name = getattr(
                component, "component_name", component.__class__.__name__
            )
            logger.info(f"[DEBUG] 组件 {i}: {component_name}")

        # 确保即使构造部分失败，也尝试清理已创建的handler (如果它们存在)
        components_to_clean = self.initialized_components
        if not components_to_clean and not (
            hasattr(self, "construction_failed") and self.construction_failed
        ):
            logger.info(
                "initialized_components 为空，尝试按固定顺序清理已实例化的handlers..."
            )
            potential_handlers = [
                self.rosbag_handler,
                self.lgsvl_handler,
            ]
            # 仅在非本地模式下添加map_handler
            if not self.local:
                potential_handlers.append(self.map_handler)
            components_to_clean = [h for h in potential_handlers if h is not None]
            logger.info(
                f"[DEBUG] 使用固定顺序，找到 {len(components_to_clean)} 个可清理的组件"
            )
        elif (
            not components_to_clean
            and hasattr(self, "construction_failed")
            and self.construction_failed
        ):
            logger.info(
                "EngineStep 构造失败，且 initialized_components 为空，尝试清理可能已实例化的handlers..."
            )
            # 收集可能已实例化的handlers，顺序应与它们在构造函数中被创建的顺序一致
            # LGSVL和Rosbag可能依赖MapHandler的某些状态（虽然此处是独立创建）
            # 但清理顺序通常是初始化的逆序。这里我们收集它们，然后外层循环会 reversed()
            potential_handlers_on_constr_fail = []
            # 仅在非本地模式下添加map_handler到清理列表
            if not self.local and hasattr(self, "map_handler") and self.map_handler:
                potential_handlers_on_constr_fail.append(self.map_handler)

            if hasattr(self, "lgsvl_handler") and self.lgsvl_handler:
                potential_handlers_on_constr_fail.append(self.lgsvl_handler)

            if hasattr(self, "rosbag_handler") and self.rosbag_handler:
                potential_handlers_on_constr_fail.append(self.rosbag_handler)

            components_to_clean = potential_handlers_on_constr_fail  # 直接赋值列表
            logger.info(
                f"[DEBUG] 构造失败情况下，找到 {len(components_to_clean)} 个可清理的组件"
            )

        logger.info(
            f"[DEBUG] 准备清理 {len(components_to_clean)} 个组件，清理顺序（逆序）:"
        )
        for i, component in enumerate(reversed(components_to_clean)):
            if component is not None:
                component_name = getattr(
                    component, "component_name", component.__class__.__name__
                )
                logger.info(f"[DEBUG] 清理序号 {i}: {component_name}")

        # 改进的清理循环 - 增加超时机制和错误隔离
        for component_handler in reversed(components_to_clean):  # reversed()作用于列表
            if component_handler is None:
                continue
            component_name = getattr(
                component_handler,
                "component_name",
                component_handler.__class__.__name__,
            )
            try:
                logger.info(f"开始清理 {component_name}...")

                # 为每个组件的清理设置超时机制
                import signal
                import threading

                cleanup_result = [False]  # 使用列表以便在内部函数中修改
                cleanup_exception = [None]

                def cleanup_with_timeout():
                    try:
                        cleanup_result[0] = component_handler.cleanup()
                    except Exception as e:
                        cleanup_exception[0] = e

                # 使用线程执行清理，设置超时
                cleanup_thread = threading.Thread(
                    target=cleanup_with_timeout, daemon=True
                )
                cleanup_thread.start()
                cleanup_thread.join(timeout=60)  # 60秒超时

                if cleanup_thread.is_alive():
                    logger.error(f"{component_name} 清理超时（60秒），强制跳过该组件")
                    final_success = False
                elif cleanup_exception[0] is not None:
                    raise cleanup_exception[0]  # 重新抛出在线程中捕获的异常
                elif not cleanup_result[0]:
                    logger.warning(f"{component_name} 清理未完全成功。")
                    final_success = False
                else:
                    logger.info(f"{component_name} 清理完成。")

            except Exception as e:
                logger.error(
                    f"{component_name} 清理过程中发生异常: {e}",
                    exc_info=True,
                )
                final_success = False
                # 继续处理下一个组件，不要中断循环

        self.initialized_components.clear()

        # 清理 ProcessManager
        if self.process_manager:
            try:
                logger.info(f"开始清理 ProcessManager...")
                self.process_manager.destroy()
                logger.info(f"ProcessManager 清理完成。")
            except Exception as e:
                logger.error(
                    f"ProcessManager 清理过程中发生异常: {e}",
                    exc_info=True,
                )
                final_success = (
                    False  # 如果ProcessManager清理失败，也标记为整体清理失败
                )

        if final_success:
            logger.info(f"dp_logsim engine clean 所有组件清理完成。")
        else:
            logger.warning(f"dp_logsim engine clean 部分组件清理时遇到问题。")
        return final_success

    def _load_local_from_tasks_info(self) -> bool:
        """
        从 tasks_info.json 文件中读取 manual 字段值

        返回:
            bool: manual 字段的值，如果读取失败则返回 False
        """
        try:
            tasks_info_path = get_tasks_info()
            logger.info(f"尝试从 {tasks_info_path} 读取 manual 字段")

            if not os.path.exists(tasks_info_path):
                logger.warning(f"tasks_info.json 文件不存在: {tasks_info_path}")
                return False

            with open(tasks_info_path, "r", encoding="utf-8") as f:
                tasks_data = json.load(f)

            local_value = tasks_data.get("manual", "false")

            # 处理字符串形式的布尔值
            if isinstance(local_value, str):
                return local_value.lower() == "true"
            elif isinstance(local_value, bool):
                return local_value
            else:
                logger.warning(
                    f"manual 字段值格式异常: {local_value}, 类型: {type(local_value)}"
                )
                return False

        except json.JSONDecodeError as e:
            logger.error(f"解析 tasks_info.json 文件失败: {e}")
            return False
        except Exception as e:
            logger.error(f"读取 tasks_info.json 时发生错误: {e}", exc_info=True)
            return False


def get_bag_file_path(search_path: str) -> Optional[str]:
    """
    在指定路径及其子目录下递归查找唯一的.bag文件

    参数:
        search_path (str): 要搜索的目录路径

    返回:
        Optional[str]: 找到的.bag文件路径；如果找到多个或未找到则返回None

    异常:
        ValueError: 如果输入路径不存在或不是目录
    """
    # 1. 路径验证（保持不变）
    path_obj = Path(search_path)
    if not path_obj.exists():
        raise ValueError(f"路径不存在: {search_path}")
    if not path_obj.is_dir():
        raise ValueError(f"路径不是目录: {search_path}")

    # 2. 使用rglob递归查找
    bag_files = [str(file) for file in path_obj.rglob("*.bag")]

    # 3. 文件数量检查（逻辑保持）
    if len(bag_files) == 1:
        return bag_files[0]
    elif len(bag_files) > 1:
        logger.info(f"警告: 找到多个.bag文件: {bag_files}")
    else:
        logger.info("警告: 未找到.bag文件")
    return None


def get_case_info_file_path(search_path: str) -> Optional[str]:
    """
    在指定路径及其子目录下递归查找唯一的case_info.json文件

    参数:
        search_path (str): 要搜索的目录路径

    返回:
        Optional[str]: 找到的case_info.json文件路径；如果找到多个或未找到则返回None

    异常:
        ValueError: 如果输入路径不存在或不是目录
    """
    # 1. 路径验证
    path_obj = Path(search_path)
    if not path_obj.exists():
        raise ValueError(f"路径不存在: {search_path}")
    if not path_obj.is_dir():
        raise ValueError(f"路径不是目录: {search_path}")

    # 2. 使用rglob递归查找case_info.json文件
    case_info_files = [str(file) for file in path_obj.rglob("case_info.json")]

    # 3. 文件数量检查
    if len(case_info_files) == 1:
        return case_info_files[0]
    elif len(case_info_files) > 1:
        logger.info(f"警告: 找到多个case_info.json文件: {case_info_files}")
    else:
        logger.info("警告: 未找到case_info.json文件")
    return None


def load_shield_config_from_flow_control_file(flow_control_path: str) -> dict:
    """
    从流程控制配置文件中读取shield_config

    参数:
        flow_control_path (str): 流程控制配置文件路径

    返回:
        dict: shield_config配置，如果读取失败则返回空字典
    """
    if not flow_control_path or not os.path.exists(flow_control_path):
        logger.warning(f"流程控制配置文件不存在: {flow_control_path}")
        return {}

    try:
        with open(flow_control_path, "r", encoding="utf-8") as f:
            flow_control_config = yaml.safe_load(f)

        shield_config = flow_control_config.get("shield_config", {})
        if shield_config:
            logger.info(
                f"从配置文件 {flow_control_path} 读取到shield_config: {shield_config}"
            )
        else:
            logger.info(f"配置文件 {flow_control_path} 中未找到shield_config")

        return shield_config

    except Exception as e:
        logger.error(f"读取流程控制配置文件失败: {e}", exc_info=True)
        return {}


def update_config(rosbag_config: dict, lgsvl_config: dict, case_json_path: str):
    """
    读取指定的case JSON文件(例如 case_info_demo.json)然后更新传入的configs。
    - rosbag_config: 会使用 'case_time' 数据更新
    - lgsvl_config: 会使用 'case_info' 和 'case_id' 数据更新

    参数:
        rosbag_config (dict): 需要更新的rosbag配置字典
        lgsvl_config (dict): 需要更新的lgsvl配置字典
        case_json_path (str): case json文件的路径

    返回:
        bool: 是否成功更新配置
    """
    if not case_json_path or not os.path.exists(case_json_path):
        logger.warning(f"case_json_path不存在: {case_json_path}")
        return False

    try:
        logger.info(f"开始从 {case_json_path} 读取配置...")
        # 读取和解析JSON文件
        with open(case_json_path, "r", encoding="utf-8") as f:
            case_data = json.load(f)

        # 1. 更新 rosbag_config (使用 'case_time' 子字典)
        time_data = case_data.get("case_time")
        if time_data and isinstance(time_data, dict):
            if "start_time" in time_data:
                rosbag_config["start_time"] = time_data["start_time"]
                logger.info(f"更新rosbag start_time: {time_data['start_time']}")

            if "end_time" in time_data:
                rosbag_config["end_time"] = time_data["end_time"]
                logger.info(f"更新rosbag end_time: {time_data['end_time']}")

            if "takeover" in time_data:
                rosbag_config["takeover"] = time_data["takeover"]
                logger.info(f"更新rosbag takeover: {time_data['takeover']}")

            if "event" in time_data:
                rosbag_config["event"] = time_data["event"]
                logger.info(f"更新rosbag event: {time_data['event']}")

            # 生成屏蔽时间参数 (以 'takeover' 字段为准) 并直接写入配置
            if "takeover" in time_data and time_data["takeover"] > 0:
                shield_time_ns = int(time_data["takeover"] * 1e9)
                rosbag_config["shield_time_ns"] = shield_time_ns
                logger.info(f"设置屏蔽时间(shield_time_ns): {shield_time_ns} ns")
        else:
            logger.warning(f"在 {case_json_path} 中未找到 'case_time' 对象或格式不正确")

        # 2. 更新 lgsvl_config
        # 从 'case_info' 更新
        case_info_data = case_data.get("case_info")
        if case_info_data and isinstance(case_info_data, dict):
            lgsvl_config.update(case_info_data)
            logger.info(
                f"使用 case_info 中的数据更新 lgsvl_config: {list(case_info_data.keys())}"
            )
        else:
            logger.warning(f"在 {case_json_path} 中未找到 'case_info' 对象或格式不正确")

        # 从根级别更新 'ext_case_id'
        if "case_id" in case_data:
            lgsvl_config["ext_case_id"] = case_data["case_id"]
            logger.info(f"更新 lgsvl_config 的 ext_case_id 为: {case_data['case_id']}")

        logger.info(f"成功从 {case_json_path} 更新配置")
        return True

    except json.JSONDecodeError as e:
        logger.error(f"解析 {case_json_path} 文件失败: {e}")
        return False
    except Exception as e:
        logger.error(f"更新配置时发生错误: {e}", exc_info=True)
        return False


if __name__ == "__main__":
    step = Step()
    # 确保 EngineStep 构造成功
    if hasattr(step, "construction_failed") and step.construction_failed:
        logger.error("EngineStep 构造失败，无法启动仿真流程。")
        # 即使构造失败，也尝试调用clean
        if hasattr(step, "clean") and callable(step.clean):
            logger.info("尝试执行构造失败后的清理...")
            clean_success = step.clean()
            logger.info(f"构造失败后的 clean() 返回: {clean_success}")
    else:
        logger.info("开始执行仿真步骤...")

        init_success = step.init()
        logger.info(f"init() 返回: {init_success}")

        if init_success:
            run_success = step.run()
            logger.info(f"run() 返回: {run_success}")

            end_success = step.end()  # end() 通常在run()之后调用，无论run是否成功
            logger.info(f"end() 返回: {end_success}")
        else:
            logger.error("init() 失败，跳过 run() 和 end()")

        clean_success = step.clean()  # clean() 总是应该被调用
        logger.info(f"clean() 返回: {clean_success}")

        logger.info("仿真步骤执行完毕。")
        # 打印出所有还在执行的子线程和子进程
        import threading
        import psutil

        # 打印当前进程的所有子进程
        current_process = psutil.Process()
        children = current_process.children(recursive=True)
        if children:
            logger.warning(f"检测到 {len(children)} 个子进程仍在运行:")
            for child in children:
                logger.warning(
                    f"- 进程ID: {child.pid}, 名称: {child.name()}, 状态: {child.status()}"
                )
        else:
            logger.info("没有检测到正在运行的子进程")

        # 打印所有非守护线程
        active_threads = threading.enumerate()
        non_daemon_threads = [t for t in active_threads if not t.daemon]
        if len(non_daemon_threads) > 1:  # 主线程总是存在的
            logger.warning(f"检测到 {len(non_daemon_threads)-1} 个非守护线程仍在运行:")
            for thread in non_daemon_threads:
                if thread != threading.main_thread():
                    logger.warning(
                        f"- 线程名称: {thread.name}, 是否存活: {thread.is_alive()}"
                    )
        else:
            logger.info("没有检测到正在运行的非守护线程")
