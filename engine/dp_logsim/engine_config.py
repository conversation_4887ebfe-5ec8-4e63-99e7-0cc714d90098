#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from utils.loggers import LoggerProxy

logger = LoggerProxy()
import yaml
import os


class EngineSimulationConfig:
    """
    负责加载和管理所有仿真所需的配置。
    """

    def __init__(self):
        self.main_config_data = self._load_main_config_file()
        logger.info("EngineSimulationConfig 初始化完成。")

    def _load_main_config_file(self) -> dict:
        """
        加载主仿真配置文件 (pdp_logsim_config.yaml)。
        """
        logger.info("开始加载主仿真配置文件...")
        try:
            config_file_name = "pdp_logsim_config.yaml"
            current_script_directory = os.path.dirname(__file__)
            config_path = os.path.join(current_script_directory, config_file_name)

            if not os.path.exists(config_path):
                # 检查旧的示例配置文件路径以提供迁移提示
                old_config_path_for_warning = os.path.join(
                    current_script_directory,
                    "example_configs",
                    "pdp_logsim_main_example.yaml",
                )
                if os.path.exists(old_config_path_for_warning):
                    logger.warning(
                        f"主配置文件在预期路径 {config_path} 未找到，但在旧路径 {old_config_path_for_warning} 找到。请将配置文件移至 {config_path}。"
                    )

                logger.error(f"主配置文件未找到: {config_path}")
                raise FileNotFoundError(f"主配置文件未找到: {config_path}")

            with open(config_path, "r") as f:
                config_data = yaml.safe_load(f)
            logger.info(f"主配置文件加载成功: {config_path}")
            return config_data if config_data else {}
        except Exception as e:
            logger.error(f"加载主配置文件失败: {e}", exc_info=True)
            raise

    def get_map_config(self) -> dict:
        """
        获取MAP配置。
        所有MAP相关配置均从主配置文件 (pdp_logsim_config.yaml) 中的 "map" 部分加载。
        """
        return self.main_config_data.get("map", {})

    def get_lgsvl_config(self) -> dict:
        """获取LGSVL相关配置。"""
        return self.main_config_data.get("lgsvl", {})

    def get_rosbag_config(self) -> dict:
        """获取播包相关配置。"""
        return self.main_config_data.get("rosbag", {})


if __name__ == "__main__":
    logger.info("测试 EngineSimulationConfig...")
    try:
        config = EngineSimulationConfig()
        logger.info(f"MAP Config: {config.get_map_config()}")
        logger.info(f"LGSVL Config: {config.get_lgsvl_config()}")
        logger.info(f"Rosbag Config: {config.get_rosbag_config()}")
    except Exception as e:
        logger.error(f"EngineSimulationConfig 测试失败: {e}", exc_info=True)
