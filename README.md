# Mogosim Engine

## 环境要求
- Ubuntu 20.04+
- Docker
- NVIDIA GPU 驱动
- NVIDIA Container Toolkit

## 快速开始

### 构建镜像

```bash
# 一次性构建三个debug镜像
cd docker_build
./build_all_debug.sh

# 构建控制器镜像
cd docker_build/controller
./build.sh -d  # debug版本
./build.sh     # release版本

# 构建引擎镜像
cd docker_build/engine
./build.sh ros1 -d  # ROS1 debug版本
./build.sh ros1     # ROS1 release版本
./build.sh ros2 -d  # ROS2 debug版本
./build.sh ros2     # ROS2 release版本

# 构建评价镜像
cd docker_build/validator
./build.sh -d  # debug版本
./build.sh     # release版本
```

### 运行容器

```bash
# 启动调试环境
cd controller/run
./debug_run_docker.sh

# 进入容器
docker exec -it controller bash  # 控制器
docker exec -it engine bash     # 引擎
docker exec -it validator bash  # 评价器
```

## 目录结构
```
mogosim_engine/
├── controller/           # 控制器代码
├── engine/              # 引擎代码
├── validator/           # 评价器代码
├── docker_build/        # Docker构建配置
├── README.md
└── Q&A.md              # 常见问题解答
```

## 注意事项
1. 确保有Docker执行权限
2. 使用GPU功能需安装NVIDIA Container Toolkit
3. debug模式使用固定标签：controller/engine/validator
4. release模式使用带commit-id的标签

