#!/usr/bin/env python3
# coding=utf-8

"""
单文件上传脚本
================

该脚本用于将本地文件上传至腾讯云 COS 指定存储桶的指定路径下。
使用方式::

    python upload_to_cos.py /path/to/local/file.txt your-bucket-name remote/dir \
        --secret-id AKIDxxx --secret-key SKIDxxx --region ap-shanghai

若未在命令行提供 `--secret-id`、`--secret-key`、`--region`，则脚本会尝试读取环境变量
`COS_SECRET_ID`、`COS_SECRET_KEY`、`COS_REGION`。

依赖安装::

    pip install qcloud_cos==5.6.18  # 请根据实际需要调整版本

"""

import argparse
import logging
import os
import sys
from typing import Any, Dict

from qcloud_cos import CosConfig, CosS3Client

# ---------------------------- 配置日志 ----------------------------- #
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# ---------------------------- 核心函数 ----------------------------- #


def init_cos_client(secret_id: str, secret_key: str, region: str) -> CosS3Client:
    """初始化并返回 COS 客户端。

    参数:
        secret_id (str): 腾讯云秘钥 ID。
        secret_key (str): 腾讯云秘钥 Key。
        region (str): 存储桶所在地域，例如 ``ap-shanghai``。

    返回:
        CosS3Client: 已初始化的 COS 客户端实例。
    """
    config = CosConfig(Region=region, SecretId=secret_id, SecretKey=secret_key)
    return CosS3Client(config)


def upload_file(
    client: CosS3Client,
    bucket_name: str,
    local_file_path: str,
    bucket_path: str,
) -> Dict[str, Any]:
    """上传文件至 COS。

    参数:
        client (CosS3Client): 已初始化的 COS 客户端。
        bucket_name (str): 目标存储桶名称。
        local_file_path (str): 本地文件完整路径。
        bucket_path (str): COS 端目标目录。例如 ``logs/2025/``。

    返回:
        Dict[str, Any]: COS SDK 返回的响应。
    """
    if not os.path.isfile(local_file_path):
        raise FileNotFoundError(f"本地文件不存在: {local_file_path}")

    # 仅保留文件名，避免目录注入
    filename = os.path.basename(local_file_path)
    # 拼接最终对象 Key，并确保使用正斜杠
    object_key = os.path.join(bucket_path, filename).replace(os.sep, "/")

    logger.info("开始上传: %s -> cos://%s/%s", local_file_path, bucket_name, object_key)

    response = client.upload_file(
        Bucket=bucket_name,
        LocalFilePath=local_file_path,
        Key=object_key,
    )

    logger.info("上传完成，ETag: %s", response.get("ETag"))
    return response


# ---------------------------- CLI 入口 ------------------------------ #


def parse_args() -> argparse.Namespace:
    """解析命令行参数。"""
    parser = argparse.ArgumentParser(description="将本地文件上传至腾讯云 COS 指定路径")
    parser.add_argument("local_file", help="本地文件路径")
    parser.add_argument("bucket_name", help="COS 存储桶名称")
    parser.add_argument("bucket_path", help="COS 端目标目录（例如 logs/2025）")
    parser.add_argument(
        "--secret-id",
        dest="secret_id",
        default=os.getenv("COS_SECRET_ID"),
        help="腾讯云 SecretId，可通过环境变量 COS_SECRET_ID 指定",
    )
    parser.add_argument(
        "--secret-key",
        dest="secret_key",
        default=os.getenv("COS_SECRET_KEY"),
        help="腾讯云 SecretKey，可通过环境变量 COS_SECRET_KEY 指定",
    )
    parser.add_argument(
        "--region",
        dest="region",
        default=os.getenv("COS_REGION", "ap-beijing"),
        help="COS 区域，默认 ap-beijing，可通过环境变量 COS_REGION 指定",
    )
    return parser.parse_args()


def main() -> None:
    """脚本主函数。"""
    args = parse_args()

    if not args.secret_id or not args.secret_key:
        logger.error(
            "缺少 COS 访问密钥，请通过参数或环境变量提供 --secret-id 与 --secret-key"
        )
        sys.exit(1)

    client = init_cos_client(args.secret_id, args.secret_key, args.region)

    try:
        upload_file(client, args.bucket_name, args.local_file, args.bucket_path)
        logger.info("文件上传成功！")
    except Exception as exc:
        logger.exception("文件上传失败: %s", exc)
        sys.exit(1)


if __name__ == "__main__":
    main()
