#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import argparse
import os
import math
import json
import rosbag
from rosbag import Bag
from google.protobuf.message import DecodeError

# 假设 my_proto 目录与此脚本在同一路径下或在 PYTHONPATH 中
try:
    from common.localization_pb2 import Localization
    from common.trajectory_pb2 import Trajectory
    from common.hadmap_pb2 import MapMsg
except ImportError:
    import sys

    sys.path.append("/home/<USER>/workspace/mount/dist-packages")
    sys.path.append("/home/<USER>/workspace/mount/dist-packages/common")
    from common.localization_pb2 import Localization
    from common.trajectory_pb2 import Trajectory
    from common.hadmap_pb2 import MapMsg
# 全局变量，用于存储从bag文件中解析出来的数据
ego_info_list = []  # 存储自车位姿信息
trajectory_info_list = []  # 存储轨迹点信息
stop_info_list = []  # 存储停车标志和速度限制信息


def clear_global_data():
    """
    清空全局数据列表，用于处理新的bag文件时避免数据混合。
    """
    global ego_info_list, trajectory_info_list, stop_info_list
    ego_info_list.clear()
    trajectory_info_list.clear()
    stop_info_list.clear()


def merge_bags_in_folder(folder_path, output_bag_name=None, verbose=False):
    """
    合并指定文件夹中的所有bag文件到一个输出文件，合并成功后删除原始bag文件。

    参数:
        folder_path (str): 包含bag文件的文件夹路径
        output_bag_name (str): 输出的合并bag文件名，如果为None则自动生成
        verbose (bool): 是否显示详细信息

    返回:
        str: 合并后的bag文件路径，失败返回None
    """
    try:
        # 验证文件夹是否存在
        if not os.path.exists(folder_path):
            raise ValueError(f"输入目录不存在: {folder_path}")

        if not os.path.isdir(folder_path):
            raise ValueError(f"输入路径不是目录: {folder_path}")

        # 查找所有bag文件
        bag_files = [f for f in os.listdir(folder_path) if f.endswith(".bag")]
        if not bag_files:
            raise ValueError(f"输入目录中没有找到.bag文件: {folder_path}")

        if len(bag_files) == 1:
            print(f"文件夹中只有一个bag文件: {bag_files[0]}，无需合并")
            return os.path.join(folder_path, bag_files[0])

        print(f"找到 {len(bag_files)} 个bag文件需要合并")

        # 生成输出文件名
        if not output_bag_name:
            dir_name = os.path.basename(os.path.normpath(folder_path))
            output_bag_name = f"{dir_name}_merged.bag"
            print(f"未指定输出文件名，使用默认名称: {output_bag_name}")

        output_bag_path = os.path.join(folder_path, output_bag_name)

        # 执行合并操作
        total_included_count = 0
        print(f"开始合并 {len(bag_files)} 个bag文件")

        with Bag(output_bag_path, "w") as output_bag_writer:
            for i, bag_file in enumerate(bag_files, 1):
                if verbose:
                    print(f"正在处理第 {i}/{len(bag_files)} 个文件: {bag_file}")

                bag_path = os.path.join(folder_path, bag_file)
                matched_topics = []
                included_count = 0

                try:
                    with Bag(bag_path, "r") as input_bag_reader:
                        for topic, msg, timestamp in input_bag_reader:
                            if topic not in matched_topics:
                                matched_topics.append(topic)
                            output_bag_writer.write(topic, msg, timestamp)
                            included_count += 1

                    total_included_count += included_count
                    if verbose:
                        print(
                            f"  - 处理了 {included_count} 条消息，涉及 {len(matched_topics)} 个topic"
                        )

                except Exception as e:
                    print(f"错误: 处理文件 {bag_file} 时出错: {e}")
                    continue

        print(f"合并完成! 总共处理了 {total_included_count} 条消息")
        print(f"输出文件: {output_bag_path}")

        # 删除合并前的bag文件
        print("删除合并前的bag文件")
        for bag_file in bag_files:
            bag_path = os.path.join(folder_path, bag_file)
            os.remove(bag_path)
            if verbose:
                print(f"  - 已删除: {bag_file}")

        return output_bag_path

    except Exception as e:
        print(f"错误: 合包失败: {e}")
        return None


# 区域配置信息，原 zone.json 内容
ZONES = [
    {
        "Name": "environment_ShunYi",
        "mapname": "shunyi",
        "UtmZoneId": 50,
        "Origin_East": 475817.965,
        "Origin_North": 4446671.6295,
        "Left_East": 474870,
        "Left_North": 4445808,
        "Right_East": 479675,
        "Right_North": 4451464,
    },
    {
        "Name": "environment_GuoZhan",
        "mapname": "guozhan",
        "UtmZoneId": 50,
        "Origin_East": 459756.7004,
        "Origin_North": 4432502.0874,
        "Left_East": 453478,
        "Left_North": 4432413,
        "Right_East": 465710,
        "Right_North": 4441984,
    },
    {
        "Name": "environment_YiZhuang",
        "mapname": "yizhuang",
        "UtmZoneId": 50,
        "Origin_East": 458897.87,
        "Origin_North": 4402632.41,
        "Left_East": 454114,
        "Left_North": 4395278,
        "Right_East": 465502,
        "Right_North": 4407837,
    },
    {
        "Name": "environment_DaXingLicensePlate",
        "mapname": "daxinglicenseplate",
        "UtmZoneId": 50,
        "Origin_East": 453375.8214,
        "Origin_North": 4398686.6208,
        "Left_East": 453185,
        "Left_North": 4398567,
        "Right_East": 453810,
        "Right_North": 4399426,
    },
    {
        "Name": "environment_TongShunLu",
        "mapname": "tongshunlu",
        "UtmZoneId": 50,
        "Origin_East": 470530,
        "Origin_North": 4446630,
        "Left_East": 465216,
        "Left_North": 4441300,
        "Right_East": 476105,
        "Right_North": 4452244,
    },
    {
        "Name": "environment_DaYunHeSenLinGongYuan",
        "mapname": "dayunhesenlingongyuan",
        "UtmZoneId": 50,
        "Origin_East": 475000,
        "Origin_North": 4416400,
        "Left_East": 472250,
        "Left_North": 4411808,
        "Right_East": 480000,
        "Right_North": 4419300,
    },
    {
        "Name": "environment_ShangHai",
        "mapname": "shanghai",
        "UtmZoneId": 51,
        "Origin_East": 343152.367,
        "Origin_North": 3455742.062,
        "Left_East": 311696,
        "Left_North": 3406950,
        "Right_East": 410495,
        "Right_North": 3483870,
    },
    {
        "Name": "environment_HuZhou",
        "mapname": "huzhou",
        "UtmZoneId": 51,
        "Origin_East": 213642.9192,
        "Origin_North": 3379657.2215,
        "Left_East": 212773,
        "Left_North": 3377986,
        "Right_East": 216695,
        "Right_North": 3381014,
    },
    {
        "Name": "environment_HengYang",
        "mapname": "hengyang",
        "UtmZoneId": 49,
        "Origin_East": 655369.5100,
        "Origin_North": 2965689.3947,
        "Left_East": 652823.1843,
        "Left_North": 2962728.7885,
        "Right_East": 666781.5120,
        "Right_North": 2972683.2766,
    },
    {
        "Name": "environment_HengYang1",
        "mapname": "hengyang1",
        "UtmZoneId": 49,
        "Origin_East": 655936.4148,
        "Origin_North": 2970977.9668,
        "Left_East": 648805.4139,
        "Left_North": 2969938.9677,
        "Right_East": 658679.4161,
        "Right_North": 2981262.9677,
    },
    {
        "Name": "environment_HengYang2",
        "mapname": "hengyang2",
        "UtmZoneId": 49,
        "Origin_East": 661742.2959,
        "Origin_North": 2960092.8443,
        "Left_East": 657173.7217,
        "Left_North": 2951779.4235,
        "Right_East": 663325.5092,
        "Right_North": 2969799.8824,
    },
    {
        "Name": "environment_HengYang3",
        "mapname": "hengyang3",
        "UtmZoneId": 49,
        "Origin_East": 663487.7356,
        "Origin_North": 2975315.4375,
        "Left_East": 657983.7346,
        "Left_North": 2972475.4380,
        "Right_East": 670579.7371,
        "Right_North": 2978906.4365,
    },
    {
        "Name": "environment_JiaXing",
        "mapname": "jiaxing",
        "UtmZoneId": 51,
        "Origin_East": 264963.55,
        "Origin_North": 3395912.6,
        "Left_East": 252936.8303,
        "Left_North": 3365805.6156,
        "Right_East": 276284.8928,
        "Right_North": 3427891.2445,
    },
    {
        "Name": "environment_Tianjin",
        "mapname": "tianjin",
        "UtmZoneId": 50,
        "Origin_East": 504211.6185,
        "Origin_North": 4322636.6207,
        "Left_East": 494239.8714,
        "Left_North": 4314102.0738,
        "Right_East": 514125.6986,
        "Right_North": 4333966.7076,
    },
    {
        "Name": "environment_ErHai",
        "mapname": "erhai",
        "UtmZoneId": 47,
        "Origin_East": 622841.1852,
        "Origin_North": 2834412.5557,
        "Left_East": 609469.1832,
        "Left_North": 2829064.5577,
        "Right_East": 624234.1892,
        "Right_North": 2867029.5596,
    },
    {
        "Name": "environment_YanTai",
        "mapname": "yantai",
        "UtmZoneId": 51,
        "Origin_East": 376899.0475,
        "Origin_North": 4145850.2253,
        "Left_East": 373868.0470,
        "Left_North": 4143361.2255,
        "Right_East": 380758.0485,
        "Right_North": 4150138.2253,
    },
]

# environment到城市的映射表
ENVIRONMENT_TO_CITY = {
    "environment_ShunYi": "北京",
    "environment_GuoZhan": "北京",
    "environment_YiZhuang": "北京",
    "environment_DaXingLicensePlate": "北京",
    "environment_TongShunLu": "北京",
    "environment_DaYunHeSenLinGongYuan": "北京",
    "environment_ShangHai": "上海",
    "environment_HuZhou": "湖州",  # 注意：湖州不在原始表格中，使用默认值
    "environment_HengYang": "衡阳",
    "environment_HengYang1": "衡阳",
    "environment_HengYang2": "衡阳",
    "environment_HengYang3": "衡阳",
    "environment_JiaXing": "嘉兴",
    "environment_Tianjin": "天津",
    "environment_ErHai": "洱海",
    "environment_YanTai": "烟台",
}

# 城市到sqlite文件的映射表
CITY_TO_SQLITE = {
    "北京": "https://autocar-mogosim-**********.cos.ap-beijing.myqcloud.com/hadmap/bj-V2.9.8.sqlite",
    "衡阳": "https://autocar-mogosim-**********.cos.ap-beijing.myqcloud.com/hadmap/hy-v4.1.6.sqlite",
    "洱海": "https://autocar-mogosim-**********.cos.ap-beijing.myqcloud.com/hadmap/erh_V1.6.9.sqlite",
    "烟台": "https://autocar-mogosim-**********.cos.ap-beijing.myqcloud.com/hadmap/yant_V1.4.8.sqlite",
    "天津": "https://autocar-mogosim-**********.cos.ap-beijing.myqcloud.com/hadmap/tianjin_V1.2.3.sqlite",
    "嘉兴": "https://autocar-mogosim-**********.cos.ap-beijing.myqcloud.com/hadmap/jx_1.0.5.sqlite",
    "上海": "https://autocar-mogosim-**********.cos.ap-beijing.myqcloud.com/hadmap/sh-V1.2.6.sqlite",
    "湖州": "https://autocar-mogosim-**********.cos.ap-beijing.myqcloud.com/hadmap/hy_v4.2.5.sqlite",
}


def get_sqlite_url_by_scene(scene):
    """
    根据场景名称获取对应的sqlite文件URL

    参数:
        scene (str): 场景名称，如"environment_ShunYi"

    返回:
        str: 对应的sqlite文件URL，如果未找到则返回默认的上海sqlite
    """
    city = ENVIRONMENT_TO_CITY.get(scene)
    if city:
        return CITY_TO_SQLITE.get(city, CITY_TO_SQLITE["上海"])
    else:
        print(f"警告: 未找到场景 '{scene}' 对应的城市映射，使用默认的上海sqlite")
        return CITY_TO_SQLITE["上海"]


# 加载区域配置文件
# 现在直接返回内置ZONES，不再读取外部文件
def load_zones(zones_file=None):
    """直接返回内置的区域配置信息，不再读取外部文件。"""
    return ZONES


# 根据坐标查找对应的区域
def find_zone_for_point(x, y, zones):
    """根据坐标点查找对应的区域"""
    for zone in zones:
        if (
            zone["Left_East"] <= x <= zone["Right_East"]
            and zone["Left_North"] <= y <= zone["Right_North"]
        ):
            return zone
    return None


def parse_bag_file(bag_file_path):
    """
    解析指定的 bag 文件，提取自车位姿和规划轨迹信息。

    参数:
        bag_file_path (str): bag 文件的路径。
    """
    # 清空之前的数据
    clear_global_data()

    print("开始解析 bag 文件: {}".format(bag_file_path))
    found_localization = False
    found_trajectory = False
    # 加载区域配置（只需加载一次）
    zones = load_zones()
    try:
        with rosbag.Bag(bag_file_path, "r") as bag:
            for topic, msg, t in bag.read_messages(
                topics=[
                    "/localization/global",
                    "/planning/global_trajectory",
                    "/hadmap/speed_limit",
                ]
            ):
                if topic == "/localization/global":
                    found_localization = True
                    try:
                        localization_data = Localization()
                        localization_data.ParseFromString(msg.data)
                        timestamp_secs = localization_data.header.stamp.sec
                        timestamp_nsecs = localization_data.header.stamp.nsec
                        full_timestamp = float(
                            "{}.{}".format(
                                timestamp_secs, str(timestamp_nsecs).zfill(9)
                            )
                        )
                        # 获取原始坐标
                        raw_x = localization_data.position.x
                        raw_y = localization_data.position.y
                        # 使用已加载的区域配置
                        # 查找对应的区域
                        zone = find_zone_for_point(raw_x, raw_y, zones)
                        if zone:
                            # 计算相对于区域原点的坐标
                            adjusted_x = raw_x - zone["Origin_East"]
                            adjusted_y = raw_y - zone["Origin_North"]
                            # 打印详细日志
                            # print("坐标点 ({}, {}) 位于区域 {}，原点为 ({}, {})，调整后坐标为 ({}, {})".format(
                            #     raw_x, raw_y, zone['Name'], zone['Origin_East'], zone['Origin_North'], adjusted_x, adjusted_y))
                        else:
                            # 如果没有找到匹配区域，使用原始坐标并打印警告
                            adjusted_x = raw_x
                            adjusted_y = raw_y
                            print(
                                "警告: 在区域中未找到坐标点 ({}, {})，使用原始坐标".format(
                                    raw_x, raw_y
                                )
                            )
                        ego_entry = {
                            "timestamp": full_timestamp,
                            "x": adjusted_x,
                            "y": adjusted_y,
                            "z": localization_data.position.z,
                            "pitch": localization_data.pitch,
                            "yaw": localization_data.yaw,  # yaw in radians
                            "roll": localization_data.roll,
                        }
                        ego_info_list.append(ego_entry)
                    except DecodeError:
                        print("警告: 解码 /localization/global 消息失败。")
                    except Exception as e:
                        print("处理 /localization/global 消息时发生错误: {}".format(e))
                elif topic == "/planning/global_trajectory":
                    found_trajectory = True
                    try:
                        trajectory_data = Trajectory()
                        trajectory_data.ParseFromString(msg.data)
                        for point in trajectory_data.points:
                            # 参考 logsim2worldsimApi.py 中的 trajectory_i 结构
                            # [x, y, 0,0,0, kappa, 0,0, theta, 0, s, 0,0,0,0,0,0,0,0,0,0,0]
                            # traj_1 (x), traj_2 (y), traj_6 (kappa), traj_9 (theta/yaw), traj_11 (s)
                            trajectory_point_formatted = [
                                point.x,
                                point.y,
                                0.0,
                                0.0,
                                0.0,
                                point.kappa,
                                0.0,
                                0.0,
                                point.theta,
                                0.0,
                                point.s,
                                0.0,
                                0.0,
                                0.0,
                                0.0,
                                0.0,
                                0.0,
                                0.0,
                                0.0,
                                0.0,
                                0.0,
                                0.0,
                            ]
                            if (
                                trajectory_point_formatted not in trajectory_info_list
                            ):  # 避免重复（如果源bag可能有重复）
                                trajectory_info_list.append(trajectory_point_formatted)
                    except DecodeError:
                        print("警告: 解码 /planning/global_trajectory 消息失败。")
                    except Exception as e:
                        print(
                            "处理 /planning/global_trajectory 消息时发生错误: {}".format(
                                e
                            )
                        )
                elif topic == "/hadmap/speed_limit":
                    try:
                        mapmsg = MapMsg()
                        mapmsg.ParseFromString(msg.data)
                        map_speedlimit = mapmsg.map
                        objects_speedlimit = map_speedlimit.objects
                        for obj in objects_speedlimit:
                            geom = obj.geom
                            if geom:  # 确保几何信息不为空
                                x = geom[0].x
                                y = geom[0].y
                                # 参考logsim2worldsimApi.py的结构：[text, attribute, x, y, value]
                                stop_entry = [obj.text, obj.attribute, x, y, obj.value]
                                stop_info_list.append(stop_entry)
                    except DecodeError:
                        print("警告: 解码 /hadmap/speed_limit 消息失败。")
                    except Exception as e:
                        print("处理 /hadmap/speed_limit 消息时发生错误: {}".format(e))
        # 对ego_info_list按时间戳排序，确保ego_info_list[0]是最早的点
        if ego_info_list:
            ego_info_list.sort(key=lambda e: e["timestamp"])
            print(f"已提取 {len(ego_info_list)} 个定位数据点")
        if trajectory_info_list:
            print(f"已提取 {len(trajectory_info_list)} 个轨迹数据点")
        if stop_info_list:
            print(f"已提取 {len(stop_info_list)} 个停车标志数据")
    except rosbag.bag.BagException as e:
        print("错误: 无法读取 bag 文件: {}".format(e))
        return
    except Exception as e:
        print("解析 bag 文件时发生未知错误: {}".format(e))
        return
    if not found_localization:
        print("警告: Bag 文件中未找到 /localization/global topic。起点信息可能不完整。")
    if not found_trajectory:
        print(
            "警告: Bag 文件中未找到 /planning/global_trajectory topic。终点和轨迹文件可能无法生成。"
        )
    print("Bag 文件解析完成。")


def extract_and_print_positions():
    """
    提取并打印起点和终点位置。
    """
    print("\n--- 起点和终点位置 ---")
    start_pos_valid = False
    if ego_info_list:
        start_data = ego_info_list[0]
        start_x = start_data["x"]
        start_y = start_data["y"]
        start_yaw_rad = start_data["yaw"]
        start_yaw_deg = math.degrees(start_yaw_rad)
        print("起点位置 (从 /localization/global 获取):")
        print("  X: {:.6f}".format(start_x))
        print("  Y: {:.6f}".format(start_y))
        print("  Yaw (弧度): {:.6f}".format(start_yaw_rad))
        print("  Yaw (角度): {:.2f}°".format(start_yaw_deg))
        start_pos_valid = True
    else:
        print("未能从 /localization/global 提取起点信息 (ego_info_list 为空)。")
    end_pos_valid = False
    if trajectory_info_list:
        # trajectory_info_list 中的每个元素是一个包含22个浮点数的列表
        # 索引0是x, 索引1是y, 索引8是theta (yaw in radians)
        end_data_raw = trajectory_info_list[-1]
        raw_end_x = end_data_raw[0]
        raw_end_y = end_data_raw[1]
        end_yaw_rad = end_data_raw[8]
        end_yaw_deg = math.degrees(end_yaw_rad)
        # 加载区域配置
        zones = load_zones()
        # 查找对应的区域
        zone = find_zone_for_point(raw_end_x, raw_end_y, zones)
        if zone:
            # 计算相对于区域原点的坐标
            end_x = raw_end_x - zone["Origin_East"]
            end_y = raw_end_y - zone["Origin_North"]
            # 打印详细日志
            print(
                "终点坐标点 ({}, {}) 位于区域 {}，原点为 ({}, {})，调整后坐标为 ({}, {})".format(
                    raw_end_x,
                    raw_end_y,
                    zone["Name"],
                    zone["Origin_East"],
                    zone["Origin_North"],
                    end_x,
                    end_y,
                )
            )
        else:
            # 如果没有找到匹配区域，使用原始坐标并打印警告
            end_x = raw_end_x
            end_y = raw_end_y
            print(
                "警告: 在区域中未找到终点坐标点 ({}, {})，使用原始坐标".format(
                    raw_end_x, raw_end_y
                )
            )
        print("\n终点位置 (从 /planning/global_trajectory 的最后一个点获取):")
        print("  X: {:.6f}".format(end_x))
        print("  Y: {:.6f}".format(end_y))
        print("  Yaw (弧度): {:.6f}".format(end_yaw_rad))
        print("  Yaw (角度): {:.2f}°".format(end_yaw_deg))
        end_pos_valid = True
    else:
        print(
            "未能从 /planning/global_trajectory 提取终点信息 (trajectory_info_list 为空)。"
        )
    if not start_pos_valid and not end_pos_valid:
        print("\n未能提取任何有效的起点或终点信息。")


def save_trajectory_file(output_dir="."):
    """
    将提取的轨迹数据保存到 traj.csv 文件。

    参数:
        output_dir (str): traj.csv 文件保存的目录路径。默认为当前目录。
    """
    if not trajectory_info_list:
        print("\n轨迹信息为空，不生成 traj.csv 文件。")
        return
    output_path = os.path.join(output_dir, "traj.csv")
    try:
        print("\n正在保存轨迹文件到: {}".format(output_path))
        with open(output_path, "w") as f:
            for traj_point_data in trajectory_info_list:
                # traj_point_data 是一个包含22个数字的列表
                # 转换列表为字符串，并移除方括号，类似原始项目的做法
                traj_str = str(traj_point_data).replace("[", "").replace("]", "")
                f.write(traj_str + "\n")
        print(
            "轨迹文件 traj.csv 已成功保存。共 {} 个轨迹点。".format(
                len(trajectory_info_list)
            )
        )
    except IOError as e:
        print("错误: 无法写入轨迹文件 {}: {}".format(output_path, e))
    except Exception as e:
        print("保存轨迹文件时发生未知错误: {}".format(e))


def save_stop_file(output_dir="."):
    """
    将提取的停车标志和速度限制数据保存到 stop.txt 文件。

    参数:
        output_dir (str): stop.txt 文件保存的目录路径。默认为当前目录。
    """
    output_path = os.path.join(output_dir, "stop.txt")
    try:
        if not stop_info_list:
            print("\n停车标志信息为空，生成空白 stop.txt 文件。")
            with open(output_path, "w") as f:
                # 创建空白文件
                pass
            print("空白 stop.txt 文件已成功保存。")
            return

        print("\n正在保存停车标志文件到: {}".format(output_path))
        with open(output_path, "w") as f:
            for stop_data in stop_info_list:
                # stop_data 结构: [text, attribute, x, y, value]
                # 参考logsim2worldsimApi.py的write_stop_data函数
                text = stop_data[0]
                attribute = stop_data[1]
                x = stop_data[2]
                y = stop_data[3]
                value = stop_data[4]

                # 确定对象类型
                if "SpeedLimit" in str(text):
                    obj_type = "OBJECT_TYPE_SpeedLimit"
                elif "Stop" in str(text):
                    obj_type = "OBJECT_TYPE_Stop"
                else:
                    obj_type = "OBJECT_TYPE_Unknown"

                # 格式化输出，参考原始代码的格式
                stop_str = 'objects {{\ntext: "{}"\nattribute: "{}"\ntype: {}\nvalue: {}\ngeom {{\nx: {}\ny: {}\n}}\n}}'.format(
                    text, attribute, obj_type, value, x, y
                )
                f.write(stop_str + "\n")

        print(
            "停车标志文件 stop.txt 已成功保存。共 {} 个停车标志。".format(
                len(stop_info_list)
            )
        )
    except IOError as e:
        print("错误: 无法写入停车标志文件 {}: {}".format(output_path, e))
    except Exception as e:
        print("保存停车标志文件时发生未知错误: {}".format(e))


def save_case_json(output_dir=".", bag_file_path=""):
    """
    生成符合case_info_demo.json格式的case.json文件

    参数:
        output_dir (str): case.json文件保存的目录路径。默认为当前目录。
        bag_file_path (str): 原始bag文件路径，用于推断case_id
    """
    if not ego_info_list or not trajectory_info_list:
        print("\n缺少起点或终点信息，不生成 case.json 文件。")
        return

    # 从bag文件名推断case_id
    bag_filename = os.path.basename(bag_file_path)
    case_id = os.path.splitext(bag_filename)[0]

    # 获取起点信息
    start_data = ego_info_list[0]
    start_x = start_data["y"]  # Y -> x
    start_z = start_data["x"]  # X -> z
    start_yaw_rad = start_data["yaw"]
    start_yaw_deg = math.degrees(start_yaw_rad)
    start_yaw_final = 360 - start_yaw_deg

    # 获取终点信息
    end_data_raw = trajectory_info_list[-1]
    raw_end_x = end_data_raw[0]  # 原始X坐标
    raw_end_y = end_data_raw[1]  # 原始Y坐标
    raw_end_yaw_rad = end_data_raw[8]
    raw_end_yaw_deg = math.degrees(raw_end_yaw_rad)
    end_yaw_final = 360 - raw_end_yaw_deg

    # 加载区域配置
    zones = load_zones()
    zone = find_zone_for_point(raw_end_x, raw_end_y, zones)

    if zone:
        # 计算相对于区域原点的坐标
        adjusted_end_x = raw_end_x - zone["Origin_East"]
        adjusted_end_y = raw_end_y - zone["Origin_North"]
    else:
        # 如果没有找到匹配区域，使用原始坐标
        adjusted_end_x = raw_end_x
        adjusted_end_y = raw_end_y
        print(
            "警告: 在区域中未找到终点坐标点 ({}, {})，使用原始坐标".format(
                raw_end_x, raw_end_y
            )
        )

    # 应用坐标转换：Y -> x, X -> z
    end_x = adjusted_end_y  # Y -> x
    end_z = adjusted_end_x  # X -> z

    # 确定场景名称
    if zone:
        scene = zone["Name"]
    else:
        scene = "Unknown"

    # 根据场景自动获取对应的城市和sqlite文件
    city_name = ENVIRONMENT_TO_CITY.get(scene, "上海")  # 默认使用上海
    sqlite_url = get_sqlite_url_by_scene(scene)

    print(f"场景: {scene}")
    print(f"对应城市: {city_name}")
    print(f"sqlite文件: {sqlite_url}")

    # 构建case.json内容
    case_data = {
        "case_id": case_id,
        "case_time": {
            "start_time": start_data["timestamp"],
            "end_time": start_data["timestamp"] + 20.0,  # 默认20秒
            "takeover": start_data["timestamp"] + 7.0,  # 默认7秒
            "event": start_data["timestamp"] + 10.0,  # 默认10秒
        },
        "case_info": {
            "ego_initial": {
                "position": {"x": -start_x, "y": 0.0, "z": start_z},
                "rotation": {"x": 0.0, "y": start_yaw_final, "z": 0.0},
            },
            "ego_dest": {"x": -end_x, "y": 0.0, "z": end_z},
            "scene": scene,
        },
        "map": city_name,
        "vehicle_config": "JLBJA31588D",
        "db.sqlite": sqlite_url,
    }

    output_path = os.path.join(output_dir, "case_info.json")
    try:
        print("\n正在保存case.json文件到: {}".format(output_path))
        with open(output_path, "w", encoding="utf-8") as f:
            json.dump(case_data, f, indent=4, ensure_ascii=False)
        print("case.json 文件已成功保存。")
    except IOError as e:
        print("错误: 无法写入case.json文件 {}: {}".format(output_path, e))
    except Exception as e:
        print("保存case.json文件时发生未知错误: {}".format(e))


if __name__ == "__main__":

    parser = argparse.ArgumentParser(
        description="处理文件夹中的bag文件，如果有多个文件会自动合并，然后提取起点、终点位置并生成轨迹文件和case.json文件。",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python process_bag.py /path/to/bags_folder/
  
说明:
  - 指定包含bag文件的文件夹路径
  - 如果文件夹中有多个bag文件，会自动合并后处理
  - 如果文件夹中只有一个bag文件，直接处理该文件
  - 输出文件会保存在指定的文件夹中
        """,
    )

    parser.add_argument("folder_path", help="包含bag文件的文件夹路径")

    args = parser.parse_args()

    try:
        # 验证文件夹路径
        if not os.path.exists(args.folder_path):
            print(f"错误: 文件夹 '{args.folder_path}' 不存在")
            exit(1)

        if not os.path.isdir(args.folder_path):
            print(f"错误: '{args.folder_path}' 不是一个文件夹")
            exit(1)

        # 处理文件夹中的bag文件
        print(f"开始处理文件夹 '{args.folder_path}' 中的bag文件...")
        merged_bag_path = merge_bags_in_folder(args.folder_path, verbose=False)

        if not merged_bag_path:
            print("处理bag文件失败")
            exit(1)

        # 使用合并后的bag文件进行后续处理
        bag_file_to_process = merged_bag_path
        print(f"开始处理bag文件: {bag_file_to_process}")

        # 使用指定文件夹作为输出目录
        output_dir = args.folder_path

        # 执行原有的处理逻辑
        parse_bag_file(bag_file_to_process)
        extract_and_print_positions()
        save_trajectory_file(output_dir)
        save_stop_file(output_dir)
        save_case_json(output_dir, bag_file_to_process)
        print("\n处理完成。需要手动修改车牌号以及按需修改时间。")

    except KeyboardInterrupt:
        print("\n用户中断操作")
        exit(1)
    except Exception as e:
        print(f"发生错误: {e}")
        exit(1)
