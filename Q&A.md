# 常见问题解答

## Q: 如何安装和配置 NVIDIA Container Toolkit？

A: 如果您需要使用 GPU 功能，请按以下步骤安装 NVIDIA Container Toolkit：

```bash
# 添加 NVIDIA 的 GPG 密钥和仓库
curl -fsSL https://nvidia.github.io/libnvidia-container/gpgkey | sudo gpg --dearmor -o /usr/share/keyrings/nvidia-container-toolkit-keyring.gpg

curl -fsSL https://nvidia.github.io/libnvidia-container/stable/deb/nvidia-container-toolkit.list | \
  sed 's#deb https://#deb [signed-by=/usr/share/keyrings/nvidia-container-toolkit-keyring.gpg] https://#g' | \
  sudo tee /etc/apt/sources.list.d/nvidia-container-toolkit.list

# 安装 NVIDIA Container Toolkit
sudo apt-get update
sudo apt-get install -y nvidia-container-toolkit

# 配置 Docker 运行时
sudo nvidia-ctk runtime configure --runtime=docker

# 重启 Docker 服务
sudo systemctl restart docker

# 验证安装
docker run --rm --gpus all nvidia/cuda:11.0-base nvidia-smi
```

如果遇到 "unknown or invalid runtime name: nvidia" 错误，请确保已正确执行上述步骤。 