from utils.path import get_task_workspace_path, get_task_casefile_path, get_task_map_path, get_task_vehicle_path, get_task_log_path, get_task_result_path, get_task_record_path
from common_steps.stepbase import ValidatorStepBase
from utils.loggers import LoggerProxy
logger = LoggerProxy() 

class Step(ValidatorStepBase):
    def init(self) -> bool:
        logger.info("pnc_common validator init")
        logger.info(f"case_id: {self.case_id}")
        return True
    
    def run(self) -> bool:
        logger.info("pnc_common validator run")
        return True
    
    def end(self) -> bool:
        logger.info("pnc_common validator end")
        return True
    
    def clean(self) -> bool:
        logger.info("pnc_common validator clean")
        return True

