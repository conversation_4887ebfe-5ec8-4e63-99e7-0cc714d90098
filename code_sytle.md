# Python 编码规范（适用于本项目）

## 1. 代码风格
- 代码遵循 PEP8 规范，适当放宽行宽限制（可超过 79，但不超过 120）。
- 使用 4 个空格缩进，不使用制表符（Tab）。
- 每行代码尽量简洁，避免过长的单行代码。
- 适当添加空行，使代码结构清晰（如类、函数之间空一行）。

## 2. 命名规范
- 变量、函数使用 `snake_case`，避免使用单字母变量名，除非在临时变量（如 `i, j, k`）的情况下。
- 类名使用 `PascalCase`。
- 常量使用 `UPPER_CASE`，并定义在模块的开头。
- 模块和文件名使用 `小写 + 下划线` 格式。

## 3. 注释与文档
- 重要逻辑、复杂代码块必须添加注释，使用 `#` 进行单行注释。
- 每个类、函数都要有 docstring 说明，格式如下：
  ```python
  def example_function(param1: int, param2: str) -> bool:
      """
      说明函数的用途。
      
      参数:
          param1 (int): 参数 1 说明
          param2 (str): 参数 2 说明
      
      返回:
          bool: 返回值说明
      """
      ```
- 避免写无意义的注释，如：
  ```python
  x = 10  # 给 x 赋值 10  (不推荐)
  ```

## 4. 代码结构
- 每个 Python 文件都应以 `#!/usr/bin/env python3` 开头（如果是可执行脚本）。
- 按照 `import` 顺序：标准库 -> 第三方库 -> 本项目模块，每个部分之间空一行。
- `__init__.py` 仅用于必要的包初始化，避免写复杂逻辑。

## 5. 异常处理
- 避免 `except Exception:` 这种宽泛的异常处理，尽量捕获特定异常类型。
- 使用 `try-except` 时，必须加日志或错误提示，避免静默错误。
  ```python
  try:
      result = some_function()
  except ValueError as e:
      logger.error(f"参数错误: {e}")
      raise
  ```

## 6. 逻辑与优化
- 使用列表推导式，但避免嵌套过深的表达式。
- 尽量使用 `with` 语句管理资源（如文件操作）。
- 避免使用 `global` 变量，尽量封装到类或函数中。
- 如果代码涉及异步操作，使用 `async/await` 而非 `threading`，避免不必要的线程。


---

