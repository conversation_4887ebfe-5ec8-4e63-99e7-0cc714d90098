ARG BASE_IMAGE=mogohub.tencentcloudcr.com/sim/logsim:enginebase_2004_ros1_noetic
FROM ${BASE_IMAGE}

# 构建参数
ARG ROS_DISTRO
ARG ROS_VERSION

# 环境变量
ENV ROS_DISTRO=${ROS_DISTRO} \
    ROS_VERSION=${ROS_VERSION} \
    DEBIAN_FRONTEND=noninteractive \
    TZ=Asia/Shanghai \
    LANG=en_US.UTF-8 \
    LC_ALL=en_US.UTF-8 \
    PYTHONIOENCODING=utf-8

# 设置时区
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 复制源代码和配置
COPY controller/msim /opt/msim
COPY controller/run /opt/run
COPY validator /opt/code/validator
COPY controller/run/start_validator.sh /usr/local/bin/start_validator.sh
COPY controller/requirements_common.txt /home/<USER>/requirements_common.txt

# 设置权限和安装依赖
USER root
RUN chmod +x /usr/local/bin/start_validator.sh && \
    mkdir -p /home/<USER>/workspace && \
    (id -u mogosim &>/dev/null || useradd -m -s /bin/bash mogosim) && \
    chown -R mogosim:mogosim /opt/msim /opt/run /home/<USER>
    echo "mogosim ALL=(ALL) NOPASSWD:ALL" > /etc/sudoers.d/mogosim && \
    chmod 0440 /etc/sudoers.d/mogosim

# 安装 Python 依赖
USER mogosim
RUN pip3 install --upgrade pip setuptools charset-normalizer -i https://mirrors.aliyun.com/pypi/simple && \
    pip3 download -r /home/<USER>/requirements_common.txt -i https://mirrors.aliyun.com/pypi/simple --dest /tmp --only-binary :all: && \
    pip3 install --no-index --find-links=/tmp -r /home/<USER>/requirements_common.txt && \
    rm -rf /tmp/*

# 设置工作目录
WORKDIR /home/<USER>/workspace

# 设置默认用户为mogosim
USER mogosim

CMD ["/bin/bash"]
