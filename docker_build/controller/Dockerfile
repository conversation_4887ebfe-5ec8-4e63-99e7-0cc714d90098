FROM mogohub.tencentcloudcr.com/sim/logsim:controller_base
ENV DEBIAN_FRONTEND=noninteractive

# 设置时区
ENV TZ=Asia/Shanghai
RUN chmod 1777 /tmp && \
    ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 使用阿里云镜像源并安装依赖
RUN sed -i 's/archive.ubuntu.com/mirrors.aliyun.com/g' /etc/apt/sources.list && \
    sed -i 's/security.ubuntu.com/mirrors.aliyun.com/g' /etc/apt/sources.list && \
    apt-get update && \
    apt-get install -y --no-install-recommends \
        locales \
        fonts-wqy-zenhei \
        software-properties-common && \
    locale-gen en_US.UTF-8 && \
    update-locale LANG=en_US.UTF-8 LC_ALL=en_US.UTF-8 && \
    echo 'mogosim ALL=(ALL) NOPASSWD: ALL' >> /etc/sudoers && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# 设置环境变量
ENV LANG=en_US.UTF-8
ENV LC_ALL=en_US.UTF-8
ENV PYTHONIOENCODING=utf-8

RUN mkdir -p /opt/msim

WORKDIR /opt/msim

# 复制依赖文件
COPY --chown=mogosim:mogosim requirements_common.txt /home/<USER>/requirements_common.txt
COPY --chown=mogosim:mogosim requirements_controller.txt /home/<USER>/requirements_controller.txt

# 用root安装依赖
RUN pip3 download -r /home/<USER>/requirements_common.txt -i https://mirrors.aliyun.com/pypi/simple --dest /tmp --only-binary :all: && \
    pip3 download -r /home/<USER>/requirements_controller.txt -i https://mirrors.aliyun.com/pypi/simple --dest /tmp --only-binary :all: && \
    pip3 install --no-index --find-links=/tmp -r /home/<USER>/requirements_common.txt && \
    pip3 install --no-index --find-links=/tmp -r /home/<USER>/requirements_controller.txt && \
    rm -rf /tmp/*

COPY ./msim /opt/msim
COPY ./run /opt/run

COPY ./run/start_controller.sh /usr/local/bin/start_controller.sh

RUN chmod 755 -R /opt/run

RUN chown -R mogosim:mogosim /home/<USER>

USER mogosim

CMD ["/bin/bash"]