#!/bin/bash
set -ex

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname $(dirname "$SCRIPT_DIR"))"
CONTROLLER_DIR="$PROJECT_DIR/controller"
REPOSITORY_NAME="mogohub.tencentcloudcr.com/sim/logsim"
CODE_VERSION=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
BUILD_PARAMS=""
DEBUG_MODE=0

# 解析命令行参数
# -p: 推送镜像到仓库
# -d: debug模式，使用固定的"controller"标签
while getopts "pd" flag
do
    case "${flag}" in
        p) BUILD_PARAMS="--push" ;;
        d) DEBUG_MODE=1 ;;
        *) echo "警告: 未知选项 ${flag}" ;;
    esac
done

# 根据是否为debug模式设置镜像标签
if [ $DEBUG_MODE -eq 1 ]; then
    TAG_NAME="controller"
    echo "使用debug模式，固定标签为: controller"
else
    TAG_NAME="controller-${CODE_VERSION}"
fi

OUTPUT_IMAGE="${REPOSITORY_NAME}:${TAG_NAME}"

echo "========================================================"
echo "构建信息:"
echo "  输出镜像: ${OUTPUT_IMAGE}"
echo "  构建参数: ${BUILD_PARAMS}"
echo "  Debug模式: $([ $DEBUG_MODE -eq 1 ] && echo "是" || echo "否")"
echo "========================================================"

# 设置构建参数
BUILD_PARAMS="${BUILD_PARAMS} -t ${OUTPUT_IMAGE} -f Dockerfile $CONTROLLER_DIR"

# 构建镜像
echo "开始构建镜像..."
docker build $BUILD_PARAMS

if [ $? -eq 0 ]; then
    echo "========================================================"
    echo "镜像构建成功: ${OUTPUT_IMAGE}"
    echo "========================================================"
else
    echo "========================================================"
    echo "错误: 镜像构建失败"
    echo "========================================================"
    exit 1
fi

