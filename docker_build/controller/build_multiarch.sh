#!/bin/bash
set -ex

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname $(dirname "$SCRIPT_DIR"))"
CONTROLLER_DIR="$PROJECT_DIR/controller"
REPOSITORY_NAME="udeer-registry-vpc.cn-hangzhou.cr.aliyuncs.com/usim/controller"
VERSION=$(cat "${CONTROLLER_DIR}/VERSION")
TAG_NAME=${VERSION}-$(git rev-parse --short HEAD)


while getopts d:r: flag
do
    case "${flag}" in
        d) TAG_NAME="debug";;
        *) error "Unexpected option ${flag}" ;;
    esac
done

function cleanup() {
  rm -rf $CONTROLLER_DIR/udeer
}

function download_udeer_scripts() {
  mkdir -p $CONTROLLER_DIR/udeer
  git archive --format=tar --remote=*********************:udeer/udeer.git HEAD:scripts get-dependencies.py | tar -xO > $CONTROLLER_DIR/udeer/get-dependencies.py
}

function docker_build() {
  docker run --rm --privileged multiarch/qemu-user-static --reset -p yes --credential yes
  docker buildx build --platform linux/arm64,linux/amd64 --push -t ${REPOSITORY_NAME}:${TAG_NAME} -f Dockerfile $CONTROLLER_DIR
}

trap 'cleanup' ERR EXIT

download_udeer_scripts
docker_build
