#!/bin/bash
set -e

# 定义分支
BRANCH="785_linux"

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

echo "========================================================"
echo "开始构建所有镜像（ROS1 Debug版本）"
echo "使用分支: ${BRANCH}"
echo "========================================================"

# 获取 git 认证信息
echo "请输入 GitLab 用户名："
read GIT_USERNAME
echo "请输入 GitLab 密码："
read -s GIT_PASSWORD
echo

# 拉取基础镜像
echo "拉取基础镜像..."
docker pull mogohub.tencentcloudcr.com/sim/logsim:controller_base
docker pull mogohub.tencentcloudcr.com/sim/logsim:enginebase_2004_ros1_noetic

# 删除旧镜像
echo "删除旧镜像..."
docker rmi -f mogohub.tencentcloudcr.com/sim/logsim:controller || true
docker rmi -f mogohub.tencentcloudcr.com/sim/logsim:engine || true
docker rmi -f mogohub.tencentcloudcr.com/sim/logsim:validator || true

# 构建控制器镜像
echo "构建控制器镜像..."
cd ${SCRIPT_DIR}/controller
./build.sh -d

# 设置LGSVL仓库
echo "设置LGSVL仓库..."
cd ${SCRIPT_DIR}
./setup_lgsvl.sh "${GIT_USERNAME}" "${GIT_PASSWORD}" "${BRANCH}"

# 构建引擎镜像
echo "构建引擎镜像..."
cd ${SCRIPT_DIR}/engine
./build.sh ros1 -d

# 构建评价镜像
echo "构建评价镜像..."
cd ${SCRIPT_DIR}/validator
./build.sh ros1 -d

# 清理认证信息
unset GIT_USERNAME
unset GIT_PASSWORD

echo "========================================================"
echo "所有镜像构建完成！"
echo "========================================================" 
