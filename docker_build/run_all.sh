#!/bin/bash

set -e

# 定义镜像仓库
REPOSITORY_NAME="mogohub.tencentcloudcr.com/sim/logsim"

# 定义容器名称
CONTROLLER_CONTAINER="controller"
ENGINE_CONTAINER="engine"
VALIDATOR_CONTAINER="validator"

# 获取当前用户名
USER_NAME=$(whoami)

# 删除MOUNT_PATH目录下的algo_image_id code，并删除容器
sudo rm -rf $MOUNT_PATH/algo_image_id $MOUNT_PATH/code
docker rm -f $CONTROLLER_CONTAINER $ENGINE_CONTAINER $VALIDATOR_CONTAINER

# 挂载路径
MOUNT_PATH="/home/<USER>/msim_workspace"
mkdir -p $MOUNT_PATH/logs
sudo chmod 777 $MOUNT_PATH/logs
mkdir -p $MOUNT_PATH/mount
sudo chmod 777 $MOUNT_PATH/mount
DOCKER_MOUNT_PATH="/home/<USER>/workspace"

# 使用纯shell方式检查manual模式
check_local_mode() {
    local tasks_info_file="$1"
    
    if [ ! -f "$tasks_info_file" ]; then
        echo "false"
        return
    fi
    
    # 使用grep和sed提取manual字段值
    # 匹配 "manual": true/false 或 "manual": "true"/"false"
    local_line=$(grep -o '"manual"[[:space:]]*:[[:space:]]*[^,}]*' "$tasks_info_file" 2>/dev/null | head -1)
    
    if [ -z "$local_line" ]; then
        echo "false"
        return
    fi
    
    # 提取值部分（冒号后的内容）
    local_value=$(echo "$local_line" | sed 's/^"manual"[[:space:]]*:[[:space:]]*//' | sed 's/[", ]//g')
    
    # 转换为小写进行比较
    local_value_lower=$(echo "$local_value" | tr '[:upper:]' '[:lower:]')
    
    if [ "$local_value_lower" = "true" ]; then
        echo "true"
    else
        echo "false"
    fi
}

# 检查manual模式
TASKS_INFO_FILE="${MOUNT_PATH}/tasks_info.json"
IS_LOCAL=$(check_local_mode "$TASKS_INFO_FILE")

# 根据manual模式设置网络参数
if [ "$IS_LOCAL" = "true" ]; then
    NETWORK_MODE="--network=host"
    CONTAINER_NETWORK_MODE="--network=host"
    echo "本地模式：使用host网络"
else
    NETWORK_MODE="--network=mynetwork"
    CONTAINER_NETWORK_MODE="--network=container:$CONTROLLER_CONTAINER"
    echo "容器模式：使用自定义网络"
fi

# 使用shell方式获取TASKS_INFO_FILE文件中caseList字段对应的字典，并获取里面的value值
# 解析caseList字段，获取所有value
TASKS_INFO_FILE="${MOUNT_PATH}/tasks_info.json"
echo "TASKS_INFO_FILE: $TASKS_INFO_FILE"
VOLUME_MOUNTS=""

if [ -f "$TASKS_INFO_FILE" ]; then
    # echo "解析tasks_info.json文件..."
    if command -v jq >/dev/null 2>&1; then
        case_values=$(jq -r '.caseList[]' "$TASKS_INFO_FILE")
    else
        # 兼容无jq环境
        case_block=$(awk '/"caseList"[[:space:]]*:/,/[}]/' "$TASKS_INFO_FILE" | sed '1d;$d')
        case_values=$(echo "$case_block" | grep ':' | sed 's/^[[:space:]]*"[^"]*"[[:space:]]*:[[:space:]]*"\?\([^",}]*\)"\?,\?/\1/')
    fi
    # echo "所有caseList : $case_values"
    for value in $case_values; do
        # echo "单个case的value: $value"
        if [[ "$value" != *https://autocar-mogosim-1255510688.cos.ap-beijing.myqcloud.com* ]]; then
            VOLUME_MOUNTS="$VOLUME_MOUNTS -v $value:/home/<USER>/workspace/$(basename $value) "
        fi
    done
fi

# 检查并删除已存在的容器
for CONTAINER in $CONTROLLER_CONTAINER $ENGINE_CONTAINER $VALIDATOR_CONTAINER
do
  if [ $(docker ps -a -q -f name=$CONTAINER) ]; then
    docker rm -f $CONTAINER
  fi
done

echo "========================================================"
echo "启动调试容器 (模式: $([ "$IS_LOCAL" = "true" ] && echo "本地" || echo "容器"))"
echo "  控制器镜像: ${REPOSITORY_NAME}:controller"
echo "  引擎镜像: ${REPOSITORY_NAME}:engine"
echo "  评价镜像: ${REPOSITORY_NAME}:validator"
echo "  挂载目录: ${MOUNT_PATH}"
echo "  映射参数: $VOLUME_MOUNTS"
echo "========================================================"

# 创建自定义网络（仅在容器模式下）
if [ "$IS_LOCAL" = "false" ] && ! docker network inspect mynetwork >/dev/null 2>&1; then
    docker network create mynetwork
fi

# 启动controller容器
echo "启动控制器容器..."
docker run -itd --name $CONTROLLER_CONTAINER $NETWORK_MODE \
    -v $MOUNT_PATH:$DOCKER_MOUNT_PATH \
    --privileged \
    -v /var/run/docker.sock:/var/run/docker.sock \
    -v /usr/bin/docker:/usr/bin/docker \
    $VOLUME_MOUNTS \
    ${REPOSITORY_NAME}:controller \
    /usr/local/bin/start_controller.sh

# 等待3s，确保controller容器启动完成
sleep 3

# 启动engine容器
echo "启动引擎容器..."
docker run -itd --name $ENGINE_CONTAINER $CONTAINER_NETWORK_MODE \
    --gpus all \
    -e NVIDIA_DRIVER_CAPABILITIES=all \
    -e ROS_LOCALHOST_ONLY=1 \
    -v /tmp/.X11-unix:/tmp/.X11-unix \
    -e DISPLAY=:99 \
    -v $MOUNT_PATH:$DOCKER_MOUNT_PATH \
    -v /var/run/docker.sock:/var/run/docker.sock \
    -v /usr/bin/docker:/usr/bin/docker \
    --privileged \
    ${REPOSITORY_NAME}:engine \
    /usr/local/bin/start_engine.sh

# 启动validator容器
echo "启动评价容器..."
docker run -itd --name $VALIDATOR_CONTAINER $CONTAINER_NETWORK_MODE \
    -e ROS_LOCALHOST_ONLY=1 \
    -v $MOUNT_PATH:$DOCKER_MOUNT_PATH \
    --privileged \
    ${REPOSITORY_NAME}:validator \
    /usr/local/bin/start_validator.sh

echo "========================================================"
echo "容器已启动"
echo "  控制器容器: $CONTROLLER_CONTAINER"
echo "  引擎容器: $ENGINE_CONTAINER"
echo "  评价容器: $VALIDATOR_CONTAINER"
echo "========================================================"
echo "可以使用以下命令进入容器:"
echo "  docker exec -it $CONTROLLER_CONTAINER bash"
echo "  docker exec -it $ENGINE_CONTAINER bash"
echo "  docker exec -it $VALIDATOR_CONTAINER bash"
echo "========================================================"

# 等待5秒钟
sleep 5

# 获取TASKS_INFO_FILE文件中caseList字段对应的字典，并获取里面的key值
case_keys=""
if [ -f "$TASKS_INFO_FILE" ]; then
    # echo "解析tasks_info.json文件..."
    if command -v jq >/dev/null 2>&1; then
        case_keys=$(jq -r '.caseList | keys[]' "$TASKS_INFO_FILE" 2>/dev/null || echo "")
    else
        # 兼容无jq环境，提取caseList中的所有key
        # 提取caseList块
        case_block=$(awk '/"caseList"[[:space:]]*:/,/[}]/' "$TASKS_INFO_FILE" | sed '1d;$d')
        # 从caseList块中提取所有key（引号内的字符串）
        case_keys=$(echo "$case_block" | grep '"' | sed -E 's/[[:space:]]*"([^"]*)"[[:space:]]*:.*/\1/')
    fi
fi
# echo "所有caseList : $case_keys"

# 获取TASKS_INFO_FILE文件中"simTaskId"字段对应的value值，如果首尾有双引号或单引号则去掉，兼容无jq环境
TASKS_INFO_FILE="${MOUNT_PATH}/tasks_info.json"
if [ -f "$TASKS_INFO_FILE" ]; then
    if command -v jq >/dev/null 2>&1; then
        SIM_TASK_ID=$(jq -r '.simTaskId' "$TASKS_INFO_FILE")
    else
        # 兼容无jq环境
        SIM_TASK_ID=$(grep '"simTaskId"' "$TASKS_INFO_FILE" | cut -d':' -f2 | sed 's/[", ]//g' | xargs)
    fi
    # echo "simTaskId: $SIM_TASK_ID"
fi
SIM_TASK_ID_FOLDER=$(ls -1 "$MOUNT_PATH" | grep "^$SIM_TASK_ID" | sort | tail -1)
# echo "SIM_TASK_ID_FOLDER: $SIM_TASK_ID_FOLDER, MOUNT_PATH: $MOUNT_PATH"



# 将case_keys转换为数组
if command -v jq >/dev/null 2>&1; then
    case_keys_array=($(jq -r '.caseList | keys[]' "$TASKS_INFO_FILE" 2>/dev/null || echo ""))
else
    case_block=$(awk '/"caseList"[[:space:]]*:/,/[}]/' "$TASKS_INFO_FILE" | sed '1d;$d')
    case_keys_array=($(echo "$case_block" | grep '"' | sed -E 's/[[:space:]]*"([^"]*)"[[:space:]]*:.*/\1/'))
fi

# 获取case数量
total_cases=${#case_keys_array[@]}
echo "总共需要执行 $total_cases 个case"

# 构建完整路径
full_task_folder="$MOUNT_PATH/$SIM_TASK_ID_FOLDER"

# 循环检查进度直到所有case完成
while true; do
    # 检查目标文件夹是否存在
    if [ ! -d "$full_task_folder" ]; then
        echo "等待任务文件夹创建: $full_task_folder"
        sleep 5
        continue
    fi
    
    # 统计目标文件夹下的子文件夹数量
    completed_cases=$(find "$full_task_folder" -mindepth 1 -maxdepth 1 -type d | wc -l)
    
    # 检查是否所有case都已完成
    if [ $completed_cases -ge $total_cases ]; then
        echo "所有 $total_cases 个case已执行完成"
        break
    fi
    
    # 计算并打印当前正在执行的case
    current_index=$completed_cases
    if [ $current_index -lt $total_cases ]; then
        current_case=${case_keys_array[$current_index]}
        echo "正在执行第 $((current_index + 1)) 个case: $current_case"
    fi
    
    echo "进度: $completed_cases/$total_cases 个case已完成"
    sleep 10
done

echo "所有case已处理完毕"
docker rm -f $CONTROLLER_CONTAINER $ENGINE_CONTAINER $VALIDATOR_CONTAINER