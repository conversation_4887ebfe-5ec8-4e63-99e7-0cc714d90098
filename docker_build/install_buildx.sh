#!/bin/bash
set -e

echo "========================================================"
echo "手动安装 Docker Buildx"
echo "========================================================"

# 检查系统架构
ARCH=$(uname -m)
echo "系统架构: $ARCH"

# 根据架构选择正确的版本
case $ARCH in
    x86_64)
        BUILDX_ARCH="linux-amd64"
        ;;
    aarch64|arm64)
        BUILDX_ARCH="linux-arm64"
        ;;
    armv7l)
        BUILDX_ARCH="linux-arm-v7"
        ;;
    *)
        echo "不支持的架构: $ARCH"
        exit 1
        ;;
esac

echo "将下载 buildx-$BUILDX_ARCH 版本"

# 创建插件目录
PLUGIN_DIR="$HOME/.docker/cli-plugins"
echo "创建插件目录: $PLUGIN_DIR"
mkdir -p "$PLUGIN_DIR"

# 下载最新版本的 buildx
echo "下载 Docker Buildx..."
BUILDX_VERSION="v0.12.1"
BUILDX_URL="https://github.com/docker/buildx/releases/download/${BUILDX_VERSION}/buildx-${BUILDX_VERSION}.${BUILDX_ARCH}"

echo "下载地址: $BUILDX_URL"

# 下载 buildx
if curl -L "$BUILDX_URL" -o "$PLUGIN_DIR/docker-buildx"; then
    echo "下载成功"
else
    echo "下载失败，尝试备用方法..."
    # 备用下载方法
    wget -O "$PLUGIN_DIR/docker-buildx" "$BUILDX_URL" || {
        echo "下载失败，请检查网络连接"
        exit 1
    }
fi

# 设置执行权限
chmod +x "$PLUGIN_DIR/docker-buildx"

echo "验证安装..."
if docker buildx version; then
    echo "========================================================"
    echo "✓ Docker Buildx 安装成功！"
    echo "========================================================"
else
    echo "========================================================"
    echo "✗ Docker Buildx 安装失败"
    echo "========================================================"
    exit 1
fi 