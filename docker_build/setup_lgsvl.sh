#!/bin/bash
set -e

# 使用说明
if [ $# -ne 3 ]; then
    echo "用法: $0 <git_username> <git_password> <branch>"
    echo "示例: $0 username password 781_linux"
    exit 1
fi

GIT_USERNAME="$1"
GIT_PASSWORD="$2"
BRANCH="$3"

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LGSVL_DIR="${SCRIPT_DIR}/../sim_lgsvl"
REPO_URL="http://${GIT_USERNAME}:${GIT_PASSWORD}@gitlab.zhidaoauto.com/autopilot/autopilot_L3/sim_lgsvl.git"

echo "========================================================"
echo "LGSVL 仓库设置脚本"
echo "目标分支: ${BRANCH}"
echo "仓库路径: ${LGSVL_DIR}"
echo "========================================================"

# 1. 如果没有sim_lgsvl目录，就执行git clone
if [ ! -d "${LGSVL_DIR}" ]; then
    echo "sim_lgsvl 目录不存在，开始克隆..."
    git clone -b ${BRANCH} ${REPO_URL} --depth=1 ${LGSVL_DIR}
    echo "克隆完成！"
    exit 0
fi

# 2. 如果有目录，检查它是不是我们要的这个项目
echo "检查 sim_lgsvl 目录..."
cd ${LGSVL_DIR}

if [ ! -d ".git" ]; then
    echo "错误: ${LGSVL_DIR} 不是git仓库"
    echo "删除目录并重新克隆..."
    cd ${SCRIPT_DIR}
    rm -rf ${LGSVL_DIR}
    git clone -b ${BRANCH} ${REPO_URL} --depth=1 ${LGSVL_DIR}
    echo "重新克隆完成！"
    exit 0
fi

# 检查远程仓库URL是否正确
CURRENT_REMOTE=$(git remote get-url origin 2>/dev/null || echo "")
if [ "${CURRENT_REMOTE}" != "${REPO_URL}" ]; then
    echo "远程仓库URL不匹配，删除目录并重新克隆..."
    cd ${SCRIPT_DIR}
    rm -rf ${LGSVL_DIR}
    git clone -b ${BRANCH} ${REPO_URL} --depth=1 ${LGSVL_DIR}
    echo "重新克隆完成！"
    exit 0
fi

# 3. 检查当前分支
echo "检查当前分支状态..."
CURRENT_BRANCH=$(git branch --show-current)
echo "当前分支: ${CURRENT_BRANCH}"

# 4. 如果目标分支不是当前分支，删除目录重新克隆
if [ "${CURRENT_BRANCH}" != "${BRANCH}" ]; then
    echo "目标分支 ${BRANCH} 与当前分支 ${CURRENT_BRANCH} 不同"
    echo "删除目录并重新克隆..."
    cd ${SCRIPT_DIR}
    rm -rf ${LGSVL_DIR}
    git clone -b ${BRANCH} ${REPO_URL} --depth=1 ${LGSVL_DIR}
    echo "重新克隆完成！"
    exit 0
fi

# 5. 如果是同一个分支，确保本地和远程一致
echo "目标分支与当前分支一致，确保与远程同步..."
git fetch origin
git reset --hard origin/${BRANCH}
git clean -fd

echo "========================================================"
echo "LGSVL 仓库设置完成！"
echo "当前分支: $(git branch --show-current)"
echo "最新提交: $(git log -1 --oneline)"
echo "========================================================" 