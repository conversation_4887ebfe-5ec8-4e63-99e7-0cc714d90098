#!/bin/bash

set -e

# 定义容器名称
CONTROLLER_CONTAINER="controller"
ENGINE_CONTAINER="engine"
VALIDATOR_CONTAINER="validator"

# 获取当前脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo "========================================================"
echo "开始更新容器代码"
echo "========================================================"

# 检查容器是否在运行
for CONTAINER in $CONTROLLER_CONTAINER $ENGINE_CONTAINER $VALIDATOR_CONTAINER
do
    if ! docker ps | grep -q $CONTAINER; then
        echo "错误: 容器 $CONTAINER 未运行"
        exit 1
    fi
done

# 更新控制器容器
echo "更新控制器容器..."
docker cp $PROJECT_ROOT/controller/msim $CONTROLLER_CONTAINER:/opt/
docker cp $PROJECT_ROOT/controller/run $CONTROLLER_CONTAINER:/opt/

# 更新引擎容器
echo "更新引擎容器..."
docker cp $PROJECT_ROOT/controller/msim $ENGINE_CONTAINER:/opt/
docker cp $PROJECT_ROOT/controller/run $ENGINE_CONTAINER:/opt/

# 更新评价容器
echo "更新评价容器..."
docker cp $PROJECT_ROOT/controller/msim $VALIDATOR_CONTAINER:/opt/
docker cp $PROJECT_ROOT/controller/run $VALIDATOR_CONTAINER:/opt/

echo "========================================================"
echo "代码更新完成！"
echo "========================================================"
echo "如果需要重启服务，请执行以下命令："
echo "  控制器: docker exec -it $CONTROLLER_CONTAINER /usr/local/bin/start_controller.sh"
echo "  引擎: docker exec -it $ENGINE_CONTAINER /usr/local/bin/start_engine.sh"
echo "  评价: docker exec -it $VALIDATOR_CONTAINER /usr/local/bin/start_validator.sh"
echo "========================================================" 