#!/bin/bash
set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname $(dirname "$SCRIPT_DIR"))" # 获取到项目根目录
echo "构建上下文：${PROJECT_DIR}"

# 定义默认参数
REPOSITORY_NAME="mogohub.tencentcloudcr.com/sim/logsim"
CODE_VERSION=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
DEBUG_MODE=0

# 处理命令行参数
if [ $# -lt 1 ]; then
    echo "错误: 必须指定ROS版本"
    echo "用法: $0 ros1|ros2 [-d]"
    echo "参数: -d 表示debug模式，会使用固定的'engine'标签"
    exit 1
fi

ROS_TYPE=$1
shift

# 解析可选参数
while getopts "d" flag; do
    case "${flag}" in
        d) DEBUG_MODE=1 ;;
        *) echo "警告: 未知选项 ${flag}" ;;
    esac
done

# 根据ROS版本决定基础镜像和ROS发行版
if [ "$ROS_TYPE" = "ros1" ]; then
    BASE_VERSION="enginebase_2004_ros1_noetic"
    ROS_DISTRO="noetic"
    ROS_VERSION="1"
elif [ "$ROS_TYPE" = "ros2" ]; then
    BASE_VERSION="enginebase_2204_ros2_humble"
    ROS_DISTRO="humble"
    ROS_VERSION="2"
else
    echo "错误: ROS版本必须是 'ros1' 或 'ros2'"
    echo "用法: $0 ros1|ros2 [-d]"
    exit 1
fi

# 根据是否为debug模式设置镜像标签
if [ $DEBUG_MODE -eq 1 ]; then
    TAG_NAME="engine"
    echo "使用debug模式，固定标签为: engine"
else
    TAG_NAME="engine-${CODE_VERSION}"
fi

BASE_IMAGE="${REPOSITORY_NAME}:${BASE_VERSION}"
OUTPUT_IMAGE="${REPOSITORY_NAME}:${TAG_NAME}"

echo "========================================================"
echo "构建信息:"
echo "  ROS版本: ${ROS_TYPE}"
echo "  基础镜像: ${BASE_IMAGE}"
echo "  输出镜像: ${OUTPUT_IMAGE}"
echo "  Debug模式: $([ $DEBUG_MODE -eq 1 ] && echo "是" || echo "否")"
echo "========================================================"

# 检查基础镜像是否存在，如果不存在则拉取
if ! docker image inspect ${BASE_IMAGE} >/dev/null 2>&1; then
    echo "拉取基础镜像 ${BASE_IMAGE}..."
    docker pull ${BASE_IMAGE} || { echo "错误: 无法拉取基础镜像 ${BASE_IMAGE}"; exit 1; }
else
    echo "基础镜像 ${BASE_IMAGE} 已存在，跳过拉取。"
fi

# 构建镜像
echo "开始构建镜像..."
docker build \
    -t ${OUTPUT_IMAGE} \
    -f ${SCRIPT_DIR}/Dockerfile ${PROJECT_DIR} \
    --build-arg BASE_IMAGE=${BASE_IMAGE} \
    --build-arg ROS_DISTRO=${ROS_DISTRO} \
    --build-arg ROS_VERSION=${ROS_VERSION}

if [ $? -eq 0 ]; then
    echo "========================================================"
    echo "镜像构建成功: ${OUTPUT_IMAGE}"
    echo "========================================================"
else
    echo "========================================================"
    echo "错误: 镜像构建失败"
    echo "========================================================"
    exit 1
fi
