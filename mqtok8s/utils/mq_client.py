import pika
import threading
import time
from contextlib import contextmanager
from .log import logger

class MQClient:
    def __init__(self, host, port, vhost, credentials, queue, config_callback=None, max_retries=5):
        self.config = self._init_config(host, port, vhost, credentials, queue)
        self.connection = None
        self.channel = None
        self.reconnecting = threading.Lock()
        self.heartbeat_failures = 0
        self.consuming = False
        self.config_callback = config_callback
        self.max_retries = max_retries
        
        # 自动连接
        self.connect()

    def _init_config(self, host, port, vhost, credentials, queue):
        """初始化MQ配置"""
        return {
            "host": host,
            "port": port,
            "vhost": vhost,
            "credentials": pika.PlainCredentials(credentials[0], credentials[1]),
            "queue": queue
        }

    def connect(self):
        """建立MQ连接，有最大重试次数"""
        self._close_connection()
        retries = 0
        while retries < self.max_retries:
            try:
                self.connection = pika.BlockingConnection(
                    pika.ConnectionParameters(
                        host=self.config["host"],
                        port=self.config["port"],
                        virtual_host=self.config["vhost"],
                        credentials=self.config["credentials"],
                        heartbeat=60,
                        connection_attempts=3,
                        retry_delay=5
                    )
                )
                self.channel = self.connection.channel()
                self.channel.basic_qos(prefetch_count=1)
                logger.info("MQ connection established")
                return True
            except pika.exceptions.AMQPConnectionError as e:
                logger.error(f"AMQP connection error: {str(e)}")
                retries += 1
                time.sleep(5)
            except pika.exceptions.ChannelError as e:
                logger.error(f"Channel error: {str(e)}")
                retries += 1
                time.sleep(5)
            except Exception as e:
                logger.error(f"Unexpected MQ error: {str(e)}")
                retries += 1
                time.sleep(5)
        
        logger.critical(f"Failed to connect after {self.max_retries} attempts")
        return False

    def start_consuming(self, message_callback):
        """启动消息消费"""
        def consumption_loop():
            self.consuming = True
            while self.consuming:
                try:
                    if not self.channel or self.channel.is_closed:
                        self.connect()
                    
                    self.channel.basic_consume(
                        queue=self.config["queue"],
                        on_message_callback=message_callback,
                        auto_ack=False
                    )
                    logger.info("Starting MQ consumer...")
                    self.channel.start_consuming()
                except Exception as e:
                    logger.error(f"MQ consumption error: {str(e)}")
                    time.sleep(5)

        self.consumer_thread = threading.Thread(
            target=consumption_loop,
            daemon=True
        )
        self.consumer_thread.start()

    def stop_consuming(self):
        """停止消费"""
        self.consuming = False
        if self.channel:
            try:
                self.channel.stop_consuming()
            except Exception as e:
                logger.error(f"Error stopping consumption: {str(e)}")

    def _close_connection(self):
        """安全关闭连接"""
        try:
            if self.channel:
                self.channel.close()
            if self.connection:
                self.connection.close()
        except Exception as e:
            logger.error(f"Error closing connection: {str(e)}")

    def maintain_connection(self):
        """维护连接健康"""
        if self.heartbeat_failures >= 3:
            with self.reconnecting:
                logger.warning("Reconnecting due to heartbeat failures")
                self.connect()
                self.heartbeat_failures = 0

    def ack_message(self, delivery_tag):
        """确认消息"""
        if self.channel:
            self.channel.basic_ack(delivery_tag)

    def nack_message(self, delivery_tag):
        """拒绝消息"""
        if self.channel:
            self.channel.basic_nack(delivery_tag)

    # 添加心跳检测方法
    def check_heartbeat(self):
        """检查心跳状态"""
        try:
            if self.connection and not self.connection.is_open:
                self.heartbeat_failures += 1
                logger.warning(f"Heartbeat failure detected ({self.heartbeat_failures}/3)")
                self.maintain_connection()
        except Exception as e:
            logger.error(f"Error checking heartbeat: {str(e)}")
            self.heartbeat_failures += 1
            self.maintain_connection()

    # 添加上下文管理器支持
    @contextmanager
    def channel_context(self):
        """提供通道上下文管理，确保资源正确释放"""
        try:
            if not self.channel or self.channel.is_closed:
                self.connect()
            yield self.channel
        except Exception as e:
            logger.error(f"Channel context error: {str(e)}")
            self._close_connection()
            raise

    def close(self):
        """优雅关闭客户端"""
        self.stop_consuming()
        self._close_connection()
        logger.info("MQ client closed") 