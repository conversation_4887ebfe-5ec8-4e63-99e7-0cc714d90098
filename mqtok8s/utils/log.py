import logging
import sys
import os
from datetime import datetime

# 确保日志目录存在
os.makedirs('logs', exist_ok=True)

# 创建日志记录器
logger = logging.getLogger('mqtok8s')
logger.setLevel(logging.INFO)

# 防止日志重复打印
if not logger.handlers:
    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)

    # 文件处理器
    log_filename = f"logs/mqtok8s_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    file_handler = logging.FileHandler(log_filename)
    file_handler.setLevel(logging.INFO)

    # 设置日志格式
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(formatter)
    file_handler.setFormatter(formatter)

    # 添加处理器
    logger.addHandler(console_handler)
    logger.addHandler(file_handler)

# 添加额外的日志功能
def print_exc():
    """打印异常堆栈信息"""
    import traceback
    logger.error(traceback.format_exc())
    
# 添加方法到logger
if not hasattr(logger, 'print_exc'):
    logger.print_exc = print_exc 