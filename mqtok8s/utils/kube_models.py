# -*- coding: utf-8 -*-
"""
kube_models.py

定义用于 Kubernetes 资源监控模块的数据模型 (dataclasses)。
这些模型用于结构化地表示集群资源、指标和调度相关信息。
"""
from dataclasses import dataclass, field
from typing import List, Dict, Optional, Any


@dataclass
class ResourceValues:
    """
    表示一组标准的资源值 (CPU, 内存, GPU)。
    """

    cpu_millicores: int
    memory_bytes: int
    gpu_count: int = 0


@dataclass
class NodeInfo:
    """包含节点的基本简化信息。"""

    name: str
    status: str
    internal_ip: str
    kubelet_version: str
    os_image: str
    architecture: str
    labels: Dict[str, str] = field(default_factory=dict)
    creation_timestamp: Optional[str] = None


@dataclass
class NodeMetricsReport:
    """包含单个节点的完整指标报告。"""

    node_name: str
    timestamp: Optional[str]
    window: Optional[str]
    usage: ResourceValues
    allocatable: ResourceValues
    available: ResourceValues


@dataclass
class PodInfo:
    """包含 Pod 的基本简化信息。"""

    name: str
    namespace: str
    status: str
    node_name: Optional[str]
    pod_ip: Optional[str]
    labels: Dict[str, str] = field(default_factory=dict)
    creation_timestamp: Optional[str] = None
    containers: List[Dict[str, str]] = field(default_factory=list)


@dataclass
class PodContainerMetrics:
    """表示 Pod 中单个容器的资源使用指标或声明。"""

    container_name: str
    usage: ResourceValues


@dataclass
class PodMetricsReport:
    """
    包含单个 Pod 的完整资源指标报告。
    'pod_total_usage' 代表实际使用量 (CPU/Mem from Metrics Server, GPU from requests)。
    """

    pod_name: str
    namespace: str
    node_name: Optional[str]
    timestamp: Optional[str]
    window: Optional[str]
    containers: List[PodContainerMetrics]
    pod_total_usage: ResourceValues


@dataclass
class ServiceInfo:
    """包含 Service 的基本简化信息。"""

    name: str
    namespace: str
    type: str
    cluster_ip: Optional[str]
    external_ips: List[str] = field(default_factory=list)
    ports: List[Dict[str, Any]] = field(default_factory=list)
    selector: Dict[str, str] = field(default_factory=dict)
    labels: Dict[str, str] = field(default_factory=dict)
    creation_timestamp: Optional[str] = None


@dataclass
class ServiceTypeRequestsReport:
    """
    表示按服务类型（基于 Pod 标签）聚合的资源 *请求 (requests)* 情况。
    Corresponds to original get_service_type_usage method.
    """

    service_type_label_value: str
    requested_resources: ResourceValues
    pod_count: int


@dataclass
class PodRequirements:
    """定义一个 Pod 的资源 *请求 (requests)* 和基本调度约束。"""

    cpu_millicores: int
    memory_bytes: int
    gpu_count: int = 0
    labels: Optional[Dict[str, str]] = None
    node_selector: Optional[Dict[str, str]] = None


@dataclass
class NodeSchedulability:
    """评估一个 Pod 在特定节点上的可调度性。"""

    node_name: str
    is_schedulable: bool
    meets_resource_requirements: bool
    meets_node_selector: bool
    available_resources_after_pod: Optional[ResourceValues] = None
    reasons: List[str] = field(default_factory=list)


@dataclass
class OptimalNodeChoice:
    """表示为 Pod 选择的最优节点的结果。"""

    node_name: str
    score: float
    strategy_used: str
    details: NodeMetricsReport


# MODIFIED (from user's upload if it was ServiceTypeResourcePercentage):
# Renamed to be specific about being based on 'requests'
@dataclass
class ServiceTypeRequestsPercentage:
    """
    表示特定服务类型 Pod *请求 (requests)* 的资源占集群总可分配资源的百分比。
    Corresponds to original get_service_type_resource_percentage method.
    """

    service_type_label_value: str
    cpu_percentage: float
    memory_percentage: float
    gpu_percentage: float
    requested_resources: ResourceValues
    cluster_total_allocatable: ResourceValues


# --- Data classes for calculations based on a single defined Limit per Pod type ---
@dataclass
class DefinedLimitForPodType:
    """
    存储为一个Pod类型定义的（通常从第一个Pod实例获取的）单一Limit值。
    """

    cpu_limit_millicores: int = 0
    memory_limit_bytes: int = 0
    gpu_limit_count: int = 0
    # 记录这个limit是从哪个Pod获取的，以及是否有未定义的资源
    source_pod_name: Optional[str] = None
    has_undefined_cpu_limit: bool = True
    has_undefined_memory_limit: bool = True
    has_undefined_gpu_limit: bool = True


@dataclass
class ServiceTypeDefinedLimitOccupationReport:
    """
    表示按服务类型（基于 Pod 标签）计算的资源“占用”情况。
    占用 = Pod数量 * 为该类型定义的单个Pod的Limit值。
    """

    service_type_label_value: str
    defined_limit_per_pod: DefinedLimitForPodType  # 为该类型定义的单一Limit值
    pod_count: int
    calculated_occupation: ResourceValues  # pod_count * defined_limit_per_pod


@dataclass
class ServiceTypeDefinedLimitOccupationPercentage:
    """
    表示特定服务类型基于 (Pod数量 * 为该类型定义的单一Limit值) 计算的“占用”占集群总可分配资源的百分比。
    """

    service_type_label_value: str
    cpu_occupation_percentage: float
    memory_occupation_percentage: float
    gpu_occupation_percentage: float
    occupation_based_on_defined_limit: ResourceValues  # 分子
    defined_limit_per_pod_for_type: (
        DefinedLimitForPodType  # 用于计算分子的类型定义的单一limit值
    )
    pod_count_for_occupation_calc: int  # 用于计算分子的Pod数量
    cluster_total_allocatable: ResourceValues  # 分母


@dataclass
class SchedulingDecision:
    """
    封装调度器对一个新Pod的调度决策结果。
    主要通过 can_schedule (True/False) 来表示。
    如果 can_schedule 为 True，其他字段提供调度的上下文。
    """

    can_schedule: bool
    target_node_name: Optional[str] = None  # 如果 can_schedule 为 True，这是选定的节点
    # Pod 的资源设置将由调用方根据服务类型从预设配置中获取
    reason: Optional[str] = None  # 如果 can_schedule 为 False，这是失败原因
    is_quota_exception: bool = False  # 是否作为配额超限的“破例”情况调度的
