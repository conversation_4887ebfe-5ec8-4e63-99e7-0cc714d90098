# -*- coding: utf-8 -*-
"""
kube_scheduler_advisor.py

包含 Kubernetes Pod 调度预检查和最优节点选择逻辑。
此类方法接收预获取的集群状态数据进行分析。
"""
import logging
from typing import List, Dict, Optional, Tuple

try:
    from .kube_models import (
        ResourceValues,
        NodeInfo,
        NodeMetricsReport,
        PodRequirements,
        NodeSchedulability,
        OptimalNodeChoice,
        SchedulingDecision,
        ServiceTypeRequestsReport,
    )
except ImportError:
    from kube_models import (  # type: ignore
        ResourceValues,
        NodeInfo,
        NodeMetricsReport,
        PodRequirements,
        NodeSchedulability,
        OptimalNodeChoice,
        SchedulingDecision,
        ServiceTypeRequestsReport,
    )

logger = logging.getLogger(__name__)


# --- 辅助函数 ---
def _check_node_labels_match_selector(
    node_labels: Dict[str, str], node_selector: Optional[Dict[str, str]]
) -> bool:
    """
    检查节点标签是否满足所有节点选择器要求。

    Args:
        node_labels (Dict[str, str]): 节点的标签。
        node_selector (Optional[Dict[str, str]]): Pod 的节点选择器。

    Returns:
        bool: 如果满足选择器或选择器为空，则为 True；否则为 False。
    """
    if not node_selector:  # 如果没有选择器，则视为匹配
        return True
    if not node_labels:  # 如果节点没有标签但选择器存在，则不匹配
        return False
    for key, value in node_selector.items():
        if key not in node_labels or node_labels[key] != value:
            return False
    return True


class KubernetesSchedulerAdvisor:
    """
    提供 Pod 调度预检查和最优节点选择建议的类。
    此类的方法通常接收预先获取的集群数据作为输入。
    """

    def __init__(self):
        """
        初始化 KubernetesSchedulerAdvisor。
        目前此类是无状态的，构造函数可以为空。
        """
        pass

    def check_pod_schedulability_on_node(
        self,
        node_metrics: NodeMetricsReport,
        node_info: NodeInfo,
        pod_requirements: PodRequirements,
    ) -> NodeSchedulability:
        """
        检查给定的 PodRequirements 是否能在指定的单个节点上满足。

        Args:
            node_metrics (NodeMetricsReport): 目标节点的指标报告，必须包含有效的 `available` 资源。
            node_info (NodeInfo): 目标节点的基本信息，必须包含 `labels`。
            pod_requirements (PodRequirements): Pod 的资源需求和调度约束。

        Returns:
            NodeSchedulability: 包含可调度性评估结果的对象。
        """
        reasons: List[str] = []

        # 1. 检查资源需求
        available = node_metrics.available
        meets_resources = (
            available.cpu_millicores >= pod_requirements.cpu_millicores
            and available.memory_bytes >= pod_requirements.memory_bytes
            and available.gpu_count >= pod_requirements.gpu_count
        )
        if not meets_resources:
            if available.cpu_millicores < pod_requirements.cpu_millicores:
                reasons.append(
                    f"CPU不足 (需{pod_requirements.cpu_millicores}m, 可用{available.cpu_millicores}m)"
                )
            if available.memory_bytes < pod_requirements.memory_bytes:
                # 导入 format_memory_bytes (或者在调用方格式化)
                # 为了保持此模块独立，这里可以简单显示字节或要求调用方处理格式化
                reasons.append(
                    f"内存不足 (需{pod_requirements.memory_bytes}b, 可用{available.memory_bytes}b)"
                )
            if available.gpu_count < pod_requirements.gpu_count:
                reasons.append(
                    f"GPU不足 (需{pod_requirements.gpu_count}, 可用{available.gpu_count})"
                )

        # 2. 检查节点选择器
        meets_selector = _check_node_labels_match_selector(
            node_info.labels, pod_requirements.node_selector
        )
        if not meets_selector:
            reasons.append(
                f"不满足节点选择器 {pod_requirements.node_selector} (节点标签: {node_info.labels})"
            )

        # (未来可扩展: 检查污点和容忍、Pod 亲和性等)

        is_schedulable = meets_resources and meets_selector

        available_after_pod = None
        if is_schedulable:
            available_after_pod = ResourceValues(
                cpu_millicores=available.cpu_millicores
                - pod_requirements.cpu_millicores,
                memory_bytes=available.memory_bytes - pod_requirements.memory_bytes,
                gpu_count=available.gpu_count - pod_requirements.gpu_count,
            )

        return NodeSchedulability(
            node_name=node_info.name,
            is_schedulable=is_schedulable,
            meets_resource_requirements=meets_resources,
            meets_node_selector=meets_selector,
            available_resources_after_pod=available_after_pod,
            reasons=reasons,
        )

    def find_schedulable_nodes(
        self,
        all_node_metrics: List[NodeMetricsReport],
        all_node_info: List[NodeInfo],
        pod_requirements: PodRequirements,
    ) -> List[NodeSchedulability]:
        """
        检查集群中所有提供的节点，返回一个列表，标明每个节点对于给定 PodRequirements 的可调度性。

        Args:
            all_node_metrics (List[NodeMetricsReport]): 集群中所有相关节点的指标报告列表。
            all_node_info (List[NodeInfo]): 集群中所有相关节点的基本信息列表。
                                            应与 all_node_metrics 中的节点对应。
            pod_requirements (PodRequirements): Pod 的资源需求和调度约束。

        Returns:
            List[NodeSchedulability]: 包含每个被评估节点对该 Pod 评估结果的列表。
        """
        results: List[NodeSchedulability] = []

        # 创建 NodeInfo 的查找字典以便快速匹配
        node_info_map: Dict[str, NodeInfo] = {info.name: info for info in all_node_info}

        for metrics_report in all_node_metrics:
            node_name = metrics_report.node_name
            node_info = node_info_map.get(node_name)

            if not node_info:
                logger.warning(
                    f"在 find_schedulable_nodes 中找不到节点 '{node_name}' 的 NodeInfo，跳过此节点。"
                )
                results.append(
                    NodeSchedulability(
                        node_name=node_name,
                        is_schedulable=False,
                        meets_resource_requirements=False,
                        meets_node_selector=False,
                        reasons=[f"无法找到节点 '{node_name}' 的 NodeInfo 对象。"],
                    )
                )
                continue

            # 复用单节点检查逻辑
            schedulability_on_this_node = self.check_pod_schedulability_on_node(
                metrics_report, node_info, pod_requirements
            )
            results.append(schedulability_on_this_node)
        return results

    def find_optimal_node_for_pod(
        self,
        schedulable_evaluations: List[NodeSchedulability],
        node_metrics_map: Dict[str, NodeMetricsReport],
        pod_requirements: PodRequirements,
        strategy: str = "least_loaded_cpu",
    ) -> Optional[OptimalNodeChoice]:
        """
        根据指定的策略，为具有特定资源需求的 Pod 找到一个最优的候选节点。

        Args:
            schedulable_evaluations (List[NodeSchedulability]):
                `find_schedulable_nodes` 返回的评估结果中，那些 `is_schedulable=True` 的节点。
            node_metrics_map (Dict[str, NodeMetricsReport]):
                一个包含所有候选节点（至少是可调度节点）的 NodeMetricsReport 的字典，键为节点名。
                用于获取部署前的节点状态。
            pod_requirements (PodRequirements): Pod 的资源需求。
            strategy (str): 节点选择策略。支持: "least_loaded_cpu", "least_loaded_memory",
                            "most_available_gpu", "first_fit".

        Returns:
            Optional[OptimalNodeChoice]: 最优节点选择的结果，或在找不到合适节点时返回 None。
        """
        if not schedulable_evaluations:
            logger.info(f"没有可调度的节点传递给 find_optimal_node_for_pod。")
            return None

        optimal_node_name: Optional[str] = None
        best_score: float = (
            -1.0
        )  # 对于 "least_loaded" 和 "most_available", 分数越高越好

        if strategy == "first_fit":
            optimal_node_name = schedulable_evaluations[0].node_name
            best_score = 0  # 对于 first_fit, score 不重要

        elif strategy == "least_loaded_cpu":
            for node_eval in schedulable_evaluations:
                if node_eval.available_resources_after_pod:  # 部署后剩余
                    current_score = float(
                        node_eval.available_resources_after_pod.cpu_millicores
                    )
                    if optimal_node_name is None or current_score > best_score:
                        best_score = current_score
                        optimal_node_name = node_eval.node_name

        elif strategy == "least_loaded_memory":
            for node_eval in schedulable_evaluations:
                if node_eval.available_resources_after_pod:
                    current_score = float(
                        node_eval.available_resources_after_pod.memory_bytes
                    )
                    if optimal_node_name is None or current_score > best_score:
                        best_score = current_score
                        optimal_node_name = node_eval.node_name

        elif strategy == "most_available_gpu":
            if pod_requirements.gpu_count == 0:
                logger.info(
                    "Pod 不请求 GPU，'most_available_gpu' 策略将退化为 'first_fit'。"
                )
                optimal_node_name = schedulable_evaluations[0].node_name
                best_score = 0
            else:
                for node_eval in schedulable_evaluations:
                    # 使用部署前的可用 GPU 进行评分
                    node_metrics_for_gpu = node_metrics_map.get(node_eval.node_name)
                    if node_metrics_for_gpu:
                        current_score = float(node_metrics_for_gpu.available.gpu_count)
                        if optimal_node_name is None or current_score > best_score:
                            best_score = current_score
                            optimal_node_name = node_eval.node_name
        else:
            logger.warning(
                f"未知的最优节点选择策略: '{strategy}'. 将使用 'first_fit'。"
            )
            optimal_node_name = schedulable_evaluations[0].node_name
            strategy = "first_fit"  # 更新策略名称
            best_score = 0

        if optimal_node_name:
            final_optimal_node_metrics = node_metrics_map.get(optimal_node_name)
            if final_optimal_node_metrics:
                return OptimalNodeChoice(
                    node_name=optimal_node_name,
                    score=best_score,
                    strategy_used=strategy,
                    details=final_optimal_node_metrics,
                )
            else:
                logger.error(
                    f"无法为选定的最优节点 '{optimal_node_name}' 获取详细指标。"
                )

        return None

    def _preliminary_node_filter(
        self,
        new_pod_requirements: PodRequirements,
        all_node_metrics: List[NodeMetricsReport],
        all_node_info: List[NodeInfo],
    ) -> List[NodeMetricsReport]:  # 返回满足条件的节点的指标报告列表
        """
        初步筛选可用节点：基于新 Pod 的资源需求和节点选择器。

        此方法会遍历所有提供的节点指标和信息，筛选出那些
        既有足够可用资源满足新 Pod 需求，又符合新 Pod 节点选择器（如果定义了）的节点。

        Args:
            new_pod_requirements (PodRequirements): 新 Pod 的资源需求对象，包含 CPU、内存、GPU 需求
                                                  以及可选的 node_selector。
            all_node_metrics (List[NodeMetricsReport]): 集群中所有（或相关）节点的当前指标报告列表。
                                                      每个报告应包含节点的可用资源信息。
            all_node_info (List[NodeInfo]): 集群中所有（或相关）节点的基本信息列表。
                                            每个信息对象应包含节点的标签，用于匹配 node_selector。

        Returns:
            List[NodeMetricsReport]: 一个列表，包含所有通过初步筛选的候选节点的 NodeMetricsReport 对象。
                                     如果没有任何节点满足条件，则返回空列表。
        """
        candidate_node_metrics: List[NodeMetricsReport] = []
        node_info_map: Dict[str, NodeInfo] = {info.name: info for info in all_node_info}

        for metrics_report in all_node_metrics:
            node_name = metrics_report.node_name
            node_info_obj = node_info_map.get(node_name)

            if not node_info_obj:
                logger.warning(
                    f"初步筛选：在all_node_info中找不到节点 '{node_name}' 的信息，跳过。"
                )
                continue

            sufficient_resources = (
                metrics_report.available.cpu_millicores
                >= new_pod_requirements.cpu_millicores
                and metrics_report.available.memory_bytes
                >= new_pod_requirements.memory_bytes
                and metrics_report.available.gpu_count >= new_pod_requirements.gpu_count
            )
            if not sufficient_resources:
                logger.debug(
                    f"节点 '{node_name}' 资源不足。需要 CPU: {new_pod_requirements.cpu_millicores}m (可用: {metrics_report.available.cpu_millicores}m), "
                    f"内存: {new_pod_requirements.memory_bytes}b (可用: {metrics_report.available.memory_bytes}b)"
                )
                continue

            if new_pod_requirements.node_selector:
                if not _check_node_labels_match_selector(
                    node_info_obj.labels, new_pod_requirements.node_selector
                ):
                    logger.debug(
                        f"节点 '{node_name}' 不满足节点选择器 '{new_pod_requirements.node_selector}'。节点标签: {node_info_obj.labels}"
                    )
                    continue

            candidate_node_metrics.append(metrics_report)
            logger.debug(f"节点 '{node_name}' 通过初步筛选。")

        return candidate_node_metrics

    def make_scheduling_decision(
        self,
        new_pod_service_type: str,
        new_pod_standard_resources: PodRequirements,
        all_node_metrics: List[NodeMetricsReport],
        all_node_info: List[NodeInfo],
        all_current_service_requests_reports: Dict[str, ServiceTypeRequestsReport],
        cluster_allocatable_resources: ResourceValues,
        all_service_type_quotas: Dict[str, Tuple[float, float]],
        # ADDED: 新增参数，用于传递各服务类型待处理任务数
        pending_tasks_per_service: Dict[str, int],
    ) -> SchedulingDecision:
        """
        根据预定义的调度逻辑为新Pod提供调度建议。

        调度流程：
        1.  **检查服务资源配额**: 计算如果调度新Pod，其所属服务类型的总资源请求是否会超出预设配额。
        2.  **处理超额情况 (公平性检查)**: 如果超额，则判断是否可以“破例”调度。
            破例的条件是：所有其他活跃的服务类型（有Pod运行或有排队任务）也都资源饱和，
            或者其他服务类型虽然未饱和但没有排队等待的任务。
            如果存在其他服务类型既有排队任务又未饱和，则当前超额的Pod不能破例。
        3.  **初步筛选可用节点**: 如果配额检查通过（或允许破例），则从所有节点中筛选出
            那些当前可用资源满足新Pod需求且符合Pod节点选择器的节点。
        4.  **选定最佳节点**: 从通过筛选的候选节点中，选择一个当前剩余可用CPU资源最多的节点。
        5.  **返回决策**: 返回一个 SchedulingDecision 对象，指示是否可以调度，
            如果可以，则包含目标节点名称和是否为配额破例。

        Args:
            new_pod_service_type (str): 新Pod所属的服务类型字符串 (例如, "pdp_logsim")。
            new_pod_standard_resources (PodRequirements): 新Pod的标准资源需求对象，
                                                        代表该服务类型Pod应配置的requests。
            all_node_metrics (List[NodeMetricsReport]): 集群中所有（或相关）节点的当前指标报告列表。
                                                      每个报告应包含节点的可用资源信息。
            all_node_info (List[NodeInfo]): 集群中所有（或相关）节点的基本信息列表。
                                            每个信息对象应包含节点的标签。
            all_current_service_requests_reports (Dict[str, ServiceTypeRequestsReport]):
                一个字典，键为服务类型字符串，值为 ServiceTypeRequestsReport 对象，
                表示当前各服务类型所有Pod的资源请求(requests)总和。
            cluster_allocatable_resources (ResourceValues): 集群总可分配资源对象。
            all_service_type_quotas (Dict[str, Tuple[float, float]]):
                一个字典，键为服务类型字符串，值为一个元组 (cpu_quota_percentage, memory_quota_percentage)，
                包含所有已知服务类型的配额信息。
            pending_tasks_per_service (Dict[str, int]):
                一个字典，键为服务类型字符串，值为该服务类型当前待处理（排队）的任务数量。
                用于公平性检查。

        Returns:
            SchedulingDecision: 一个 SchedulingDecision 对象，封装了调度决策结果。
                                `can_schedule` 属性为 True 表示可以调度，此时 `target_node_name` 会被设置。
                                `can_schedule` 属性为 False 表示不可调度，此时 `reason` 会说明原因。
                                `is_quota_exception` 属性指示是否作为配额破例情况进行调度。
        """

        logger.info(
            f"开始为服务类型 '{new_pod_service_type}' 的新Pod进行调度决策。需求: "
            f"CPU {new_pod_standard_resources.cpu_millicores}m, "
            f"Mem {new_pod_standard_resources.memory_bytes}b, "
            f"GPU {new_pod_standard_resources.gpu_count}"
        )

        current_service_quotas = all_service_type_quotas.get(new_pod_service_type)
        if not current_service_quotas:
            return SchedulingDecision(
                can_schedule=False,
                reason=f"服务类型 '{new_pod_service_type}' 的配额百分比未在 all_service_type_quotas 中定义。",
            )
        service_type_quota_cpu_percent, service_type_quota_memory_percent = (
            current_service_quotas
        )

        # 步骤1: 检查服务资源配额
        current_type_report = all_current_service_requests_reports.get(
            new_pod_service_type
        )
        current_type_cpu_req = (
            current_type_report.requested_resources.cpu_millicores
            if current_type_report
            else 0
        )
        current_type_mem_req = (
            current_type_report.requested_resources.memory_bytes
            if current_type_report
            else 0
        )

        projected_cpu_req = (
            current_type_cpu_req + new_pod_standard_resources.cpu_millicores
        )
        projected_mem_req = (
            current_type_mem_req + new_pod_standard_resources.memory_bytes
        )

        abs_quota_limit_cpu = cluster_allocatable_resources.cpu_millicores * (
            service_type_quota_cpu_percent / 100.0
        )
        abs_quota_limit_memory = cluster_allocatable_resources.memory_bytes * (
            service_type_quota_memory_percent / 100.0
        )

        is_within_cpu_quota = True
        is_within_memory_quota = True
        quota_check_reasons: List[str] = []

        if cluster_allocatable_resources.cpu_millicores > 0:
            if projected_cpu_req > abs_quota_limit_cpu:
                is_within_cpu_quota = False
                quota_check_reasons.append(
                    f"CPU超额(预计{projected_cpu_req}m > 配额{abs_quota_limit_cpu:.0f}m)"
                )
        elif new_pod_standard_resources.cpu_millicores > 0:
            is_within_cpu_quota = False
            quota_check_reasons.append("Pod请求CPU但集群无CPU可分配")

        if cluster_allocatable_resources.memory_bytes > 0:
            if projected_mem_req > abs_quota_limit_memory:
                is_within_memory_quota = False
                quota_check_reasons.append(
                    f"内存超额(预计{projected_mem_req}b > 配额{abs_quota_limit_memory:.0f}b)"
                )
        elif new_pod_standard_resources.memory_bytes > 0:
            is_within_memory_quota = False
            quota_check_reasons.append("Pod请求内存但集群无内存可分配")

        is_within_quota = is_within_cpu_quota and is_within_memory_quota
        made_quota_exception = False

        # 步骤1.1: 处理超额情况 (公平性检查 - MODIFIED LOGIC)
        if not is_within_quota:
            combined_quota_reasons = "; ".join(quota_check_reasons)
            logger.info(
                f"服务类型 '{new_pod_service_type}' 调度新Pod将超额。原因: {combined_quota_reasons}. 检查是否可破例..."
            )

            can_make_exception = True
            # 新逻辑: 如果存在其他服务类型有排队任务 *并且* 其自身未饱和，则不能破例。
            for other_service_type, (
                other_cpu_quota_p,
                other_mem_quota_p,
            ) in all_service_type_quotas.items():
                if other_service_type == new_pod_service_type:
                    continue

                other_has_pending_tasks = (
                    pending_tasks_per_service.get(other_service_type, 0) > 0
                )

                # 如果其他服务没有排队任务，则它不阻止当前服务破例
                if not other_has_pending_tasks:
                    logger.debug(
                        f"  公平性检查：服务类型 '{other_service_type}' 无排队任务，不影响破例决策。"
                    )
                    continue

                # 如果其他服务有排队任务，再检查其是否饱和
                other_service_req_report = all_current_service_requests_reports.get(
                    other_service_type
                )
                if not (
                    other_service_req_report and other_service_req_report.pod_count > 0
                ):
                    # 这种情况理论上较少：有排队任务但没有当前运行的Pod或请求报告
                    # 我们可以保守地认为它不阻止破例，或者认为它“需要资源”
                    logger.debug(
                        f"  公平性检查：服务类型 '{other_service_type}' 有排队任务但无活跃Pod/请求报告，暂不影响破例决策。"
                    )
                    continue

                other_cpu_req = (
                    other_service_req_report.requested_resources.cpu_millicores
                )
                other_mem_req = (
                    other_service_req_report.requested_resources.memory_bytes
                )
                other_abs_quota_cpu = cluster_allocatable_resources.cpu_millicores * (
                    other_cpu_quota_p / 100.0
                )
                other_abs_quota_mem = cluster_allocatable_resources.memory_bytes * (
                    other_mem_quota_p / 100.0
                )

                # 定义“饱和”阈值
                is_other_cpu_saturated = (other_abs_quota_cpu == 0) or (
                    other_cpu_req >= other_abs_quota_cpu * 0.90
                )
                is_other_mem_saturated = (other_abs_quota_mem == 0) or (
                    other_mem_req >= other_abs_quota_mem * 0.90
                )

                if not (
                    is_other_cpu_saturated and is_other_mem_saturated
                ):  # 如果其他服务有排队任务 *且* 未饱和
                    logger.info(
                        f"  公平性检查：服务类型 '{other_service_type}' 有排队任务且未饱和 (CPU请求 {other_cpu_req}/{other_abs_quota_cpu:.0f}, Mem请求 {other_mem_req}/{other_abs_quota_mem:.0f})，不允许 '{new_pod_service_type}' 破例。"
                    )
                    can_make_exception = False
                    break

            if can_make_exception:
                logger.info(
                    f"  允许服务类型 '{new_pod_service_type}' 破例超额调度 (原因：其他服务无排队任务，或有排队任务但也已饱和)。"
                )
                made_quota_exception = True
            else:
                return SchedulingDecision(
                    can_schedule=False,
                    reason=f"服务类型 '{new_pod_service_type}' {combined_quota_reasons}. 其他有排队任务的服务未饱和，为保证公平拒绝调度。",
                )
        else:
            logger.info(f"服务类型 '{new_pod_service_type}' 在配额内。")

        # 步骤2: 初步筛选可用节点
        candidate_node_metrics = self._preliminary_node_filter(
            new_pod_standard_resources, all_node_metrics, all_node_info
        )

        if not candidate_node_metrics:
            logger.info(f"配额检查通过（或破例），但初步筛选后无可用节点。")
            return SchedulingDecision(
                can_schedule=False,
                reason=f"没有节点满足Pod '{new_pod_service_type}' 的初始资源需求或节点选择器（即使配额允许）。",
            )
        logger.info(
            f"配额检查通过（或破例），初步筛选后有 {len(candidate_node_metrics)} 个候选节点: {[n.node_name for n in candidate_node_metrics]}"
        )

        # 步骤3: 选定最佳节点
        best_node_name: Optional[str] = None
        max_available_cpu_on_candidate = -1.0

        for node_metric_report in candidate_node_metrics:
            if (
                node_metric_report.available.cpu_millicores
                > max_available_cpu_on_candidate
            ):
                max_available_cpu_on_candidate = float(
                    node_metric_report.available.cpu_millicores
                )
                best_node_name = node_metric_report.node_name

        if not best_node_name:
            logger.error(
                f"在候选节点中未能选定最佳节点，尽管候选列表不为空（异常情况）。候选节点: {[n.node_name for n in candidate_node_metrics]}"
            )
            return SchedulingDecision(
                can_schedule=False, reason=f"在候选节点中未能选定最佳节点。"
            )

        logger.info(
            f"为 Pod '{new_pod_service_type}' 选定节点 '{best_node_name}' (该节点当前可用CPU: {max_available_cpu_on_candidate}m)。"
        )

        # 步骤4: 返回决策
        return SchedulingDecision(
            can_schedule=True,
            target_node_name=best_node_name,
            is_quota_exception=made_quota_exception,
        )


if __name__ == "__main__":
    from mqtok8s.utils.kube_resource_monitor import (
        KubernetesResourceMonitor,
        format_memory_bytes,
    )

    # 配置日志以便观察调度逻辑的详细步骤
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    )
    logger.info("开始 kube_scheduler_advisor.py 单模块测试运行...")

    # 1. 模拟/定义 SERVICE_CONFIGS (服务画像和配额)
    #    在实际应用中，这部分配置会从外部加载
    SERVICE_CONFIGS = {
        "pdp_logsim": {  # 类型A
            "standard_resources": PodRequirements(
                cpu_millicores=500, memory_bytes=1 * 1024 * 1024 * 1024, gpu_count=0
            ),  # 0.5 CPU, 1Gi Mem
            "quota_cpu_percent": 40.0,
            "quota_memory_percent": 40.0,
        },
        "pnc_logsim": {  # 类型B
            "standard_resources": PodRequirements(
                cpu_millicores=300, memory_bytes=512 * 1024 * 1024, gpu_count=0
            ),  # 0.3 CPU, 512Mi Mem
            "quota_cpu_percent": 60.0,
            "quota_memory_percent": 60.0,
        },
        "pnc_worldsim": {
            "standard_resources": PodRequirements(
                cpu_millicores=1000, memory_bytes=2 * 1024 * 1024 * 1024, gpu_count=0
            ),  # 1 CPU, 2Gi Mem
            "quota_cpu_percent": 50.0,
            "quota_memory_percent": 50.0,
        },
    }
    ALL_SERVICE_TYPE_QUOTAS_FOR_FAIRNESS: Dict[str, Tuple[float, float]] = {
        stype: (config["quota_cpu_percent"], config["quota_memory_percent"])
        for stype, config in SERVICE_CONFIGS.items()
    }
    logger.info(f"服务配置加载: {len(SERVICE_CONFIGS)} 种类型。")

    # 2. 模拟/定义待处理任务队列
    MOCKED_PENDING_TASKS = {
        "pdp_logsim": 0,
        "pnc_logsim": 2,  # pnc_logsim 有排队任务
        "pnc_worldsim": 0,
    }
    logger.info(f"模拟的待处理任务队列: {MOCKED_PENDING_TASKS}")

    # 3. 初始化 KubernetesResourceMonitor 以获取真实或模拟的集群数据
    #    注意: 为了真正独立测试 advisor，理想情况是完全 mock monitor 返回的数据。
    #    这里我们仍然尝试连接集群，如果失败，测试可能无法完整进行。
    try:
        monitor = KubernetesResourceMonitor()  # 假设 kubeconfig 可用
        logger.info("KubernetesResourceMonitor 初始化成功。")
    except Exception as e:
        logger.error(
            f"无法初始化 KubernetesResourceMonitor: {e}。测试将使用空数据，可能无法准确反映调度逻辑。"
        )
        # 提供一些空的默认值，以便代码至少能运行，但结果可能无意义
        monitor = None

    # 4. 获取集群数据 (如果 monitor 初始化失败，这些会是 None 或空列表)
    all_nodes_metrics: List[NodeMetricsReport] = []
    all_nodes_info: List[NodeInfo] = []
    current_service_req_reports: Dict[str, ServiceTypeRequestsReport] = {}
    cluster_alloc_resources: Optional[ResourceValues] = None

    if monitor:
        logger.info("尝试从集群获取实时数据...")
        all_nodes_metrics = monitor.get_node_metrics(include_allocatable=True) or []
        all_nodes_info = monitor.list_nodes(simplify=True) or []
        # 假设 Pods 使用 'app-type' 标签来区分服务类型
        current_service_req_reports = (
            monitor.get_service_type_requests_aggregation(service_label_key="app-type")
            or {}
        )
        cluster_alloc_resources = monitor.get_cluster_allocatable_resources()

        if not all_nodes_metrics:
            logger.warning("未能获取到节点指标数据。")
        if not all_nodes_info:
            logger.warning("未能获取到节点信息列表。")
        if not cluster_alloc_resources:
            logger.error("未能获取到集群总可分配资源！测试可能不准确。")

    if not cluster_alloc_resources:  # 如果无法获取集群资源，提供一个模拟值以继续测试
        logger.warning("由于无法获取集群总可分配资源，将使用模拟值进行测试。")
        cluster_alloc_resources = ResourceValues(
            cpu_millicores=8000, memory_bytes=16 * 1024 * 1024 * 1024, gpu_count=0
        )  # 模拟 8 CPU, 16Gi Mem

    if (
        not all_nodes_metrics and all_nodes_info
    ):  # 如果没有metrics但有node info, 尝试构建基础的metrics
        logger.warning(
            "没有节点指标，将基于节点可分配资源构建模拟指标（仅含available和allocatable）。"
        )
        for nodeinfo_item in all_nodes_info:
            if monitor:  # 尝试获取单个节点的allocatable
                node_obj_for_alloc = monitor._get_node_obj(
                    nodeinfo_item.name
                )  # 使用受保护成员以便测试
                if (
                    node_obj_for_alloc
                    and node_obj_for_alloc.status
                    and node_obj_for_alloc.status.allocatable
                ):
                    alloc = node_obj_for_alloc.status.allocatable
                    alloc_val = ResourceValues(
                        monitor.parse_cpu_to_millicores(alloc.get("cpu")),
                        monitor.parse_memory_to_bytes(alloc.get("memory")),
                        int(alloc.get(monitor.gpu_resource_name, "0")),
                    )
                    # 假设当前无使用 (usage=0)，则 available = allocatable
                    all_nodes_metrics.append(
                        NodeMetricsReport(
                            nodeinfo_item.name,
                            None,
                            None,
                            ResourceValues(0, 0, 0),
                            alloc_val,
                            alloc_val,
                        )
                    )
                else:  # 无法获取allocatable，模拟一个
                    dummy_alloc = ResourceValues(2000, 4 * 1024 * 1024 * 1024, 0)
                    all_nodes_metrics.append(
                        NodeMetricsReport(
                            nodeinfo_item.name,
                            None,
                            None,
                            ResourceValues(0, 0, 0),
                            dummy_alloc,
                            dummy_alloc,
                        )
                    )
            else:  # monitor也无法初始化的情况
                dummy_alloc = ResourceValues(
                    2000, 4 * 1024 * 1024 * 1024, 0
                )  # 2 CPU, 4Gi
                all_nodes_metrics.append(
                    NodeMetricsReport(
                        nodeinfo_item.name,
                        None,
                        None,
                        ResourceValues(0, 0, 0),
                        dummy_alloc,
                        dummy_alloc,
                    )
                )

    # 5. 初始化调度顾问
    advisor = KubernetesSchedulerAdvisor()

    # 6. 模拟调度一个 "pdp_logsim" 类型的 Pod
    new_pod_to_schedule_type = "pdp_logsim"
    logger.info(f"\n--- 模拟调度 Pod 类型: '{new_pod_to_schedule_type}' ---")

    if new_pod_to_schedule_type not in SERVICE_CONFIGS:
        logger.error(f"错误: 服务类型 '{new_pod_to_schedule_type}' 的配置未找到。")
    elif not all_nodes_metrics or not all_nodes_info:
        logger.error("错误: 无法获取足够的节点信息或指标来进行调度决策。")
    else:
        pod_config = SERVICE_CONFIGS[new_pod_to_schedule_type]
        new_pod_reqs_from_config = pod_config["standard_resources"]

        decision = advisor.make_scheduling_decision(
            new_pod_service_type=new_pod_to_schedule_type,
            new_pod_standard_resources=new_pod_reqs_from_config,
            all_node_metrics=all_nodes_metrics,
            all_node_info=all_nodes_info,
            all_current_service_requests_reports=current_service_req_reports,
            cluster_allocatable_resources=cluster_alloc_resources,  # 使用已获取或模拟的值
            all_service_type_quotas=ALL_SERVICE_TYPE_QUOTAS_FOR_FAIRNESS,
            pending_tasks_per_service=MOCKED_PENDING_TASKS,
        )

        print("\n--- 调度决策结果 ---")
        if decision.can_schedule:
            print(f"  决策: 可以调度 Pod 类型 '{new_pod_to_schedule_type}'")
            print(f"    目标节点: {decision.target_node_name}")
            # 调用方负责使用 new_pod_reqs_from_config 来设置Pod的requests和limits
            print(
                f"    Pod 应配置资源 (来自预设配置): CPU {new_pod_reqs_from_config.cpu_millicores}m, Mem {format_memory_bytes(new_pod_reqs_from_config.memory_bytes)}"
            )
            if decision.is_quota_exception:
                print("    注意: 此调度是作为配额超限的破例情况。")
        else:
            print(f"  决策: 无法调度 Pod 类型 '{new_pod_to_schedule_type}'")
            print(f"    原因: {decision.reason}")

    # 7. 模拟调度一个 "pnc_logsim" 类型的 Pod (它有排队任务，且配额可能不同)
    new_pod_to_schedule_type_2 = "pnc_logsim"
    logger.info(f"\n--- 模拟调度 Pod 类型: '{new_pod_to_schedule_type_2}' ---")
    if new_pod_to_schedule_type_2 not in SERVICE_CONFIGS:
        logger.error(f"错误: 服务类型 '{new_pod_to_schedule_type_2}' 的配置未找到。")
    elif not all_nodes_metrics or not all_nodes_info:
        logger.error("错误: 无法获取足够的节点信息或指标来进行调度决策。")
    else:
        pod_config_2 = SERVICE_CONFIGS[new_pod_to_schedule_type_2]
        new_pod_reqs_from_config_2 = pod_config_2["standard_resources"]

        decision_2 = advisor.make_scheduling_decision(
            new_pod_service_type=new_pod_to_schedule_type_2,
            new_pod_standard_resources=new_pod_reqs_from_config_2,
            all_node_metrics=all_nodes_metrics,
            all_node_info=all_nodes_info,
            all_current_service_requests_reports=current_service_req_reports,
            cluster_allocatable_resources=cluster_alloc_resources,
            all_service_type_quotas=ALL_SERVICE_TYPE_QUOTAS_FOR_FAIRNESS,
            pending_tasks_per_service=MOCKED_PENDING_TASKS,
        )
        print("\n--- 调度决策结果 (类型 pnc_logsim) ---")
        if decision_2.can_schedule:
            print(f"  决策: 可以调度 Pod 类型 '{new_pod_to_schedule_type_2}'")
            print(f"    目标节点: {decision_2.target_node_name}")
            print(
                f"    Pod 应配置资源: CPU {new_pod_reqs_from_config_2.cpu_millicores}m, Mem {format_memory_bytes(new_pod_reqs_from_config_2.memory_bytes)}"
            )
            if decision_2.is_quota_exception:
                print("    注意: 此调度是作为配额超限的破例情况。")
        else:
            print(f"  决策: 无法调度 Pod 类型 '{new_pod_to_schedule_type_2}'")
            print(f"    原因: {decision_2.reason}")

    logger.info("kube_scheduler_advisor.py 单模块测试运行结束。")
