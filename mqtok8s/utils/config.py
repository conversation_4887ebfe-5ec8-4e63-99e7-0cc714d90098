import os
import yaml
import json
from .log import logger

# 默认配置文件路径
DEFAULT_CONFIG_PATH = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'config.yaml')

# 配置缓存
_config_cache = {}

def load_config(config_path=None):
    """加载配置文件
    
    Args:
        config_path: 配置文件路径，默认为项目根目录下的config.yaml
    
    Returns:
        dict: 配置字典
    """
    config_path = config_path or DEFAULT_CONFIG_PATH
    
    if config_path in _config_cache:
        return _config_cache[config_path]
    
    try:
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        _config_cache[config_path] = config
        logger.info(f"成功加载配置文件: {config_path}")
        return config
    except Exception as e:
        logger.error(f"加载配置文件失败: {str(e)}")
        logger.print_exc()
        return {}

def get_value(section, key, config_path=None, default=None):
    """获取配置值
    
    Args:
        section: 配置节
        key: 配置键
        config_path: 配置文件路径
        default: 默认值
        
    Returns:
        配置值
    """
    config = load_config(config_path)
    try:
        return config.get(section, {}).get(key, default)
    except Exception as e:
        logger.error(f"获取配置项失败 [{section}].[{key}]: {str(e)}")
        return default
        
def save_config(config, config_path=None):
    """保存配置到文件
    
    Args:
        config: 配置字典
        config_path: 配置文件路径
    """
    config_path = config_path or DEFAULT_CONFIG_PATH
    try:
        with open(config_path, 'w') as f:
            yaml.dump(config, f)
        _config_cache[config_path] = config
        logger.info(f"成功保存配置文件: {config_path}")
        return True
    except Exception as e:
        logger.error(f"保存配置文件失败: {str(e)}")
        logger.print_exc()
        return False 