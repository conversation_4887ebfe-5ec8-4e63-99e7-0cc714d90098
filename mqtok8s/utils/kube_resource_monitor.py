# -*- coding: utf-8 -*-
"""
kube_resource_monitor.py

一个用于监控 Kubernetes 集群资源使用情况的 Python 模块。
主要利用 Kubernetes Metrics Server API 获取实时的节点和 Pod 资源使用数据，
并结合 Kubernetes Core API 获取集群的资源分配情况、对象列表和 Pod 的资源请求。
使用 kube_models.py 中定义的数据类进行输入和输出。
"""
import re
import logging
from typing import List, Dict, Optional, Union, Any

from kubernetes import client, config
from kubernetes.client.rest import ApiException

# 导入数据模型和新的调度顾问类
try:
    from .kube_models import (
        ResourceValues,
        NodeInfo,
        NodeMetricsReport,
        PodInfo,
        PodContainerMetrics,
        PodMetricsReport,
        ServiceInfo,
        PodRequirements,
        ServiceTypeDefinedLimitOccupationReport,
        ServiceTypeDefinedLimitOccupationPercentage,
        DefinedLimitForPodType,
    )
    from .kube_scheduler_advisor import KubernetesSchedulerAdvisor
except ImportError:
    from kube_models import (
        ResourceValues,
        NodeInfo,
        NodeMetricsReport,
        PodInfo,
        PodContainerMetrics,
        PodMetricsReport,
        ServiceInfo,
        PodRequirements,
        ServiceTypeDefinedLimitOccupationReport,
        ServiceTypeDefinedLimitOccupationPercentage,
        DefinedLimitForPodType,
    )
    from kube_scheduler_advisor import KubernetesSchedulerAdvisor


# --- 配置与常量 ---
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

METRICS_API_GROUP = "metrics.k8s.io"
METRICS_API_VERSION = "v1beta1"
DEFAULT_GPU_RESOURCE_NAME = "nvidia.com/gpu"

# --- 辅助函数：解析与格式化 (保留在此模块或未来移至 utils) ---


def parse_cpu_to_millicores(cpu_str: Optional[str]) -> int:
    """
    将 Kubernetes CPU 字符串 (如 "100m", "1", "500n") 解析为毫核心 (millicores)。
    """
    if not cpu_str:
        return 0
    cpu_str = str(cpu_str)
    try:
        if cpu_str.endswith("n"):
            return int(cpu_str[:-1]) // 1000000
        if cpu_str.endswith("u"):
            return int(cpu_str[:-1]) // 1000
        if cpu_str.endswith("m"):
            return int(cpu_str[:-1])
        if cpu_str.isdigit():
            return int(cpu_str) * 1000
        return int(float(cpu_str) * 1000)
    except ValueError:
        logger.debug(f"无法解析 CPU 字符串 '{cpu_str}'，返回 0。")
        return 0


def parse_memory_to_bytes(memory_str: Optional[str]) -> int:
    """
    将 Kubernetes 内存字符串 (如 "128Mi", "2Gi", "500Ki", "1024") 解析为字节数。
    """
    if not memory_str:
        return 0
    memory_str = str(memory_str)
    multipliers = {
        "k": 10**3,
        "m": 10**6,
        "g": 10**9,
        "t": 10**12,
        "p": 10**15,
        "e": 10**18,
        "ki": 1024**1,
        "mi": 1024**2,
        "gi": 1024**3,
        "ti": 1024**4,
        "pi": 1024**5,
        "ei": 1024**6,
    }
    match = re.fullmatch(r"(\d+)([eEa-zA-Z]*)", memory_str)
    if not match:
        if memory_str.isdigit():
            return int(memory_str)
        logger.debug(f"无法解析内存字符串 '{memory_str}'，返回 0。")
        return 0
    value_str, unit_str = match.groups()
    value = int(value_str)
    unit_lower = unit_str.lower()
    if unit_lower in multipliers:
        return value * multipliers[unit_lower]
    if not unit_lower and value:
        return value
    logger.debug(f"未知内存单位 '{unit_str}' 来自 '{memory_str}'，返回 0。")
    return 0


def format_memory_bytes(bytes_val: Optional[int], precision: int = 2) -> str:
    """
    将字节数格式化为易读的 Kubernetes 内存字符串 (Bytes, Ki, Mi, Gi, Ti)。
    """
    if bytes_val is None or bytes_val < 0:
        return "N/A"
    if bytes_val == 0:
        return "0Bytes"
    units = ["Bytes", "Ki", "Mi", "Gi", "Ti", "Pi", "Ei"]
    unit_index = 0
    num = float(bytes_val)
    while num >= 1024 and unit_index < len(units) - 1:
        num /= 1024
        unit_index += 1
    if units[unit_index] == "Bytes":
        return f"{int(num)}{units[unit_index]}"
    return f"{num:.{precision}f}{units[unit_index]}"


def _calculate_available_resources(
    allocatable: ResourceValues, usage: ResourceValues
) -> ResourceValues:
    """计算节点的可用资源 (allocatable - usage)。"""
    return ResourceValues(
        cpu_millicores=max(0, allocatable.cpu_millicores - usage.cpu_millicores),
        memory_bytes=max(0, allocatable.memory_bytes - usage.memory_bytes),
        gpu_count=max(0, allocatable.gpu_count - usage.gpu_count),
    )


class KubernetesResourceMonitor:
    """
    用于监控 Kubernetes 集群资源的核心类。
    """

    def __init__(
        self,
        kubeconfig_path: Optional[str] = None,
        in_cluster: bool = False,
        gpu_resource_name: str = DEFAULT_GPU_RESOURCE_NAME,
    ):
        """
        初始化 KubernetesResourceMonitor。
        """
        try:
            if in_cluster:
                config.load_incluster_config()
                logger.info("使用集群内部配置初始化 Kubernetes 客户端。")
            elif kubeconfig_path:
                config.load_kube_config(config_file=kubeconfig_path)
                logger.info(
                    f"从路径 '{kubeconfig_path}' 加载 kubeconfig 初始化 Kubernetes 客户端。"
                )
            else:
                config.load_kube_config()
                logger.info("从默认路径加载 kubeconfig 初始化 Kubernetes 客户端。")
        except config.ConfigException as e:
            logger.error(f"无法加载 Kubernetes 配置: {e}")
            raise RuntimeError(f"无法加载 Kubernetes 配置: {e}") from e

        self.core_v1_api = client.CoreV1Api()
        self.custom_objects_api = client.CustomObjectsApi()
        self.gpu_resource_name = gpu_resource_name
        self._node_cache: Dict[str, client.V1Node] = {}

    def _get_node_obj(self, node_name: str) -> Optional[client.V1Node]:
        """获取单个节点对象，使用缓存。"""
        if node_name in self._node_cache:
            return self._node_cache[node_name]
        try:
            node_obj = self.core_v1_api.read_node(name=node_name)
            self._node_cache[node_name] = node_obj
            return node_obj
        except ApiException as e:
            logger.warning(f"获取节点对象 '{node_name}' 失败: {e.status} - {e.reason}")
            return None

    def _clear_node_cache(self):
        """清空节点缓存。"""
        self._node_cache = {}

    def _simplify_node_info(self, v1_node: client.V1Node) -> NodeInfo:
        """将 V1Node 对象转换为 NodeInfo 数据类。"""
        status = "Unknown"
        if v1_node.status and v1_node.status.conditions:
            for condition in v1_node.status.conditions:
                if condition.type == "Ready":
                    status = "Ready" if condition.status == "True" else "NotReady"
                    if condition.reason:
                        status = f"{status} ({condition.reason})"
                    break
        internal_ip = "N/A"
        if v1_node.status and v1_node.status.addresses:
            for addr in v1_node.status.addresses:
                if addr.type == "InternalIP":
                    internal_ip = addr.address
                    break

        return NodeInfo(
            name=v1_node.metadata.name,
            status=status,
            internal_ip=internal_ip,
            kubelet_version=(
                v1_node.status.node_info.kubelet_version
                if v1_node.status and v1_node.status.node_info
                else "N/A"
            ),
            os_image=(
                v1_node.status.node_info.os_image
                if v1_node.status and v1_node.status.node_info
                else "N/A"
            ),
            architecture=(
                v1_node.status.node_info.architecture
                if v1_node.status and v1_node.status.node_info
                else "N/A"
            ),
            labels=v1_node.metadata.labels or {},
            creation_timestamp=(
                v1_node.metadata.creation_timestamp.isoformat()
                if v1_node.metadata.creation_timestamp
                else None
            ),
        )

    def _simplify_pod_info(self, v1_pod: client.V1Pod) -> PodInfo:
        """将 V1Pod 对象转换为 PodInfo 数据类。"""
        return PodInfo(
            name=v1_pod.metadata.name,
            namespace=v1_pod.metadata.namespace,
            status=v1_pod.status.phase if v1_pod.status else "Unknown",
            node_name=v1_pod.spec.node_name,
            pod_ip=v1_pod.status.pod_ip if v1_pod.status else None,
            labels=v1_pod.metadata.labels or {},
            creation_timestamp=(
                v1_pod.metadata.creation_timestamp.isoformat()
                if v1_pod.metadata.creation_timestamp
                else None
            ),
            containers=(
                [{"name": c.name, "image": c.image} for c in v1_pod.spec.containers]
                if v1_pod.spec
                else []
            ),
        )

    def _simplify_service_info(self, v1_service: client.V1Service) -> ServiceInfo:
        """将 V1Service 对象转换为 ServiceInfo 数据类。"""
        ports_info = []
        if v1_service.spec and v1_service.spec.ports:
            for p in v1_service.spec.ports:
                ports_info.append(
                    {
                        "name": p.name,
                        "port": p.port,
                        "protocol": p.protocol,
                        "target_port": p.target_port,
                        "node_port": (
                            p.node_port
                            if hasattr(p, "node_port") and p.node_port
                            else None
                        ),
                    }
                )
        return ServiceInfo(
            name=v1_service.metadata.name,
            namespace=v1_service.metadata.namespace,
            type=v1_service.spec.type if v1_service.spec else "Unknown",
            cluster_ip=v1_service.spec.cluster_ip if v1_service.spec else None,
            external_ips=(
                v1_service.spec.external_i_ps
                if v1_service.spec and hasattr(v1_service.spec, "external_i_ps")
                else []
            ),
            ports=ports_info,
            selector=v1_service.spec.selector or {} if v1_service.spec else {},
            labels=v1_service.metadata.labels or {},
            creation_timestamp=(
                v1_service.metadata.creation_timestamp.isoformat()
                if v1_service.metadata.creation_timestamp
                else None
            ),
        )

    def get_cluster_allocatable_resources(self) -> Optional[ResourceValues]:
        """
        获取整个集群的总可分配资源 (CPU, 内存, GPU)。
        (文档字符串同前)
        """
        total_cpu_millicores = 0
        total_memory_bytes = 0
        total_gpu_count = 0
        self._clear_node_cache()

        try:
            nodes_list_obj = self.core_v1_api.list_node()
            for node in nodes_list_obj.items:
                self._node_cache[node.metadata.name] = node
                if node.status and node.status.allocatable:
                    allocatable = node.status.allocatable
                    total_cpu_millicores += parse_cpu_to_millicores(
                        allocatable.get("cpu", "0")
                    )
                    total_memory_bytes += parse_memory_to_bytes(
                        allocatable.get("memory", "0")
                    )
                    total_gpu_count += int(allocatable.get(self.gpu_resource_name, "0"))
            return ResourceValues(
                total_cpu_millicores, total_memory_bytes, total_gpu_count
            )
        except ApiException as e:
            logger.error(f"获取集群可分配资源失败: {e.status} - {e.reason}")
            return None

    def list_nodes(
        self,
        label_selector: Optional[str] = None,
        field_selector: Optional[str] = None,
        simplify: bool = True,
    ) -> Optional[List[Union[NodeInfo, client.V1Node]]]:
        """
        列出集群中的 Node 对象。
        (文档字符串同前)
        """
        self._clear_node_cache()
        try:
            node_list_obj = self.core_v1_api.list_node(
                label_selector=label_selector, field_selector=field_selector
            )
            for node in node_list_obj.items:
                self._node_cache[node.metadata.name] = node
            if simplify:
                return [self._simplify_node_info(node) for node in node_list_obj.items]
            return node_list_obj.items
        except ApiException as e:
            logger.error(f"列出节点失败: {e.status} - {e.reason}")
            return None

    def get_node_metrics(
        self, node_name: Optional[str] = None, include_allocatable: bool = True
    ) -> Union[Optional[NodeMetricsReport], Optional[List[NodeMetricsReport]]]:
        """
        获取一个或所有节点的资源指标报告。
        报告包含使用量、可分配量以及计算出的可用资源。
        """
        try:
            if node_name:
                metrics_data_items = [
                    self.custom_objects_api.get_cluster_custom_object(
                        group=METRICS_API_GROUP,
                        version=METRICS_API_VERSION,
                        plural="nodes",
                        name=node_name,
                    )
                ]
            else:
                metrics_list_data = self.custom_objects_api.list_cluster_custom_object(
                    group=METRICS_API_GROUP, version=METRICS_API_VERSION, plural="nodes"
                )
                metrics_data_items = metrics_list_data.get("items", [])

            if not metrics_data_items:
                logger.info("从 Metrics Server 未获取到节点指标数据。")
                return None if node_name else []

            node_gpu_requests_map: Dict[str, int] = {}
            try:
                all_pods_for_gpu_calc = self.core_v1_api.list_pod_for_all_namespaces(
                    field_selector="status.phase=Running"
                ).items
                for pod in all_pods_for_gpu_calc:
                    if pod.spec.node_name:
                        gpu_on_pod = sum(
                            int(c.resources.requests.get(self.gpu_resource_name, "0"))
                            for c in pod.spec.containers
                            if c.resources and c.resources.requests
                        )
                        node_gpu_requests_map[pod.spec.node_name] = (
                            node_gpu_requests_map.get(pod.spec.node_name, 0)
                            + gpu_on_pod
                        )
            except ApiException as e_pods:
                logger.warning(
                    f"获取 Pods 用于 GPU 计算失败: {e_pods.status} - {e_pods.reason}"
                )

            results: List[NodeMetricsReport] = []
            for metrics_item in metrics_data_items:
                current_node_name = metrics_item["metadata"]["name"]
                usage_dict = metrics_item.get("usage", {})
                usage_resources = ResourceValues(
                    parse_cpu_to_millicores(usage_dict.get("cpu")),
                    parse_memory_to_bytes(usage_dict.get("memory")),
                    node_gpu_requests_map.get(current_node_name, 0),
                )
                allocatable_resources = ResourceValues(0, 0, 0)
                if include_allocatable:
                    node_obj = self._get_node_obj(current_node_name)
                    if node_obj and node_obj.status and node_obj.status.allocatable:
                        alloc = node_obj.status.allocatable
                        allocatable_resources = ResourceValues(
                            parse_cpu_to_millicores(alloc.get("cpu")),
                            parse_memory_to_bytes(alloc.get("memory")),
                            int(alloc.get(self.gpu_resource_name, "0")),
                        )
                    else:
                        logger.warning(
                            f"无法获取节点 '{current_node_name}' 的可分配资源。"
                        )

                available_resources = _calculate_available_resources(
                    allocatable_resources, usage_resources
                )
                results.append(
                    NodeMetricsReport(
                        current_node_name,
                        metrics_item.get("timestamp"),
                        metrics_item.get("window"),
                        usage_resources,
                        (
                            allocatable_resources
                            if include_allocatable
                            else ResourceValues(0, 0, 0)
                        ),
                        available_resources,
                    )
                )
            return results[0] if node_name and results else results
        except ApiException as e:
            if e.status == 404:
                logger.error(
                    f"Metrics Server API ({METRICS_API_GROUP}/{METRICS_API_VERSION}/nodes) 未找到或无数据。"
                )
            else:
                logger.error(f"获取节点指标失败: {e.status} - {e.reason} - {e.body}")
            return None

    def list_pods(
        self,
        namespace: Optional[str] = None,
        all_namespaces: bool = False,
        label_selector: Optional[str] = None,
        field_selector: Optional[str] = None,
        simplify: bool = True,
    ) -> Optional[List[Union[PodInfo, client.V1Pod]]]:
        """
        列出集群中的 Pod 对象。
        """
        try:
            if all_namespaces:
                pod_list_obj = self.core_v1_api.list_pod_for_all_namespaces(
                    label_selector=label_selector, field_selector=field_selector
                )
            else:
                current_namespace = namespace
                if not current_namespace:
                    try:
                        _, active_context = config.list_kube_config_contexts()
                        current_namespace = active_context.get("context", {}).get(
                            "namespace", "default"
                        )
                    except config.ConfigException:
                        current_namespace = "default"
                        logger.info(
                            "无法从 kubeconfig 获取当前命名空间，使用 'default'。"
                        )
                pod_list_obj = self.core_v1_api.list_namespaced_pod(
                    namespace=current_namespace,
                    label_selector=label_selector,
                    field_selector=field_selector,
                )
            if simplify:
                return [self._simplify_pod_info(pod) for pod in pod_list_obj.items]
            return pod_list_obj.items
        except ApiException as e:
            logger.error(f"列出 Pod 失败: {e.status} - {e.reason}")
            return None

    def get_pod_metrics(
        self,
        namespace: str,
        pod_name: Optional[str] = None,
        all_pods_in_namespace: bool = False,
    ) -> Union[Optional[PodMetricsReport], Optional[List[PodMetricsReport]]]:
        """
        获取一个或多个 Pod 的资源使用指标报告。
        """
        if not pod_name and not all_pods_in_namespace:
            logger.error(
                "获取 Pod 指标错误: 必须指定 pod_name 或设置 all_pods_in_namespace=True。"
            )
            return None
        try:
            if pod_name and not all_pods_in_namespace:
                metrics_data_items = [
                    self.custom_objects_api.get_namespaced_custom_object(
                        group=METRICS_API_GROUP,
                        version=METRICS_API_VERSION,
                        namespace=namespace,
                        plural="pods",
                        name=pod_name,
                    )
                ]
            else:
                pod_metrics_list_data = (
                    self.custom_objects_api.list_namespaced_custom_object(
                        group=METRICS_API_GROUP,
                        version=METRICS_API_VERSION,
                        namespace=namespace,
                        plural="pods",
                    )
                )
                metrics_data_items = pod_metrics_list_data.get("items", [])

            if not metrics_data_items:
                logger.info(
                    f"从 Metrics Server 未获取到命名空间 '{namespace}' 的 Pod 指标数据。"
                )
                return None if (pod_name and not all_pods_in_namespace) else []

            results: List[PodMetricsReport] = []
            for metrics_item in metrics_data_items:
                current_pod_name = metrics_item["metadata"]["name"]
                node_name_for_pod = "N/A"
                try:
                    pod_obj = self.core_v1_api.read_namespaced_pod(
                        name=current_pod_name, namespace=namespace
                    )
                    node_name_for_pod = pod_obj.spec.node_name
                except ApiException:
                    logger.debug(
                        f"无法获取 Pod '{namespace}/{current_pod_name}' 的详细信息以确定 node_name。"
                    )

                containers_metrics_list: List[PodContainerMetrics] = []
                pod_total_cpu, pod_total_memory = 0, 0
                for container_metric in metrics_item.get("containers", []):
                    usage = container_metric.get("usage", {})
                    cpu_m, mem_b = parse_cpu_to_millicores(
                        usage.get("cpu")
                    ), parse_memory_to_bytes(usage.get("memory"))
                    containers_metrics_list.append(
                        PodContainerMetrics(
                            container_metric.get("name", "UnknownContainer"),
                            ResourceValues(cpu_m, mem_b),
                        )
                    )
                    pod_total_cpu += cpu_m
                    pod_total_memory += mem_b
                results.append(
                    PodMetricsReport(
                        current_pod_name,
                        namespace,
                        node_name_for_pod,
                        metrics_item.get("timestamp"),
                        metrics_item.get("window"),
                        containers_metrics_list,
                        ResourceValues(pod_total_cpu, pod_total_memory),
                    )
                )
            if pod_name and not all_pods_in_namespace and results:
                return results[0]
            return results
        except ApiException as e:
            api_path_log = (
                f"{METRICS_API_GROUP}/{METRICS_API_VERSION}/namespaces/{namespace}/pods"
            )
            if pod_name and not all_pods_in_namespace:
                api_path_log += f"/{pod_name}"
            if e.status == 404:
                logger.error(f"Metrics Server API ({api_path_log}) 未找到或无数据。")
            else:
                logger.error(f"获取 Pod 指标失败: {e.status} - {e.reason} - {e.body}")
            return None

    def list_services(
        self,
        namespace: Optional[str] = None,
        all_namespaces: bool = False,
        label_selector: Optional[str] = None,
        field_selector: Optional[str] = None,
        simplify: bool = True,
    ) -> Optional[List[Union[ServiceInfo, client.V1Service]]]:
        """
        列出集群中的 Service 对象。
        """
        try:
            if all_namespaces:
                service_list_obj = self.core_v1_api.list_service_for_all_namespaces(
                    label_selector=label_selector, field_selector=field_selector
                )
            else:
                current_namespace = namespace
                if not current_namespace:
                    try:
                        _, active_context = config.list_kube_config_contexts()
                        current_namespace = active_context.get("context", {}).get(
                            "namespace", "default"
                        )
                    except config.ConfigException:
                        current_namespace = "default"
                        logger.info(
                            "无法从 kubeconfig 获取当前命名空间，使用 'default'。"
                        )
                service_list_obj = self.core_v1_api.list_namespaced_service(
                    namespace=current_namespace,
                    label_selector=label_selector,
                    field_selector=field_selector,
                )
            if simplify:
                return [
                    self._simplify_service_info(svc) for svc in service_list_obj.items
                ]
            return service_list_obj.items
        except ApiException as e:
            logger.error(f"列出 Service 失败: {e.status} - {e.reason}")
            return None

    def get_service_type_defined_limit_occupation(
        self, service_label_key: str = "service-type"
    ) -> Optional[Dict[str, ServiceTypeDefinedLimitOccupationReport]]:
        """
        计算不同Pod类型基于 (Pod数量 * 为该类型定义的单一Pod Limit) 的“占用”资源。

        Args:
            service_label_key (str): 用于标识服务类型的 Pod 标签键。

        Returns:
            Optional[Dict[str, ServiceTypeDefinedLimitOccupationReport]]: 键是服务类型标签值，
                值为 ServiceTypeDefinedLimitOccupationReport 实例。API 错误时返回 None。
        """
        occupation_map: Dict[str, ServiceTypeDefinedLimitOccupationReport] = {}
        # 用于存储每种类型Pod的列表，以便后续获取第一个Pod作为样本
        pods_by_type: Dict[str, List[client.V1Pod]] = {}

        try:
            all_pods_list = self.core_v1_api.list_pod_for_all_namespaces(
                label_selector=service_label_key
            )
            if not all_pods_list:
                return {}

            for pod in all_pods_list.items:
                if pod.metadata.labels and service_label_key in pod.metadata.labels:
                    service_type_value = pod.metadata.labels[service_label_key]
                    if service_type_value not in pods_by_type:
                        pods_by_type[service_type_value] = []
                    pods_by_type[service_type_value].append(pod)

            for service_type, pod_list_for_type in pods_by_type.items():
                if not pod_list_for_type:
                    continue

                pod_count = len(pod_list_for_type)
                sample_pod = pod_list_for_type[
                    0
                ]  # 取第一个Pod作为样本来确定该类型的Limit

                defined_cpu_limit = 0
                defined_memory_limit = 0
                defined_gpu_limit = 0

                has_undefined_cpu = True
                has_undefined_memory = True
                has_undefined_gpu = True  # 假设GPU limit也是期望的

                # 计算样本Pod的limits总和 (所有容器)
                for container in sample_pod.spec.containers:
                    if container.resources and container.resources.limits:
                        limits = container.resources.limits
                        cpu_val = limits.get("cpu")
                        if cpu_val:
                            defined_cpu_limit += parse_cpu_to_millicores(cpu_val)
                            has_undefined_cpu = False

                        mem_val = limits.get("memory")
                        if mem_val:
                            defined_memory_limit += parse_memory_to_bytes(mem_val)
                            has_undefined_memory = False

                        gpu_val = limits.get(self.gpu_resource_name)
                        if gpu_val:
                            defined_gpu_limit += int(gpu_val)
                            has_undefined_gpu = False
                    elif container.resources:  # 有resources块但没有limits块
                        pass  # 保持 undefined 状态
                    # else: 没有resources块，此容器对limit贡献为0

                # 如果样本Pod完全没有定义任何容器的任何一个资源的limit，则该资源的defined_limit为0
                # has_undefined_* 标记的是样本Pod是否对该资源有明确的limit定义

                defined_limit_for_type = DefinedLimitForPodType(
                    cpu_limit_millicores=defined_cpu_limit,
                    memory_limit_bytes=defined_memory_limit,
                    gpu_limit_count=defined_gpu_limit,
                    source_pod_name=f"{sample_pod.metadata.namespace}/{sample_pod.metadata.name}",
                    has_undefined_cpu_limit=has_undefined_cpu,
                    has_undefined_memory_limit=has_undefined_memory,
                    has_undefined_gpu_limit=has_undefined_gpu,
                )

                calculated_occupation = ResourceValues(
                    cpu_millicores=pod_count * defined_cpu_limit,
                    memory_bytes=pod_count * defined_memory_limit,
                    gpu_count=pod_count * defined_gpu_limit,
                )

                occupation_map[service_type] = ServiceTypeDefinedLimitOccupationReport(
                    service_type_label_value=service_type,
                    defined_limit_per_pod=defined_limit_for_type,
                    pod_count=pod_count,
                    calculated_occupation=calculated_occupation,
                )
            return occupation_map
        except ApiException as e:
            logger.error(
                f"获取服务类型 Defined Limit Occupation 失败: {e.status} - {e.reason}"
            )
            return None

    def get_service_type_defined_limit_occupation_percentage(
        self, service_label_key: str, service_label_value: str
    ) -> Optional[ServiceTypeDefinedLimitOccupationPercentage]:
        """
        计算特定服务类型基于 (Pod数量 * 为该类型定义的单一Limit值) 的“占用”占集群总可分配资源的百分比。
        """
        cluster_alloc = self.get_cluster_allocatable_resources()
        if not cluster_alloc:
            logger.error(
                "无法获取集群总可分配资源以计算基于 Defined Limit Occupation 的百分比。"
            )
            return None

        all_occupations = self.get_service_type_defined_limit_occupation(
            service_label_key=service_label_key
        )

        # 默认值，以防找不到特定服务类型的数据
        occupation_for_percentage = ResourceValues(0, 0, 0)
        defined_limit_for_type_default = DefinedLimitForPodType()
        pod_count_for_calc = 0

        if all_occupations is None:
            logger.warning(
                f"无法获取服务类型 Defined Limit Occupation (标签键: {service_label_key}) 以计算百分比。"
            )
        else:
            specific_report = all_occupations.get(service_label_value)
            if not specific_report:
                logger.info(
                    f"未找到服务类型 '{service_label_value}' (标签键: {service_label_key}) 的 Defined Limit Occupation 数据。"
                )
            else:
                occupation_for_percentage = specific_report.calculated_occupation
                defined_limit_for_type_default = specific_report.defined_limit_per_pod
                pod_count_for_calc = specific_report.pod_count

        cpu_p = (
            (
                occupation_for_percentage.cpu_millicores
                / cluster_alloc.cpu_millicores
                * 100.0
            )
            if cluster_alloc.cpu_millicores > 0
            else (-1.0 if occupation_for_percentage.cpu_millicores > 0 else 0.0)
        )
        mem_p = (
            (
                occupation_for_percentage.memory_bytes
                / cluster_alloc.memory_bytes
                * 100.0
            )
            if cluster_alloc.memory_bytes > 0
            else (-1.0 if occupation_for_percentage.memory_bytes > 0 else 0.0)
        )
        gpu_p = (
            (occupation_for_percentage.gpu_count / cluster_alloc.gpu_count * 100.0)
            if cluster_alloc.gpu_count > 0 and occupation_for_percentage.gpu_count > 0
            else (0.0 if occupation_for_percentage.gpu_count == 0 else -1.0)
        )

        return ServiceTypeDefinedLimitOccupationPercentage(
            service_type_label_value=service_label_value,
            cpu_occupation_percentage=cpu_p,
            memory_occupation_percentage=mem_p,
            gpu_occupation_percentage=gpu_p,
            occupation_based_on_defined_limit=occupation_for_percentage,
            defined_limit_per_pod_for_type=defined_limit_for_type_default,
            pod_count_for_occupation_calc=pod_count_for_calc,
            cluster_total_allocatable=cluster_alloc,
        )


if __name__ == "__main__":
    logger.info("正在初始化 KubernetesResourceMonitor...")
    try:
        monitor = KubernetesResourceMonitor()
        logger.info("KRM 初始化成功。\n")

        scheduler_advisor = KubernetesSchedulerAdvisor()
        logger.info("KSA 初始化成功。\n")

        # 1. 获取集群总可分配资源
        print("\n=== 1. 集群总可分配资源 ===")
        cluster_res = monitor.get_cluster_allocatable_resources()
        if cluster_res:
            print(f"  总 CPU (毫核): {cluster_res.cpu_millicores}")
            print(f"  总内存: {format_memory_bytes(cluster_res.memory_bytes)}")
            print(f"  总 GPU: {cluster_res.gpu_count}")
        else:
            print("  无法获取集群资源。")

        # 2. 列出所有节点 (简化信息)
        print("\n=== 2. 节点列表 (简化信息) ===")
        all_nodes_info = monitor.list_nodes(simplify=True)  # List[NodeInfo]
        if all_nodes_info:
            for node in all_nodes_info[:2]:
                print(
                    f"  节点: {node.name}, 状态: {node.status}, IP: {node.internal_ip}"
                )
            if len(all_nodes_info) > 2:
                print(f"  ... 以及其他 {len(all_nodes_info) - 2} 个节点")
        else:
            print("  无法列出节点或没有节点。")

        # 3. 获取所有节点的资源指标
        print("\n=== 3. 所有节点资源指标 ===")
        all_node_metrics_reports = monitor.get_node_metrics(
            include_allocatable=True
        )  # List[NodeMetricsReport]
        if all_node_metrics_reports and isinstance(all_node_metrics_reports, list):
            for metrics in all_node_metrics_reports[:2]:
                print(f"  节点: {metrics.node_name}")
                print(
                    f"    使用 CPU: {metrics.usage.cpu_millicores}m, 内存: {format_memory_bytes(metrics.usage.memory_bytes)}"
                )
                print(
                    f"    可用 CPU: {metrics.available.cpu_millicores}m, 内存: {format_memory_bytes(metrics.available.memory_bytes)}"
                )
            if len(all_node_metrics_reports) > 2:
                print(
                    f"  ... 以及其他 {len(all_node_metrics_reports) - 2} 个节点的指标"
                )
        else:
            print("  无法获取节点指标。")

        # 4. 定义 Pod 需求
        print(f"\n=== 4. 定义 Pod 资源需求进行调度预检查 ===")
        small_pod_req = PodRequirements(
            cpu_millicores=100,
            memory_bytes=100 * 1024 * 1024,
            gpu_count=0,
            node_selector={"kubernetes.io/os": "linux"},  # 调整为更通用的标签
        )
        print(
            f"  测试 Pod 需求: CPU {small_pod_req.cpu_millicores}m, Mem {format_memory_bytes(small_pod_req.memory_bytes)}"
        )

        # 5. 检查特定节点的可调度性 (需要确保 all_nodes_info 和 all_node_metrics_reports 都有效)
        if (
            all_nodes_info
            and all_node_metrics_reports
            and isinstance(all_nodes_info, list)
            and len(all_nodes_info) > 0
            and isinstance(all_node_metrics_reports, list)
            and len(all_node_metrics_reports) > 0
        ):

            target_node_name_check = all_nodes_info[0].name
            target_node_info_check = all_nodes_info[0]
            target_node_metrics_check = next(
                (
                    m
                    for m in all_node_metrics_reports
                    if m.node_name == target_node_name_check
                ),
                None,
            )

            if target_node_metrics_check and target_node_info_check:
                print(
                    f"\n=== 5. 检查 Pod 在节点 '{target_node_name_check}' 上的可调度性 ==="
                )
                sched_info_on_node = scheduler_advisor.check_pod_schedulability_on_node(
                    target_node_metrics_check, target_node_info_check, small_pod_req
                )
                print(
                    f"  节点 '{sched_info_on_node.node_name}' 是否可调度: {sched_info_on_node.is_schedulable}"
                )
                if not sched_info_on_node.is_schedulable:
                    for reason in sched_info_on_node.reasons:
                        print(f"    - 原因: {reason}")
            else:
                print(
                    f"  无法为节点 '{target_node_name_check}' 找到匹配的 NodeInfo 和 NodeMetricsReport 进行检查。"
                )
        else:
            print("  没有足够的节点信息或指标来进行特定节点调度检查。")

        # 6. 查找所有可调度节点
        print(f"\n=== 6. 查找集群中所有可调度此 Pod 的节点 ===")
        if (
            all_node_metrics_reports
            and all_nodes_info
            and isinstance(all_node_metrics_reports, list)
            and isinstance(all_nodes_info, list)
        ):
            all_sched_evals = scheduler_advisor.find_schedulable_nodes(
                all_node_metrics_reports, all_nodes_info, small_pod_req
            )
            suitable_node_names = [
                info.node_name for info in all_sched_evals if info.is_schedulable
            ]
            if suitable_node_names:
                print(f"  以下节点可以调度此 Pod: {', '.join(suitable_node_names)}")
            else:
                print("  集群中没有节点可以调度此 Pod。")
            for info in all_sched_evals:
                if not info.is_schedulable:
                    print(
                        f"  节点 '{info.node_name}' 不可调度，原因: {'; '.join(info.reasons)}"
                    )
        else:
            print("  无法获取足够的节点信息和指标来查找可调度节点。")

        # 7. 查找最优节点
        print(f"\n=== 7. 为 Pod 寻找最优节点 (策略: least_loaded_cpu) ===")
        if (
            all_node_metrics_reports
            and all_nodes_info
            and isinstance(all_node_metrics_reports, list)
            and isinstance(all_nodes_info, list)
        ):

            # 准备数据给 find_optimal_node_for_pod
            # a. 先获取所有节点的评估结果
            all_evals_for_optimal = scheduler_advisor.find_schedulable_nodes(
                all_node_metrics_reports, all_nodes_info, small_pod_req
            )
            # b. 筛选出真正可调度的评估结果
            schedulable_evals_for_optimal = [
                ev for ev in all_evals_for_optimal if ev.is_schedulable
            ]
            # c. 创建 node_metrics_map
            node_metrics_map_for_optimal = {
                metrics_report.node_name: metrics_report
                for metrics_report in all_node_metrics_reports
            }

            if schedulable_evals_for_optimal:
                optimal_choice_cpu = scheduler_advisor.find_optimal_node_for_pod(
                    schedulable_evals_for_optimal,
                    node_metrics_map_for_optimal,
                    small_pod_req,
                    strategy="least_loaded_cpu",
                )
                if optimal_choice_cpu:
                    print(
                        f"  最优节点 (least_loaded_cpu): {optimal_choice_cpu.node_name} (Score: {optimal_choice_cpu.score})"
                    )
                    print(
                        f"    节点部署前可用: CPU {optimal_choice_cpu.details.available.cpu_millicores}m"
                    )
                else:
                    print(f"  无法为 Pod 找到满足条件的最优节点 (least_loaded_cpu)。")
            else:
                print("  没有可调度节点，无法寻找最优节点。")
        else:
            print("  无法获取足够的节点信息和指标来寻找最优节点。")

    except RuntimeError as e:
        logger.critical(f"运行时错误: {e}", exc_info=True)
    except Exception as e:
        logger.critical(f"发生未知主流程错误: {e}", exc_info=True)
