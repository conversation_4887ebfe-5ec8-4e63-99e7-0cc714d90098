# MQ to K8s 任务调度程序配置文件

# 系统配置
system:
  # 是否为内网环境
  internal_network: "false"
  # 运行模式: online, local, debug
  runtime_mode: "local"
  # 日志级别: DEBUG, INFO, WARNING, ERROR
  log_level: "INFO"

# MQ配置 - 外网
mq:
  host: "*************"
  port: 56720
  vhost: "/"
  credentials_user: "huoshan"
  credentials_password: "<PERSON><PERSON><PERSON>@2024"
  mq_queue: "common_sim_engine"

# MQ配置 - 内网
mq_internal:
  host: "**********"
  port: 5672
  vhost: "/"
  credentials_user: "admin"
  credentials_password: "admin"
  mq_queue: "common_sim_engine"

# K8s配置
kubernetes:
  # 命名空间
  namespace: "default"
  # Pod配置模板
  pod_template:
    # 镜像
    image: "mogohub.tencentcloudcr.com/sim/logsim:controller_latest"
    # 资源限制
    resources:
      limits:
        memory: "2Gi"
        cpu: "1"
      requests:
        memory: "1Gi"
        cpu: "0.5"
    # 命令
    command: ["/bin/bash", "-c", "/opt/run/run_controller.sh"]
    # 环境变量
    env:
      # 固定环境变量
      - name: "TZ"
        value: "Asia/Shanghai"
  
  # K8s API配置，如果使用kubectl，可以不配置
  api:
    enabled: false
    host: "https://kubernetes.default.svc"
    token: ""
    ca_cert: "" 