import json
import threading
import time
from dataclasses import dataclass
from typing import Dict, Any, List, Optional
import os
from queue import Queue

from utils.log import logger
from utils.config import get_value
from utils.mq_client import MQClient

@dataclass
class TaskMessage:
    """任务消息数据结构"""
    task_id: str
    ads_list: List[Dict[str, Any]]
    raw_data: Dict[str, Any]

class TaskProcessor:
    """任务处理器，处理MQ消息并启动K8s Pod"""
    
    def __init__(self):
        """初始化任务处理器"""
        # 配置MQ连接
        section = 'mq'
        if get_value("system", "internal_network") == "true":
            section = 'mq_internal'
            
        self.mq_client = MQClient(
            get_value(section, "host"),
            get_value(section, "port"),
            get_value(section, "vhost"),
            (get_value(section, "credentials_user"), get_value(section, "credentials_password")),
            get_value(section, "mq_queue")
        )
        
        # 任务处理相关
        self.tasks = {}
        self.tasks_lock = threading.Lock()
        self.running = False
        
        # 优先级队列 (0-4)，4为最高优先级，0为最低优先级
        self.priority_queues = {i: {} for i in range(5)}
        self.priority_queues_lock = threading.Lock()
        
    def start(self):
        """启动处理器"""
        logger.info("启动任务处理器")
        self.running = True
        
        # 启动MQ消费
        self.mq_client.start_consuming(self._mq_callback)
        
        # 启动任务状态检查
        self._start_status_checker()
        
        # 启动队列状态打印
        self._start_queue_printer()
        
        # 启动调度器
        self._start_scheduler()
        
    def stop(self):
        """停止处理器"""
        logger.info("停止任务处理器")
        self.running = False
        self.mq_client.stop_consuming()
        
    def _mq_callback(self, ch, method, properties, body):
        """MQ消息回调"""
        try:
            message = json.loads(body.decode())
            print("原始mq")
            print(message)
            task = self._parse_message(message)
            
            # 确认消息已处理
            self.mq_client.ack_message(method.delivery_tag)
                
        except Exception as e:
            logger.error(f"消息处理失败: {str(e)}")
            logger.print_exc()
            self.mq_client.nack_message(method.delivery_tag)
            
    def _parse_message(self, message: Dict) -> TaskMessage:
        """解析MQ消息，只提取关键信息并合并相同simTaskId的任务"""
        # 提取基本信息
        sim_task_id = message.get("simTaskId")
        sim_sub_task_id = message.get("simSubTaskId")
        sim_type = message.get("simType")
        vehicle_type = message.get("vehicleType")
        code_image = message.get("codeImage")
        priority = message.get("priority", 1)  # 默认优先级为1
        wrapper_list = message.get("wrapperSimAdsSubmitParamList", [])
        
        with self.priority_queues_lock:
            # 检查是否已存在相同simTaskId的任务
            if sim_task_id in self.priority_queues[priority]:
                # 存在则合并
                queue_task = self.priority_queues[priority][sim_task_id]
                # 合并wrapperSimAdsSubmitParamList
                queue_task["wrapperSimAdsSubmitParamList"]["items"].extend(wrapper_list)
                queue_task["wrapperSimAdsSubmitParamList"]["count"] += len(wrapper_list)
                
                # 打印更新信息
                logger.info("--------------------------------")
                logger.info(f"更新任务，当前信息：\n{json.dumps(queue_task, indent=2, ensure_ascii=False)}")
            else:
                # 不存在则创建新任务
                new_task = {
                    "simTaskId": sim_task_id,
                    "simType": sim_type,
                    "vehicleType": vehicle_type,
                    "codeImage": code_image,
                    "priority": priority,
                    "wrapperSimAdsSubmitParamList": {
                        "count": len(wrapper_list),
                        "items": wrapper_list
                    }
                }
                
                # 添加到对应优先级队列
                self.priority_queues[priority][sim_task_id] = new_task
                
                # 打印新增信息
                logger.info("--------------------------------")
                logger.info(f"新增任务，任务信息：\n{json.dumps(new_task, indent=2, ensure_ascii=False)}")
        
        return TaskMessage(
            task_id=sim_sub_task_id,
            ads_list=wrapper_list,
            raw_data=message
        )
    
    def _launch_k8s_pod(self, task: TaskMessage, cluster_name: str = None) -> bool:
        """启动K8s Pod，通过外部K8s配置
        
        Args:
            task: 任务消息
            cluster_name: 目标集群名称
            
        Returns:
            bool: 启动成功返回True，否则返回False
        """
        try:
            # 获取任务类型和ID
            sim_type = task.raw_data.get("simType", "")
            task_id = task.task_id
            
            logger.info(f"准备在集群 {cluster_name} 启动任务 {task_id}，类型: {sim_type}")
            
            # 根据任务类型获取相应的配置文件路径
            config_paths = {
                "pnc_worldsim": "templates/pnc_worldsim.yaml",
                "pnc_logsim": "templates/pnc_logsim.yaml", 
                "pdp_logsim": "templates/pdp_logsim.yaml",
                "percp_logsim": "templates/percp_logsim.yaml"
            }
            
            config_path = config_paths.get(sim_type)
            if not config_path:
                logger.error(f"未知的任务类型: {sim_type}")
                return False
                
            # 获取当前工作目录的绝对路径
            base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            full_config_path = os.path.join(base_dir, "mqtok8s", config_path)
            
            # 创建任务配置JSON文件
            task_config = {
                "simTaskId": task.raw_data.get("simTaskId"),
                "simType": task.raw_data.get("simType"),
                "vehicleType": task.raw_data.get("vehicleType"),
                "codeImage": task.raw_data.get("codeImage"),
                "priority": task.raw_data.get("priority", 0),
                "wrapperSimAdsSubmitParamList": task.raw_data.get("wrapperSimAdsSubmitParamList", [])
            }
            
            # 将JSON文件保存到临时目录
            task_json_path = f"/tmp/task_config_{task_id}.json"
            with open(task_json_path, 'w', encoding='utf-8') as f:
                json.dump(task_config, f, ensure_ascii=False, indent=2)
            
            logger.info(f"任务配置文件已保存到临时目录: {task_json_path}")
            
            # 读取原始配置文件
            with open(full_config_path, 'r') as f:
                config_content = f.read()
                
            # 替换变量
            config_content = config_content.replace("${TASK_ID}", task_id)
            config_content = config_content.replace("${VEHICLE_TYPE}", task.raw_data.get("vehicleType", "car"))
            config_content = config_content.replace("${CODE_IMAGE}", task.raw_data.get("codeImage", "mogohub.tencentcloudcr.com/sim/logsim:controller_latest"))
            
            # 生成临时配置文件
            temp_config_path = f"/tmp/sim_task_{task_id}.yaml"
            with open(temp_config_path, 'w') as f:
                f.write(config_content)
            
            logger.info(f"生成临时配置文件: {temp_config_path}")
            
            # 创建ConfigMap，只包含任务配置JSON
            cm_name = f"task-data-{task_id}"
            cm_cmd = f"kubectl create configmap {cm_name} --from-file=tasks_info.json={task_json_path}"
            if cluster_name:
                cm_cmd += f" --context={cluster_name}"
            
            logger.info(f"创建ConfigMap: {cm_cmd}")
            # 实际部署时取消注释下行
            # cm_result = os.system(cm_cmd)
            # if cm_result != 0:
            #     logger.error(f"创建ConfigMap失败: {cm_name}")
            #     return False
            
            # 构建命令，指定目标集群
            cmd = f"kubectl apply -f {temp_config_path}"
            if cluster_name:
                cmd += f" --context={cluster_name}"
                
            logger.info(f"执行命令: {cmd}")
            # 实际部署时取消注释下行
            # result = os.system(cmd)
            
            # 模拟成功
            result = 0
            
            # 清理临时文件
            try:
                os.remove(task_json_path)
                os.remove(temp_config_path)
                logger.info(f"临时文件已清理: {task_json_path}, {temp_config_path}")
            except Exception as e:
                logger.warning(f"清理临时文件失败: {str(e)}")
            
            if result == 0:
                logger.info(f"成功启动任务: {task_id} 在集群 {cluster_name}")
                return True
            else:
                logger.error(f"启动任务失败: {task_id}, 错误码: {result}")
                return False
                
        except Exception as e:
            logger.error(f"启动K8s Pod失败: {str(e)}")
            logger.print_exc()
            return False
    
    def _start_status_checker(self):
        """启动状态检查线程"""
        def check_status():
            while self.running:
                try:
                    self._check_tasks_status()
                except Exception as e:
                    logger.error(f"检查任务状态失败: {str(e)}")
                    logger.print_exc()
                
                # 每60秒检查一次
                time.sleep(60)
        
        status_thread = threading.Thread(target=check_status, daemon=True)
        status_thread.start()
    
    def _check_tasks_status(self):
        """检查任务状态"""
        with self.tasks_lock:
            # 遍历所有任务
            for task_id, task_info in list(self.tasks.items()):
                # 检查运行中的任务
                if task_info["status"] == "running":
                    # TODO: 实现Pod状态检查逻辑
                    # 示例: 使用kubectl检查Pod状态
                    pod_name = f"sim-task-{task_id}"
                    cmd = f"kubectl get pod {pod_name} -o json"
                    
                    # 这里只是示例，实际实现需要解析kubectl的输出
                    # 如果Pod已完成或失败，更新任务状态
                    pass
                
                # 清理过期的已完成任务
                if task_info["status"] in ["completed", "failed"]:
                    if time.time() - task_info["create_time"] > 86400:  # 1天
                        logger.info(f"清理过期任务: {task_id}")
                        del self.tasks[task_id]
    
    def _start_queue_printer(self):
        """启动队列状态打印线程"""
        def print_queues():
            last_print_time = 0
            while self.running:
                try:
                    # 获取当前时间
                    current_time = time.time()
                    
                    # 判断是否需要打印队列状态
                    if current_time - last_print_time >= 5:  # 每5秒打印一次
                        self._print_queue_status()
                        last_print_time = current_time
                except Exception as e:
                    logger.error(f"打印队列状态失败: {str(e)}")
                    logger.print_exc()
                
                # 休眠100毫秒
                time.sleep(0.1)
        
        printer_thread = threading.Thread(target=print_queues, daemon=True)
        printer_thread.start()
    
    def _print_queue_status(self):
        """打印队列状态"""
        with self.priority_queues_lock:
            logger.info("==== 当前队列状态 ====")
            # 从高优先级到低优先级打印
            for priority in range(4, -1, -1):
                queue = self.priority_queues[priority]
                task_count = len(queue)
                
                if task_count > 0:
                    logger.info(f"优先级 {priority} 队列: 共 {task_count} 个任务")
                    for sim_task_id, task in queue.items():
                        # 创建一个不包含items的副本用于打印
                        print_task = task.copy()
                        # 保留count但删除items
                        wrapper_info = print_task["wrapperSimAdsSubmitParamList"].copy()
                        wrapper_info.pop("items", None)
                        print_task["wrapperSimAdsSubmitParamList"] = wrapper_info
                        
                        logger.info(f"  - {json.dumps(print_task, ensure_ascii=False)}")
                else:
                    logger.info(f"优先级 {priority} 队列: 空")
            logger.info("==== 队列状态结束 ====")
            
    def _start_scheduler(self):
        """启动调度器线程"""
        def scheduler_loop():
            last_schedule_time = 0
            while self.running:
                try:
                    # 获取当前时间
                    current_time = time.time()
                    
                    # 判断是否需要执行调度
                    if current_time - last_schedule_time >= 30:  # 每30秒调度一次
                        self._schedule_tasks()
                        last_schedule_time = current_time
                except Exception as e:
                    logger.error(f"调度任务失败: {str(e)}")
                    logger.print_exc()
                
                # 休眠1秒
                time.sleep(1)
        
        scheduler_thread = threading.Thread(target=scheduler_loop, daemon=True)
        scheduler_thread.start()
        logger.info("调度器已启动")
    
    def _get_cluster_resources(self):
        """获取集群资源情况
        
        Returns:
            Dict: 集群资源情况，格式为 {cluster_name: {capacity, available}}
        """
        # 当前支持的集群列表
        return {
            "local_arm_ROS1": {"capacity": 5, "available": 5, "type": "arm"},
            "local_arm_ROS2": {"capacity": 5, "available": 5, "type": "arm"},
            "huoshan_x86": {"capacity": 20, "available": 20, "type": "x86"},
            "huoshan_arm": {"capacity": 10, "available": 10, "type": "arm"}
        }
    
    def _schedule_tasks(self):
        """根据优先级调度任务，当前全部调度到huoshan_x86集群"""
        logger.info("开始任务调度...")
        
        # 任务分配计数
        scheduled_count = 0
        
        # 指定目标集群
        target_cluster = "huoshan_x86"
        
        with self.priority_queues_lock:
            # 按优先级从高到低处理任务队列
            for priority in range(4, -1, -1):
                queue = self.priority_queues[priority]
                if not queue:
                    continue
                
                logger.info(f"调度优先级 {priority} 队列，共 {len(queue)} 个任务")
                
                # 处理当前优先级的任务
                for sim_task_id, task in list(queue.items()):
                    # 获取任务类型
                    sim_type = task.get("simType", "")
                    
                    # 创建任务消息
                    task_message = TaskMessage(
                        task_id=sim_task_id,
                        ads_list=task["wrapperSimAdsSubmitParamList"]["items"],
                        raw_data=task
                    )
                    
                    # 启动Pod
                    logger.info(f"准备调度任务 {sim_task_id}，类型: {sim_type}，到集群: {target_cluster}")
                    
                    # 调用实际的Pod启动方法
                    success = self._launch_k8s_pod(task_message, target_cluster)
                    
                    if success:
                        # 更新调度计数
                        scheduled_count += 1
                        
                        # 从队列中移除已调度的任务
                        del queue[sim_task_id]
                        
                        logger.info(f"任务 {sim_task_id} 已调度到集群 {target_cluster}")
                    else:
                        logger.error(f"任务 {sim_task_id} 调度失败")
        
        logger.info(f"调度完成，共调度 {scheduled_count} 个任务") 