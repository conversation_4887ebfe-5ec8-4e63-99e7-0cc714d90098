#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
MQ to K8s 任务调度程序
接收MQ消息，启动K8s Pod执行任务
"""

import os
import sys
import time
import signal
import argparse

# 将当前目录添加到系统路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from mqtok8s.task_processor import TaskProcessor
from mqtok8s.utils.log import logger
from mqtok8s.utils.config import get_value, load_config

# 全局变量
processor = None
running = True

def signal_handler(sig, frame):
    """处理终止信号"""
    global running
    logger.info("接收到终止信号，正在关闭程序...")
    running = False
    
    if processor:
        processor.stop()
        
    sys.exit(0)

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='MQ to K8s 任务调度程序')
    parser.add_argument('--config', help='配置文件路径', default='config.yaml')
    return parser.parse_args()

def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()
    
    # 加载配置
    config = load_config(args.config)
    
    # 注册信号处理
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 创建并启动任务处理器
    global processor
    processor = TaskProcessor()
    processor.start()
    
    # 保持程序运行
    logger.info("MQ to K8s 任务调度程序已启动")
    
    try:
        while running:
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
        if processor:
            processor.stop()
    
    logger.info("程序正常退出")

if __name__ == "__main__":
    main() 