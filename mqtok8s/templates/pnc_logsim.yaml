apiVersion: v1
kind: Pod
metadata:
  name: pnc-logsim-${TASK_ID}
  labels:
    app: simulation
    type: pnc_logsim
    task-id: ${TASK_ID}
spec:
  shareProcessNamespace: true
  containers:
  - name: controller
    image: mogohub.tencentcloudcr.com/sim/logsim:controller
    imagePullPolicy: IfNotPresent
    env:
    - name: TASK_ID
      value: "${TASK_ID}"
    - name: SIM_TYPE
      value: "pnc_logsim"
    - name: VEHICLE_TYPE
      value: "${VEHICLE_TYPE}"
    - name: ROS_LOCALHOST_ONLY
      value: "1"
    resources:
      limits:
        memory: "4Gi"
        cpu: "2"
      requests:
        memory: "2Gi"
        cpu: "1"
    volumeMounts:
    - name: workspace-volume
      mountPath: /home/<USER>/workspace
    - name: task-data
      mountPath: /home/<USER>/workspace/tasks_info.json
      subPath: tasks_info.json
    command: ["/bin/bash", "-c"]
    args: ["cd /home/<USER>/opt/run/run_controller.sh"]
  
  - name: engine
    image: mogohub.tencentcloudcr.com/sim/logsim:engine
    imagePullPolicy: IfNotPresent
    env:
    - name: TASK_ID
      value: "${TASK_ID}"
    - name: SIM_TYPE
      value: "pnc_logsim"
    - name: ROS_LOCALHOST_ONLY
      value: "1"
    - name: NVIDIA_DRIVER_CAPABILITIES
      value: "all"
    securityContext:
      privileged: true
    resources:
      limits:
        nvidia.com/gpu: 1
        memory: "8Gi"
        cpu: "4"
      requests:
        memory: "4Gi"
        cpu: "2"
    volumeMounts:
    - name: workspace-volume
      mountPath: /home/<USER>/workspace
    command: ["/bin/bash", "-c"]
    args: ["cd /home/<USER>/usr/local/bin/start_engine.sh"]
  
  - name: algo
    image: ${CODE_IMAGE}
    imagePullPolicy: IfNotPresent
    env:
    - name: TASK_ID
      value: "${TASK_ID}"
    - name: SIM_TYPE
      value: "pnc_logsim"
    - name: VEHICLE_TYPE
      value: "${VEHICLE_TYPE}"
    - name: ROS_LOCALHOST_ONLY
      value: "1"
    - name: NVIDIA_DRIVER_CAPABILITIES
      value: "all"
    resources:
      limits:
        nvidia.com/gpu: 1
        memory: "12Gi"
        cpu: "6"
      requests:
        memory: "6Gi"
        cpu: "3"
    volumeMounts:
    - name: workspace-volume
      mountPath: /home/<USER>/workspace
    command: ["/bin/bash", "-c"]
    args: ["cd /home/<USER>/usr/local/bin/start_algo.sh"]
  
  - name: validator
    image: mogohub.tencentcloudcr.com/sim/logsim:validator
    imagePullPolicy: IfNotPresent
    env:
    - name: TASK_ID
      value: "${TASK_ID}"
    - name: SIM_TYPE
      value: "pnc_logsim"
    - name: ROS_LOCALHOST_ONLY
      value: "1"
    securityContext:
      privileged: true
    resources:
      limits:
        memory: "4Gi"
        cpu: "2"
      requests:
        memory: "2Gi"
        cpu: "1"
    volumeMounts:
    - name: workspace-volume
      mountPath: /home/<USER>/workspace
    command: ["/bin/bash", "-c"]
    args: ["cd /home/<USER>/usr/local/bin/start_validator.sh"]
  
  volumes:
  - name: workspace-volume
    emptyDir: {}
  - name: task-data
    configMap:
      name: task-data-${TASK_ID}
  restartPolicy: Never 