# MQ to K8s 任务调度程序

这个程序用于接收RabbitMQ消息，然后启动Kubernetes Pod执行仿真任务。

## 功能特点

- 从RabbitMQ接收任务消息
- 根据消息内容生成Kubernetes Pod配置
- 启动Kubernetes Pod执行任务
- 监控任务执行状态
- 支持内外网环境配置

## 依赖

- Python 3.8+
- pika (RabbitMQ客户端)
- PyYAML
- kubernetes (可选，如果不使用kubectl)

## 安装

1. 安装依赖:

```bash
pip install pika pyyaml kubernetes
```

2. 配置:

编辑`config.yaml`文件，根据你的环境配置MQ和Kubernetes参数。

## 使用方法

### 直接运行

```bash
python main.py
```

或者指定配置文件:

```bash
python main.py --config /path/to/config.yaml
```

### Docker运行

构建Docker镜像:

```bash
docker build -t mqtok8s .
```

运行容器:

```bash
docker run -v /path/to/kubeconfig:/root/.kube/config mqtok8s
```

## 配置说明

配置文件`config.yaml`包含以下主要部分:

### 系统配置

```yaml
system:
  internal_network: "false"  # 是否为内网环境
  runtime_mode: "local"      # 运行模式: online, local, debug
  log_level: "INFO"          # 日志级别
```

### MQ配置

```yaml
mq:
  host: "mq.example.com"     # MQ服务器地址
  port: 5672                 # MQ端口
  vhost: "/"                 # 虚拟主机
  credentials_user: "guest"  # 用户名
  credentials_password: "guest"  # 密码
  mq_queue: "sim_task_queue"  # 队列名称
```

### K8s配置

```yaml
kubernetes:
  namespace: "default"       # 命名空间
  pod_template:              # Pod模板配置
    image: "controller:latest"  # 镜像
    resources:               # 资源限制
      limits:
        memory: "2Gi"
        cpu: "1"
      requests:
        memory: "1Gi"
        cpu: "0.5"
```

## 消息格式

程序接收的MQ消息格式应为JSON，包含以下字段:

```json
{
  "simSubTaskId": "12345",
  "wrapperSimAdsSubmitParamList": [
    {
      "adsId": "ads001"
    },
    {
      "adsId": "ads002"
    }
  ]
}
```

## 开发

### 目录结构

```
mqtok8s/
├── config.yaml             # 配置文件
├── main.py                 # 入口文件
├── task_processor.py       # 任务处理器
├── utils/                  # 工具类
│   ├── config.py           # 配置工具
│   ├── log.py              # 日志工具
│   └── mq_client.py        # MQ客户端
└── README.md               # 说明文档
```

## 问题排查

常见问题及解决方法:

1. MQ连接失败: 检查MQ配置和网络连接
2. K8s Pod启动失败: 检查K8s配置和权限设置
3. 消息格式错误: 确保消息符合预期格式

## 许可证

Copyright (C) Mogo.ai Information and Technology Co.Ltd 